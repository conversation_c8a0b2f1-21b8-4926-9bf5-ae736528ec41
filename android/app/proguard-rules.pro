# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.

# Keep PDF processing classes
-dontwarn com.gemalto.jp2.JP2Decoder
-dontwarn com.gemalto.**

# Keep PDF Box related classes
-keep class com.tom_roush.pdfbox.** { *; }
-dontwarn com.tom_roush.pdfbox.**

# Keep classes for read_pdf_text plugin
-keep class com.example.read_pdf_text.** { *; }

# Keep JAI (Java Advanced Imaging) related classes if present
-dontwarn javax.media.jai.**
-dontwarn java.awt.**

# Keep reflection-based classes
-keepattributes Signature
-keepattributes *Annotation*

# Don't warn about missing optional dependencies
-dontwarn org.apache.pdfbox.**
-dontwarn org.bouncycastle.**