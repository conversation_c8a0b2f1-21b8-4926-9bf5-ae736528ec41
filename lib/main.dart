import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';

import 'src/app_theme.dart';
import 'src/state/app_state.dart';
import 'src/screens/splash_screen.dart';
import 'src/services/network_permission_service.dart';
import 'src/services/network_status_service.dart';

import 'l10n/app_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化网络服务
  await NetworkPermissionService.instance.initialize();
  NetworkStatusService.instance.startMonitoring();
  
  final appState = AppState();
  await appState.initialize();
  runApp(AppProviders(appState: appState));
}

class AppProviders extends StatelessWidget {
  final AppState appState;
  const AppProviders({super.key, required this.appState});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => appState),
      ],
      child: Consumer<AppState>(
        builder: (context, state, _) {
          return MaterialApp(
            debugShowCheckedModeBanner: false,
            title: 'All Document Hub',
            theme: buildLightTheme(state.activeBrandTheme.brandColor),
            locale: state.selectedLocale,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: AppState.supportedLocales,
            home: const SplashScreen(),
          );
        },
      ),
    );
  }
}
