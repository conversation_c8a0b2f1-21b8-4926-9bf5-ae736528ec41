import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/signature.dart';
import '../services/signature_service.dart';
import '../state/app_state.dart';
import '../../l10n/app_localizations.dart';

class SignatureCreationScreen extends StatefulWidget {
  final Signature? existingSignature;
  
  const SignatureCreationScreen({super.key, this.existingSignature});

  @override
  State<SignatureCreationScreen> createState() => _SignatureCreationScreenState();
}

class _SignatureCreationScreenState extends State<SignatureCreationScreen> {
  final List<SignatureStroke> _strokes = [];
  final List<Offset> _currentStrokePoints = [];
  Size? _canvasSize;
  
  Color _selectedColor = Colors.black;
  double _selectedStrokeWidth = 3.0;
  bool _isErasing = false;
  bool _hasUnsavedChanges = false;

  

  @override
  void initState() {
    super.initState();
    if (widget.existingSignature != null) {
      _strokes.addAll(widget.existingSignature!.strokes);
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final state = context.watch<AppState>();
    final brand = state.activeBrandTheme;
    
    return PopScope(
      canPop: !_hasUnsavedChanges,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop && _hasUnsavedChanges) {
          final shouldPop = await _showDiscardDialog(context);
          if (shouldPop && context.mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: brand.brandColor,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          title: Text(widget.existingSignature != null ? l10n.editSignature : l10n.createSignature),
          actions: [
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _strokes.isEmpty ? null : () => _saveSignature(context),
              tooltip: l10n.saveSignature,
            ),
          ],
        ),
        body: Column(
          children: [
            Expanded(
              child: Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300, width: 2),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                ),
                child: LayoutBuilder(
                    builder: (context, constraints) {
                      _canvasSize = constraints.biggest;
                      return ClipRRect(
                        borderRadius: BorderRadius.circular(6),
                        child: GestureDetector(
                          onPanStart: _onPanStart,
                          onPanUpdate: _onPanUpdate,
                          onPanEnd: _onPanEnd,
                          child: CustomPaint(
                            painter: SignaturePainter(
                              strokes: _strokes,
                              currentStroke: _currentStrokePoints.isNotEmpty
                                  ? SignatureStroke(
                                      points: List.from(_currentStrokePoints),
                                      color: _isErasing ? Colors.white : _selectedColor,
                                      strokeWidth: _selectedStrokeWidth,
                                    )
                                  : null,
                              isErasing: _isErasing,
                            ),
                            size: Size.infinite,
                          ),
                        ),
                      );
                    }
                ),
              ),
            ),
            _buildToolbar(context),
          ],
        ),
      ),
    );
  }

  Widget _buildToolbar(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Text(l10n.color),
              const SizedBox(width: 8),
              _colorDot(Colors.red, _selectedColor == Colors.red && !_isErasing, () {
                setState(() {
                  _selectedColor = Colors.red;
                  _isErasing = false;
                });
              }),
              _colorDot(Colors.blue, _selectedColor == Colors.blue && !_isErasing, () {
                setState(() {
                  _selectedColor = Colors.blue;
                  _isErasing = false;
                });
              }),
              _colorDot(Colors.green, _selectedColor == Colors.green && !_isErasing, () {
                setState(() {
                  _selectedColor = Colors.green;
                  _isErasing = false;
                });
              }),
              _colorDot(Colors.black, _selectedColor == Colors.black && !_isErasing, () {
                setState(() {
                  _selectedColor = Colors.black;
                  _isErasing = false;
                });
              }),
              const Spacer(),
              IconButton(
                icon: Icon(
                  Icons.cleaning_services,
                  color: _isErasing ? Theme.of(context).colorScheme.primary : null,
                ),
                onPressed: () {
                  setState(() {
                    _isErasing = !_isErasing;
                  });
                },
                tooltip: l10n.eraser,
              ),
              IconButton(
                icon: const Icon(Icons.delete_outline),
                tooltip: l10n.delete,
                onPressed: _strokes.isEmpty
                    ? null
                    : () async {
                        final confirmed = await _showClearConfirmationDialog(context);
                        if (confirmed) {
                          _clearCanvas();
                        }
                      },
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(l10n.strokeWidth),
              const SizedBox(width: 8),
              Expanded(
                child: Slider(
                  min: 1,
                  max: 16,
                  value: _selectedStrokeWidth,
                  onChanged: (v) => setState(() => _selectedStrokeWidth = v),
                ),
              ),
              Text('${_selectedStrokeWidth.toStringAsFixed(0)}'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _colorDot(Color color, bool selected, VoidCallback onTap) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: 22,
          height: 22,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            border: Border.all(color: selected ? Theme.of(context).colorScheme.primary : Colors.grey.shade400, width: selected ? 2 : 1),
          ),
        ),
      ),
    );
  }

  void _onPanStart(DragStartDetails details) {
    final offset = details.localPosition;
    
    _currentStrokePoints.clear();
    _currentStrokePoints.add(offset);
    
    setState(() {
      _hasUnsavedChanges = true;
    });
  }

  void _onPanUpdate(DragUpdateDetails details) {
    final offset = details.localPosition;
    
    setState(() {
      _currentStrokePoints.add(offset);
    });
  }

  void _onPanEnd(DragEndDetails details) {
    if (_currentStrokePoints.length > 1) {
      if (_isErasing) {
        _handleErasing();
      } else {
        _strokes.add(SignatureStroke(
          points: List.from(_currentStrokePoints),
          color: _selectedColor,
          strokeWidth: _selectedStrokeWidth,
        ));
      }
    }
    
    setState(() {
      _currentStrokePoints.clear();
    });
  }

  void _handleErasing() {
    final eraserPath = _currentStrokePoints;
    _strokes.removeWhere((stroke) {
      return _strokeIntersectsPath(stroke.points, eraserPath, _selectedStrokeWidth * 2);
    });
  }

  bool _strokeIntersectsPath(List<Offset> strokePoints, List<Offset> eraserPath, double threshold) {
    for (final strokePoint in strokePoints) {
      for (final eraserPoint in eraserPath) {
        final distance = (strokePoint - eraserPoint).distance;
        if (distance < threshold) {
          return true;
        }
      }
    }
    return false;
  }

  void _clearCanvas() {
    setState(() {
      _strokes.clear();
      _currentStrokePoints.clear();
      _hasUnsavedChanges = false;
    });
  }

  Future<bool> _showDiscardDialog(BuildContext context) async {
    final l10n = AppLocalizations.of(context)!;
    
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.discardChangesTitle),
        content: Text(l10n.discardChangesContent),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(l10n.keepEditing),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(l10n.discard),
          ),
        ],
      ),
    );
    
    return result ?? false;
  }

  Future<bool> _showClearConfirmationDialog(BuildContext context) async {
    final l10n = AppLocalizations.of(context)!;
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.confirmClear),
        content: Text(l10n.confirmClearSignature),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(l10n.clear, style: TextStyle(color: Theme.of(context).colorScheme.error)),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  Future<void> _saveSignature(BuildContext context) async {
    final l10n = AppLocalizations.of(context)!;
    
    if (_strokes.isEmpty) return;

    if (_canvasSize == null || _canvasSize!.isEmpty) {
      if (kDebugMode) {
        print('[SignatureCreationScreen] Error: Canvas size is not available or empty.');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(l10n.pdfSaveFailed)),
        );
      }
      return;
    }

    try {
      final signatureService = SignatureService.instance;
      final signature = Signature(
        id: widget.existingSignature?.id ?? signatureService.generateId(),
        name: widget.existingSignature?.name ?? '${l10n.newSignature} ${DateTime.now().millisecondsSinceEpoch ~/ 1000}',
        strokes: List.from(_strokes),
        createdAt: widget.existingSignature?.createdAt ?? DateTime.now(),
        canvasSize: _canvasSize!,
      );

      await signatureService.saveSignature(signature);
      
      if (kDebugMode) {
        print('[SignatureCreationScreen] Signature saved: ${signature.id}');
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(widget.existingSignature != null ? l10n.signatureUpdated : l10n.signatureCreated)),
        );
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (kDebugMode) {
        print('[SignatureCreationScreen] Error saving signature: $e');
      }
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(l10n.pdfSaveFailed)),
        );
      }
    }
  }
}

class SignaturePainter extends CustomPainter {
  final List<SignatureStroke> strokes;
  final SignatureStroke? currentStroke;
  final bool isErasing;

  SignaturePainter({
    required this.strokes,
    this.currentStroke,
    this.isErasing = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    for (final stroke in strokes) {
      if (stroke.points.length < 2) continue;
      
      paint.color = stroke.color;
      paint.strokeWidth = stroke.strokeWidth;
      
      final path = Path();
      path.moveTo(stroke.points.first.dx, stroke.points.first.dy);
      
      for (int i = 1; i < stroke.points.length; i++) {
        path.lineTo(stroke.points[i].dx, stroke.points[i].dy);
      }
      
      canvas.drawPath(path, paint);
    }

    if (currentStroke != null && currentStroke!.points.length >= 2) {
      paint.color = isErasing ? Colors.red.withValues(alpha: 0.5) : currentStroke!.color;
      paint.strokeWidth = currentStroke!.strokeWidth;
      
      final path = Path();
      path.moveTo(currentStroke!.points.first.dx, currentStroke!.points.first.dy);
      
      for (int i = 1; i < currentStroke!.points.length; i++) {
        path.lineTo(currentStroke!.points[i].dx, currentStroke!.points[i].dy);
      }
      
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(SignaturePainter oldDelegate) {
    return strokes != oldDelegate.strokes ||
           currentStroke != oldDelegate.currentStroke ||
           isErasing != oldDelegate.isErasing;
  }
}