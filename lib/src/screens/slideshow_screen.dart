import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import '../../l10n/app_localizations.dart';
import '../services/document_scanner.dart';
import '../services/external_file_service.dart';

class SlideshowScreen extends StatefulWidget {
  final DocumentFile documentFile;
  final String? tempFilePath;

  const SlideshowScreen({
    super.key,
    required this.documentFile,
    this.tempFilePath,
  });

  @override
  State<SlideshowScreen> createState() => _SlideshowScreenState();
}

class _SlideshowScreenState extends State<SlideshowScreen> {
  InAppWebViewController? _controller;
  String? _filePathToLoad;
  bool _isLoading = true;
  String? _errorMessage;
  String? _createdTempFilePath; // For temp files created by this screen

  @override
  void initState() {
    super.initState();
    _enterFullscreen();
    _forceLandscape();
    _initializeWebView();
  }

  @override
  void dispose() {
    _restoreOrientation();
    _exitFullscreen();
    
    // Clean up temporary file if created by this screen
    if (_createdTempFilePath != null) {
      ExternalFileService.deleteTempFile(_createdTempFilePath!);
      if (kDebugMode) {
        print('[SlideshowScreen] Deleted temp file: $_createdTempFilePath');
      }
    }
    
    super.dispose();
  }

  void _enterFullscreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  }

  void _exitFullscreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }

  /// Force device orientation to landscape while in slideshow
  void _forceLandscape() {
    if (Platform.isIOS) {
      SystemChrome.setPreferredOrientations(const [
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }
  }

  /// Restore device orientation preferences when leaving slideshow
  void _restoreOrientation() {
    if (Platform.isIOS) {
      SystemChrome.setPreferredOrientations(DeviceOrientation.values);
    }
  }

  Future<void> _initializeWebView() async {
    try {
      if (kDebugMode) {
        print('[SlideshowScreen] Initializing slideshow for: ${widget.documentFile.displayName}');
        print('[SlideshowScreen] Is external file: ${widget.documentFile.isExternal}');
      }

      String filePathToLoad;

      // Logic to determine the file path, similar to OfficeDocumentViewerScreen
      if (widget.tempFilePath != null) {
        // Use provided temp file path from the previous screen
        filePathToLoad = widget.tempFilePath!;
        if (kDebugMode) {
          print('[SlideshowScreen] Using provided temp file path: $filePathToLoad');
        }
      } else if (widget.documentFile.isExternal && widget.documentFile.externalBytes != null) {
        // External file: copy to a new temporary file for the slideshow
        if (kDebugMode) {
          print('[SlideshowScreen] Processing external file with ${widget.documentFile.externalBytes!.length} bytes');
        }
        try {
          _createdTempFilePath = await ExternalFileService.copyExternalFileToTemp(
            fileBytes: widget.documentFile.externalBytes!,
            originalFileName: widget.documentFile.displayName,
          );
          filePathToLoad = _createdTempFilePath!;
          if (kDebugMode) {
            print('[SlideshowScreen] External file copied to new temp path: $filePathToLoad');
          }
        } catch (copyError) {
          if (kDebugMode) {
            print('[SlideshowScreen] Error copying external file for slideshow: $copyError');
          }
          if (mounted) {
            setState(() {
              _isLoading = false;
              _errorMessage = 'Cannot copy external file: $copyError';
            });
          }
          return;
        }
      } else {
        // Local file: use the original path
        filePathToLoad = widget.documentFile.path;
        if (kDebugMode) {
          print('[SlideshowScreen] Using local file path: $filePathToLoad');
        }
      }

      // Verify the file exists
      final file = File(filePathToLoad);
      if (kDebugMode) {
        print('[SlideshowScreen] Checking file existence: $filePathToLoad');
      }

      if (await file.exists()) {
        if (kDebugMode) {
          print('[SlideshowScreen] File exists, ready to load in WebView');
        }
        _filePathToLoad = filePathToLoad;
        
        // File path is ready, so stop loading and let the WebView build
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      } else {
        if (kDebugMode) {
          print('[SlideshowScreen] File does not exist: $filePathToLoad');
        }
        if (mounted) {
          setState(() {
            _isLoading = false;
            _errorMessage = 'File not found at: $filePathToLoad';
          });
        }
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('[SlideshowScreen] Error initializing slideshow: $e');
        print('[SlideshowScreen] Stack trace: $stackTrace');
      }
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to initialize slideshow: $e';
        });
      }
    }
  }

  Future<void> _jumpToFirstSlide() async {
    if (kDebugMode) {
      print('[SlideshowScreen] Attempting to jump to first slide');
    }
    
    try {
      // 尝试多种方法跳转到第一页
      if (_controller != null) {
        await _controller!.evaluateJavascript(source: '''
        (function() {
          console.log('[SlideshowScreen] Executing jump to first slide JavaScript');
          
          // 方法1: 清除URL hash，通常用于基于hash的导航
          if (window.location.hash) {
            console.log('[SlideshowScreen] Clearing URL hash: ' + window.location.hash);
            window.location.hash = '';
            // 强制重新加载
            setTimeout(function() {
              window.location.reload();
            }, 100);
          }
          
          // 方法2: 滚动到文档顶部
          console.log('[SlideshowScreen] Scrolling to top');
          window.scrollTo(0, 0);
          document.documentElement.scrollTop = 0;
          document.body.scrollTop = 0;
          
          // 方法3: 查找并点击"第一页"或"开始"按钮
          var firstPageSelectors = [
            'button[title*="first" i]',
            'button[title*="start" i]', 
            'button[title*="beginning" i]',
            'a[href*="slide=1"]',
            'a[href*="page=1"]',
            '.slide-nav-first',
            '.ppt-nav-first'
          ];
          
          for (var i = 0; i < firstPageSelectors.length; i++) {
            var element = document.querySelector(firstPageSelectors[i]);
            if (element && typeof element.click === 'function') {
              console.log('[SlideshowScreen] Found and clicking element: ' + firstPageSelectors[i]);
              element.click();
              break;
            }
          }
          
          // 方法4: 对于PowerPoint Online查看器的特殊处理
          if (typeof PowerPointViewer !== 'undefined') {
            console.log('[SlideshowScreen] PowerPoint viewer detected');
            if (PowerPointViewer.goToSlide) {
              PowerPointViewer.goToSlide(1);
            } else if (PowerPointViewer.setCurrentSlide) {
              PowerPointViewer.setCurrentSlide(0); // 0-based index
            }
          }
          
          // 方法5: 查找并触发键盘事件（Home键）
          console.log('[SlideshowScreen] Triggering Home key event');
          var homeEvent = new KeyboardEvent('keydown', {
            key: 'Home',
            keyCode: 36,
            which: 36,
            bubbles: true
          });
          document.dispatchEvent(homeEvent);
          
          console.log('[SlideshowScreen] Jump to first slide completed');
        })();
        ''');
      }
      
      if (kDebugMode) {
        print('[SlideshowScreen] Jump to first slide JavaScript executed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[SlideshowScreen] Error jumping to first slide: $e');
      }
    }
  }

  void _exitSlideshow() {
    Navigator.of(context).pop();
  }


  Widget _buildInAppWebView() {
    if (_filePathToLoad == null) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    return InAppWebView(
      initialUrlRequest: URLRequest(url: WebUri('file://$_filePathToLoad')),
      initialSettings: InAppWebViewSettings(
        javaScriptEnabled: true,
        allowsInlineMediaPlayback: true,
        mediaPlaybackRequiresUserGesture: false,
        allowsBackForwardNavigationGestures: true,
        supportZoom: true,
        builtInZoomControls: false,
        displayZoomControls: false,
        // iOS specific settings for local file access
        allowFileAccessFromFileURLs: true,
        allowUniversalAccessFromFileURLs: true,
        allowFileAccess: true,
      ),
      onWebViewCreated: (controller) {
        _controller = controller;
        if (kDebugMode) {
          print('[SlideshowScreen] InAppWebView created');
        }
      },
      onLoadStart: (controller, url) {
        if (kDebugMode) {
          print('[SlideshowScreen] Page started loading: $url');
        }
      },
      onLoadStop: (controller, url) async {
        if (kDebugMode) {
          print('[SlideshowScreen] Page finished loading: $url');
        }
        
        // Jump to first slide after page loads
        await Future.delayed(const Duration(milliseconds: 1500));
        await _jumpToFirstSlide();
      },
      onReceivedError: (controller, request, error) {
        if (kDebugMode) {
          print('[SlideshowScreen] WebView error: ${error.description}');
        }
        if (mounted) {
          setState(() {
            _isLoading = false;
            _errorMessage = error.description;
          });
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _exitSlideshow,
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.white,
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context)!.cannotOpenFile,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            TextButton(
              onPressed: _exitSlideshow,
              child: Text(
                AppLocalizations.of(context)!.exitSlideshow,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      );
    }

    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              color: Colors.white,
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context)!.loadingDocument,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    // Show WebView only on iOS as per the original logic
    if (Platform.isIOS) {
      return Stack(
        children: [
          _buildInAppWebView(),
          // Exit hint at top right
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                AppLocalizations.of(context)!.exitSlideshow,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
            ),
          ),
        ],
      );
    } else {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.info_outline,
              size: 64,
              color: Colors.white,
            ),
            const SizedBox(height: 16),
            Text(
              'WebView slideshow is only supported on iOS',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            TextButton(
              onPressed: _exitSlideshow,
              child: Text(
                AppLocalizations.of(context)!.exitSlideshow,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      );
    }
  }
}