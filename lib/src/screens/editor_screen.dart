import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as p;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:provider/provider.dart';

import '../app_theme.dart';
import '../models/image_item.dart';
import '../services/document_scanner.dart';
import '../services/image_processing_service.dart';
import '../services/local_image_storage.dart';
import '../state/app_state.dart';
import '../../l10n/app_localizations.dart';
import 'crop_screen.dart';

enum OperationType {
  addImage,
  deleteImage,
  reorderImages,
  cropImage,
  takePicture,
}

class UndoOperation {
  final OperationType type;
  final List<File> previousImages;
  final Map<String, dynamic> metadata;
  final String description;

  UndoOperation({
    required this.type,
    required this.previousImages,
    required this.description,
    this.metadata = const {},
  });
}

class EditorScreen extends StatefulWidget {
  final bool fromCamera;
  final bool fromGallery;
  const EditorScreen({super.key, this.fromCamera = false, this.fromGallery = false});

  @override
  State<EditorScreen> createState() => _EditorScreenState();
}

class _EditorScreenState extends State<EditorScreen> {
  final ImagePicker _picker = ImagePicker();
  final List<ImageItem> _imageItems = [];
  final List<UndoOperation> _undoStack = [];
  bool _saving = false;
  bool _hasUserActions = false; // Track if user has performed actual editing actions
  final ImageProcessingService _processingService = ImageProcessingService();
  final LocalImageStorage _localStorage = LocalImageStorage();

  @override
  void initState() {
    super.initState();
    Future.microtask(() async {
      // Initialize local storage
      await _localStorage.initialize(AppLocalizations.of(context)!);
      
      if (widget.fromCamera) {
        await _takePictureInitial();
      } else if (widget.fromGallery) {
        await _pickFromGalleryInitial();
      }
    });
  }

  @override
  void dispose() {
    _processingService.dispose();
    super.dispose();
  }

  /// 处理新图像的完整流程：保存原图 → 分辨率检查 → AI处理
  Future<void> _processNewImage(File imageFile, {bool isUserAction = true}) async {
    if (kDebugMode) {
      print('[EditorScreen] Processing new image: ${imageFile.path}');
    }

    // 1. 生成唯一ID并创建ImageItem
    final imageId = 'img_${DateTime.now().millisecondsSinceEpoch}_${_imageItems.length}';
    
    try {
      // 2. 保存到"拍照原图"目录
      final savedFile = await _localStorage.saveOriginalPhoto(imageFile, imageId);
      
      // 3. 创建ImageItem并添加到列表（显示为processing状态）
      final imageItem = ImageItem(
        id: imageId,
        originalFile: savedFile,
        createdAt: DateTime.now(),
        status: ImageProcessingStatus.processing,
      );

      setState(() {
        _imageItems.add(imageItem);
      });

      if (kDebugMode) {
        print('[EditorScreen] Image added to list, starting AI processing...');
      }

      // 4. 后台开始AI处理
      _processImageWithAI(imageItem);

    } catch (e) {
      if (kDebugMode) {
        print('[EditorScreen] Error processing image: $e');
      }
      
      // 如果保存失败，创建一个错误状态的ImageItem
      final errorItem = ImageItem(
        id: imageId,
        originalFile: imageFile,
        createdAt: DateTime.now(),
        status: ImageProcessingStatus.failed,
        errorMessage: e.toString(),
      );

      setState(() {
        _imageItems.add(errorItem);
      });
    }
  }

  /// AI处理图像（异步，不阻塞UI）
  Future<void> _processImageWithAI(ImageItem imageItem) async {
    try {
      if (kDebugMode) {
        print('[EditorScreen] Starting AI processing for: ${imageItem.id}');
      }

      // 调用图像处理服务
      final processedImages = await _processingService.processImages([imageItem.originalFile]);
      
      if (processedImages.isNotEmpty) {
        final processedImage = processedImages.first;
        
        // 检查是否是低分辨率
        if (processedImage.metadata['isLowResolution'] == true) {
          if (kDebugMode) {
            print('[EditorScreen] Image marked as low resolution: ${imageItem.id}');
          }
          
          _updateImageItemStatus(imageItem.id, ImageProcessingStatus.lowResolution);
          return;
        }
        
        // 检查是否有处理错误
        if (processedImage.metadata['isError'] == true) {
          if (kDebugMode) {
            print('[EditorScreen] AI processing failed: ${processedImage.metadata['error']}');
          }
          
          _updateImageItemStatus(imageItem.id, ImageProcessingStatus.failed, 
              errorMessage: processedImage.metadata['error']?.toString());
          return;
        }
        
        // AI处理成功，下载处理后的图像
        if (processedImage.processedUrl.isNotEmpty) {
          try {
            final processedFile = await _localStorage.downloadProcessedImage(
                processedImage.processedUrl, imageItem.id);
            
            // 提取AI检测的角点数据
            CropData? aiCropData;
            if (processedImage.metadata['corners'] != null) {
              try {
                final cornersData = processedImage.metadata['corners'];
                if (kDebugMode) {
                  print('[EditorScreen] Raw corners data: $cornersData');
                  print('[EditorScreen] Corners data type: ${cornersData.runtimeType}');
                }
                
                List<Point> corners = [];
                if (cornersData is List) {
                  for (var cornerData in cornersData) {
                    if (cornerData is List && cornerData.length >= 2) {
                      corners.add(Point(
                        (cornerData[0] as num).toDouble(),
                        (cornerData[1] as num).toDouble(),
                      ));
                    } else if (cornerData is Map) {
                      corners.add(Point(
                        (cornerData['x'] as num).toDouble(),
                        (cornerData['y'] as num).toDouble(),
                      ));
                    }
                  }
                }
                
                if (corners.length == 4) {
                  // 获取原图尺寸（从加载的图像或文件信息）
                  final bytes = await imageItem.originalFile.readAsBytes();
                  final codec = await ui.instantiateImageCodec(bytes);
                  final frame = await codec.getNextFrame();
                  final imageWidth = frame.image.width.toDouble();
                  final imageHeight = frame.image.height.toDouble();
                  frame.image.dispose();
                  
                  // 检查角点坐标是否为绝对像素坐标，如果是则转换为归一化坐标
                  List<Point> normalizedCorners = corners.map((corner) {
                    double normalizedX = corner.x;
                    double normalizedY = corner.y;
                    
                    // 如果坐标值大于1.0，说明是绝对像素坐标，需要归一化
                    if (corner.x > 1.0 || corner.y > 1.0) {
                      normalizedX = corner.x / imageWidth;
                      normalizedY = corner.y / imageHeight;
                    }
                    
                    // 确保坐标在0-1范围内
                    normalizedX = normalizedX.clamp(0.0, 1.0);
                    normalizedY = normalizedY.clamp(0.0, 1.0);
                    
                    return Point(normalizedX, normalizedY);
                  }).toList();
                  
                  aiCropData = CropData(
                    corners: normalizedCorners,
                    originalWidth: imageWidth,
                    originalHeight: imageHeight,
                  );
                  
                  if (kDebugMode) {
                    print('[EditorScreen] Created AI crop data with ${corners.length} corners');
                    print('[EditorScreen] Image dimensions: ${imageWidth}x$imageHeight');
                    print('[EditorScreen] Original AI corners (absolute): $corners');
                    print('[EditorScreen] Normalized AI corners: $normalizedCorners');
                  }
                } else {
                  if (kDebugMode) {
                    print('[EditorScreen] Invalid corners count: ${corners.length}, expected 4');
                  }
                }
              } catch (e) {
                if (kDebugMode) {
                  print('[EditorScreen] Failed to parse corners data: $e');
                }
              }
            }
            
            _updateImageItemStatus(imageItem.id, ImageProcessingStatus.processed, 
                processedFile: processedFile, aiCropData: aiCropData);
            
            if (kDebugMode) {
              print('[EditorScreen] AI processing completed successfully: ${imageItem.id}');
            }
          } catch (e) {
            if (kDebugMode) {
              print('[EditorScreen] Failed to download processed image: $e');
            }
            _updateImageItemStatus(imageItem.id, ImageProcessingStatus.failed, 
                errorMessage: 'Failed to download processed image');
          }
        } else {
          _updateImageItemStatus(imageItem.id, ImageProcessingStatus.failed, 
              errorMessage: 'No processed image returned');
        }
      } else {
        _updateImageItemStatus(imageItem.id, ImageProcessingStatus.failed, 
            errorMessage: 'No processing results returned');
      }

    } catch (e) {
      if (kDebugMode) {
        print('[EditorScreen] AI processing error for ${imageItem.id}: $e');
      }
      
      _updateImageItemStatus(imageItem.id, ImageProcessingStatus.failed, 
          errorMessage: e.toString());
    }
  }

  /// 更新ImageItem的状态
  void _updateImageItemStatus(String imageId, ImageProcessingStatus status, {
    File? processedFile,
    String? errorMessage,
    CropData? aiCropData,
  }) {
    setState(() {
      final index = _imageItems.indexWhere((item) => item.id == imageId);
      if (index != -1) {
        _imageItems[index] = _imageItems[index].copyWith(
          status: status,
          processedFile: processedFile,
          errorMessage: errorMessage,
          aiCropData: aiCropData,
        );
      }
    });
  }

  void _pushUndoOperation(OperationType type, String description, {Map<String, dynamic>? metadata, bool isUserAction = true}) {
    _undoStack.add(UndoOperation(
      type: type,
      previousImages: _imageItems.map((item) => item.currentFile).toList(),
      description: description,
      metadata: metadata ?? {},
    ));

    // Mark that user has performed actions (not initial camera/gallery)
    if (isUserAction) {
      _hasUserActions = true;
    }

    // Limit undo stack size
    if (_undoStack.length > 20) {
      _undoStack.removeAt(0);
    }
  }

  String _getUndoDescription() {
    if (_undoStack.isEmpty) return '';
    final lastOp = _undoStack.last;
    return lastOp.description;
  }

  Future<bool> _onWillPop() async {
    // Show confirmation if there are images present (unsaved work),
    // regardless of whether they came from initial camera/gallery load
    // or subsequent user actions. Allow immediate pop only when there
    // is nothing to lose or while saving is in progress.
    if (_saving || _imageItems.isEmpty) return true;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.discardChangesTitle),
        content: Text(AppLocalizations.of(context)!.discardChangesContent),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(AppLocalizations.of(context)!.keepEditing),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.error),
            child: Text(AppLocalizations.of(context)!.discard),
          ),
        ],
      ),
    );

    return confirmed ?? false;
  }

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AppState>();
    final brand = state.activeBrandTheme;

    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: brand.brandColor,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          title: Text(AppLocalizations.of(context)!.imageToPdf),
          actions: [
            IconButton(
              onPressed: _undoStack.isEmpty || _saving || !_hasUserActions ? null : _showUndoConfirmation,
              icon: const Icon(Icons.undo),
              tooltip: AppLocalizations.of(context)!.undo,
            )
          ],
        ),
        body: Column(
          children: [
          // Main grid area
          Expanded(
            child: Container(
              width: double.infinity,
              color: Colors.grey.shade100,
              padding: const EdgeInsets.all(16),
              child: _buildImageGrid(),
            ),
          ),
          // Bottom action area
          Container(
            padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
            color: Theme.of(context).scaffoldBackgroundColor,
            child: _buildBottomActions(brand),
          ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageGrid() {
    if (_imageItems.isEmpty) {
      return Center(
        child: GestureDetector(
          onTap: _showAddMoreOptions,
          child: Container(
            width: 150,
            height: 150,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey.shade300,
                width: 2,
                style: BorderStyle.solid,
              ),
              color: Colors.grey.shade50,
            ),
            child: Icon(
              Icons.add,
              size: 48,
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    final children = <Widget>[];

    // Add image thumbnails
    children.addAll(_imageItems.asMap().entries.map((entry) {
      final index = entry.key;
      final imageItem = entry.value;
      return _ImageThumbnail(
        key: ValueKey(imageItem.id),
        imageItem: imageItem,
        index: index,
        onCrop: () => _cropImage(index),
        onDelete: () => _showDeleteConfirmation(index),
        onTap: () => _openImagePreview(index),
      );
    }));

    // Add placeholder for adding more images
    children.add(_AddMorePlaceholder(
      key: const ValueKey('add_more'),
      onTap: _showAddMoreOptions,
    ));

    return ReorderableGridView.count(
      crossAxisCount: _getCrossAxisCount(),
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      children: children,
      onReorder: (oldIndex, newIndex) {
        // Don't allow reordering the add more placeholder
        if (oldIndex >= _imageItems.length || newIndex >= _imageItems.length) return;

        _pushUndoOperation(
          OperationType.reorderImages,
          AppLocalizations.of(context)!.reorderImage,
        );
        setState(() {
          if (newIndex > oldIndex) newIndex -= 1;
          final item = _imageItems.removeAt(oldIndex);
          _imageItems.insert(newIndex, item);
        });
      },
    );
  }

  int _getCrossAxisCount() {
    final width = MediaQuery.of(context).size.width;
    if (width > 600) return 4;
    if (width > 400) return 3;
    return 2;
  }

  Widget _buildBottomActions(BrandTheme brand) {
    final galleryButton = _buildCircularButton(
      icon: Icons.photo_library_outlined,
      onPressed: _pickFromGallery,
      size: 48,
      iconSize: 20,
    );

    final cameraButton = _buildCircularButton(
      icon: Icons.camera_alt_outlined,
      onPressed: _takePicture,
      size: 48,
      iconSize: 20,
    );

    final convertButton = SizedBox(
      width: 120,
      height: 56,
      child: ElevatedButton(
        onPressed: _imageItems.isEmpty || _saving ? null : _savePdf,
        style: ElevatedButton.styleFrom(
          backgroundColor: brand.brandColor,
          foregroundColor: Colors.white,
          disabledBackgroundColor: Colors.grey.shade300,
          disabledForegroundColor: Colors.grey.shade700,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
          elevation: 3,
          shadowColor: brand.brandColor.withOpacity(0.3),
        ),
        child: _saving
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                AppLocalizations.of(context)!.convert,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.5,
                ),
              ),
      ),
    );

    final List<Widget> actionButtons = [galleryButton, cameraButton];

    return Row(
      children: [
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: actionButtons,
          ),
        ),
        const SizedBox(width: 12),
        convertButton,
      ],
    );
  }

  Widget _buildCircularButton({
    required IconData icon, 
    required VoidCallback onPressed,
    double size = 56,
    double iconSize = 24,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.grey.shade300, width: 1.5),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(size / 2),
          onTap: onPressed,
          child: Center(
            child: Icon(
              icon,
              color: Colors.grey.shade700,
              size: iconSize,
            ),
          ),
        ),
      ),
    );
  }

  // Initial methods for entry from camera/gallery (don't count as user actions)
  Future<void> _takePictureInitial() async {
    try {
      final img = await _picker.pickImage(source: ImageSource.camera, imageQuality: 95);
      if (img != null) {
        await _processNewImage(File(img.path), isUserAction: false);
      } else {
        // If user cancels and no images are present, pop back
        if (_imageItems.isEmpty && mounted) {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[EditorScreen] Error taking initial picture: $e');
      }
    }
  }

  Future<void> _pickFromGalleryInitial() async {
    try {
      final imgs = await _picker.pickMultiImage(imageQuality: 95);
      if (imgs.isNotEmpty) {
        await _processBatchImages(imgs, isUserAction: false);
        // If no images were successfully processed and list is empty, pop back
        if (_imageItems.isEmpty && mounted) {
          Navigator.of(context).pop();
        }
      } else {
        // If user cancels and no images are present, pop back
        if (_imageItems.isEmpty && mounted) {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      await _handleGalleryPickingError(e, isInitial: true);
    }
  }

  // User-initiated methods (count as user actions)
  Future<void> _takePicture() async {
    try {
      final img = await _picker.pickImage(source: ImageSource.camera, imageQuality: 95);
      if (img != null) {
        _pushUndoOperation(
          OperationType.takePicture,
          AppLocalizations.of(context)!.takePicture,
        );
        await _processNewImage(File(img.path), isUserAction: true);
      }
    } catch (e) {
      if (kDebugMode) {
        print('[EditorScreen] Error taking picture: $e');
      }
    }
  }

  Future<void> _pickFromGallery() async {
    try {
      final imgs = await _picker.pickMultiImage(imageQuality: 95);
      if (imgs.isNotEmpty) {
        _pushUndoOperation(
          OperationType.addImage,
          AppLocalizations.of(context)!.addImagesFromGallery(imgs.length),
        );
        await _processBatchImages(imgs, isUserAction: true);
      }
    } catch (e) {
      await _handleGalleryPickingError(e, isInitial: false);
    }
  }

  Future<void> _pickFromFiles() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
        allowMultiple: true,
        dialogTitle: AppLocalizations.of(context)!.importFromOtherApps,
      );
      
      if (result != null && result.files.isNotEmpty) {
        final files = result.files
            .where((file) => file.path != null)
            .map((file) => File(file.path!))
            .toList();
        
        if (files.isNotEmpty) {
          _pushUndoOperation(
            OperationType.addImage,
            AppLocalizations.of(context)!.addImagesFromFiles(files.length),
          );
          await _processBatchFiles(files, isUserAction: true);
        }
      }
    } catch (e) {
      await _handleFilePickingError(e);
    }
  }

  /// 批量处理XFile图片列表，处理部分成功的情况
  Future<void> _processBatchImages(List<XFile> images, {required bool isUserAction}) async {
    if (kDebugMode) {
      print('[EditorScreen] Processing batch of ${images.length} images');
    }

    int successCount = 0;
    int failCount = 0;
    List<String> failedReasons = [];

    for (int i = 0; i < images.length; i++) {
      final img = images[i];
      try {
        // 验证图片文件是否有效
        final file = File(img.path);
        if (!await _validateImageFile(file)) {
          failCount++;
          failedReasons.add('Invalid image file: ${img.path.split('/').last}');
          if (kDebugMode) {
            print('[EditorScreen] Skipping invalid image: ${img.path}');
          }
          continue;
        }

        await _processNewImage(file, isUserAction: isUserAction);
        successCount++;
        
        if (kDebugMode) {
          print('[EditorScreen] Successfully processed image ${i + 1}/${images.length}');
        }
      } catch (e) {
        failCount++;
        failedReasons.add('Failed to process: ${img.path.split('/').last}');
        if (kDebugMode) {
          print('[EditorScreen] Failed to process image ${i + 1}: $e');
        }
      }
    }

    // 显示批量处理结果
    await _showBatchProcessingResult(successCount, failCount, failedReasons, isInitial: !isUserAction);
  }

  /// 批量处理File文件列表
  Future<void> _processBatchFiles(List<File> files, {required bool isUserAction}) async {
    if (kDebugMode) {
      print('[EditorScreen] Processing batch of ${files.length} files');
    }

    int successCount = 0;
    int failCount = 0;
    List<String> failedReasons = [];

    for (int i = 0; i < files.length; i++) {
      final file = files[i];
      try {
        // 验证图片文件是否有效
        if (!await _validateImageFile(file)) {
          failCount++;
          failedReasons.add('Invalid image file: ${file.path.split('/').last}');
          if (kDebugMode) {
            print('[EditorScreen] Skipping invalid file: ${file.path}');
          }
          continue;
        }

        await _processNewImage(file, isUserAction: isUserAction);
        successCount++;
        
        if (kDebugMode) {
          print('[EditorScreen] Successfully processed file ${i + 1}/${files.length}');
        }
      } catch (e) {
        failCount++;
        failedReasons.add('Failed to process: ${file.path.split('/').last}');
        if (kDebugMode) {
          print('[EditorScreen] Failed to process file ${i + 1}: $e');
        }
      }
    }

    // 显示批量处理结果
    await _showBatchProcessingResult(successCount, failCount, failedReasons, isInitial: false);
  }

  /// 验证图片文件是否有效
  Future<bool> _validateImageFile(File file) async {
    try {
      // 检查文件是否存在
      if (!await file.exists()) {
        if (kDebugMode) {
          print('[EditorScreen] File does not exist: ${file.path}');
        }
        return false;
      }

      // 检查文件大小（不能为0）
      final fileSize = await file.length();
      if (fileSize == 0) {
        if (kDebugMode) {
          print('[EditorScreen] File is empty: ${file.path}');
        }
        return false;
      }

      // 检查文件扩展名
      final path = file.path.toLowerCase();
      final validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      final hasValidExtension = validExtensions.any((ext) => path.endsWith(ext));
      if (!hasValidExtension) {
        if (kDebugMode) {
          print('[EditorScreen] Invalid file extension: ${file.path}');
        }
        return false;
      }

      // 尝试读取文件头部来验证是否为有效图片
      try {
        final bytes = await file.readAsBytes();
        if (bytes.isEmpty) {
          if (kDebugMode) {
            print('[EditorScreen] Could not read file bytes: ${file.path}');
          }
          return false;
        }

        // 简单的文件头验证
        if (bytes.length < 4) {
          if (kDebugMode) {
            print('[EditorScreen] File too small to be valid image: ${file.path}');
          }
          return false;
        }

        // 检查常见图片文件头
        final header = bytes.take(4).toList();
        // PNG: 89 50 4E 47
        if (header[0] == 0x89 && header[1] == 0x50 && header[2] == 0x4E && header[3] == 0x47) return true;
        // JPEG: FF D8 FF
        if (header[0] == 0xFF && header[1] == 0xD8 && header[2] == 0xFF) return true;
        // GIF: 47 49 46 38 or 47 49 46 39
        if (header[0] == 0x47 && header[1] == 0x49 && header[2] == 0x46) return true;
        // BMP: 42 4D
        if (header[0] == 0x42 && header[1] == 0x4D) return true;
        // WebP: 52 49 46 46
        if (header[0] == 0x52 && header[1] == 0x49 && header[2] == 0x46 && header[3] == 0x46) {
          if (bytes.length > 8) {
            final webpCheck = bytes.skip(8).take(4).toList();
            if (webpCheck[0] == 0x57 && webpCheck[1] == 0x45 && webpCheck[2] == 0x42 && webpCheck[3] == 0x50) return true;
          }
        }

        // 如果没有匹配已知格式，但有合理的扩展名，仍然尝试处理
        if (kDebugMode) {
          print('[EditorScreen] Unknown image format but will try to process: ${file.path}');
        }
        return true;
      } catch (e) {
        if (kDebugMode) {
          print('[EditorScreen] Error reading file for validation: ${file.path}, error: $e');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('[EditorScreen] Error validating file: ${file.path}, error: $e');
      }
      return false;
    }
  }

  /// 显示批量处理结果
  Future<void> _showBatchProcessingResult(int successCount, int failCount, List<String> failedReasons, {required bool isInitial}) async {
    if (!mounted) return;

    if (kDebugMode) {
      print('[EditorScreen] Batch processing result: $successCount success, $failCount failed');
      if (failedReasons.isNotEmpty) {
        print('[EditorScreen] Failed reasons: ${failedReasons.join(', ')}');
      }
    }

    if (successCount == 0 && failCount > 0) {
      // 所有图片都失败了
      final message = AppLocalizations.of(context)!.allImportsFailed(failCount);
      if (isInitial && _imageItems.isEmpty) {
        await _showErrorDialog(
          title: AppLocalizations.of(context)!.importFailed,
          message: message,
          actionSuggestion: AppLocalizations.of(context)!.selectedImagesCorruptedOrInaccessible,
          showRetry: true,
          showCamera: true,
          onRetry: () => _pickFromGalleryInitial(),
          onCamera: () => _takePicture(),
          onCancel: () => Navigator.of(context).pop(),
        );
      } else {
        _showErrorSnackBar(
          message: message,
          actionSuggestion: AppLocalizations.of(context)!.trySelectingDifferentImages,
          showRetry: true,
          onRetry: () => _pickFromGallery(),
        );
      }
    } else if (failCount > 0 && successCount > 0) {
      // 部分成功，部分失败
      final message = AppLocalizations.of(context)!.partialImportSuccess(successCount, failCount);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.orange.shade600,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: AppLocalizations.of(context)!.details,
            textColor: Colors.white,
            onPressed: () {
              _showPartialFailureDetails(successCount, failCount, failedReasons);
            },
          ),
        ),
      );
    } else if (successCount > 0) {
      // 全部成功，显示简短成功消息
      if (kDebugMode) {
        print('[EditorScreen] All $successCount images imported successfully');
      }
    }
  }

  /// 显示部分失败的详细信息
  void _showPartialFailureDetails(int successCount, int failCount, List<String> failedReasons) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.importResults),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(AppLocalizations.of(context)!.successfullyImported(successCount)),
            const SizedBox(height: 8),
            Text(AppLocalizations.of(context)!.failedToImport(failCount)),
            if (failedReasons.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(AppLocalizations.of(context)!.failedFiles, style: const TextStyle(fontWeight: FontWeight.w600)),
              const SizedBox(height: 4),
              ...failedReasons.take(5).map((reason) => Padding(
                padding: const EdgeInsets.only(left: 8, top: 2),
                child: Text('• $reason', style: TextStyle(fontSize: 13, color: Colors.grey.shade700)),
              )),
              if (failedReasons.length > 5)
                Padding(
                  padding: const EdgeInsets.only(left: 8, top: 2),
                  child: Text('• ${AppLocalizations.of(context)!.andXMore(failedReasons.length - 5)}', 
                             style: TextStyle(fontSize: 13, color: Colors.grey.shade700)),
                ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context)!.ok),
          ),
        ],
      ),
    );
  }

  /// 处理图库选择错误的统一方法
  Future<void> _handleGalleryPickingError(dynamic error, {required bool isInitial}) async {
    if (kDebugMode) {
      print('[EditorScreen] Gallery picking error: $error');
      print('[EditorScreen] Error type: ${error.runtimeType}');
      if (error is PlatformException) {
        print('[EditorScreen] Platform error code: ${error.code}');
        print('[EditorScreen] Platform error message: ${error.message}');
        print('[EditorScreen] Platform error details: ${error.details}');
      }
    }

    if (!mounted) return;

    String errorMessage;
    String? actionSuggestion;
    bool shouldShowRetry = false;
    bool shouldOfferCamera = false;

    if (error is PlatformException) {
      switch (error.code) {
        case 'missing_valid_image_uri':
          errorMessage = AppLocalizations.of(context)!.galleryAccessError;
          actionSuggestion = AppLocalizations.of(context)!.tryAgainOrUseCamera;
          shouldShowRetry = true;
          shouldOfferCamera = true;
          break;
        case 'photo_access_denied':
        case 'camera_access_denied':
          errorMessage = AppLocalizations.of(context)!.permissionDenied;
          actionSuggestion = AppLocalizations.of(context)!.checkPermissionsInSettings;
          shouldOfferCamera = true;
          break;
        case 'invalid_image':
          errorMessage = AppLocalizations.of(context)!.invalidImageSelected;
          shouldShowRetry = true;
          break;
        default:
          errorMessage = AppLocalizations.of(context)!.gallerySelectionFailed;
          shouldShowRetry = true;
          shouldOfferCamera = true;
      }
    } else {
      errorMessage = AppLocalizations.of(context)!.unexpectedError;
      shouldShowRetry = true;
    }

    // 对于初始加载失败，如果没有图片则返回上级页面
    if (isInitial && _imageItems.isEmpty) {
      await _showErrorDialog(
        title: AppLocalizations.of(context)!.galleryLoadFailed,
        message: errorMessage,
        actionSuggestion: actionSuggestion,
        showRetry: shouldShowRetry,
        showCamera: shouldOfferCamera,
        onRetry: () => _pickFromGalleryInitial(),
        onCamera: () => _takePicture(),
        onCancel: () => Navigator.of(context).pop(),
      );
    } else {
      // 对于用户主动选择失败，显示错误提示但不退出
      _showErrorSnackBar(
        message: errorMessage,
        actionSuggestion: actionSuggestion,
        showRetry: shouldShowRetry,
        showCamera: shouldOfferCamera,
        onRetry: () => _pickFromGallery(),
        onCamera: () => _takePicture(),
      );
    }
  }

  /// 处理文件选择错误
  Future<void> _handleFilePickingError(dynamic error) async {
    if (kDebugMode) {
      print('[EditorScreen] File picking error: $error');
      print('[EditorScreen] Error type: ${error.runtimeType}');
    }

    if (!mounted) return;

    String errorMessage = AppLocalizations.of(context)!.fileSelectionFailed;
    if (error is PlatformException) {
      switch (error.code) {
        case 'read_external_storage_denied':
          errorMessage = AppLocalizations.of(context)!.storagePermissionDenied;
          break;
        case 'invalid_file_type':
          errorMessage = AppLocalizations.of(context)!.unsupportedFileType;
          break;
      }
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(errorMessage),
        backgroundColor: Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 显示错误对话框（用于关键错误）
  Future<void> _showErrorDialog({
    required String title,
    required String message,
    String? actionSuggestion,
    bool showRetry = false,
    bool showCamera = false,
    VoidCallback? onRetry,
    VoidCallback? onCamera,
    VoidCallback? onCancel,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(message),
              if (actionSuggestion != null) ...[
                const SizedBox(height: 8),
                Text(
                  actionSuggestion,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontSize: 14,
                  ),
                ),
              ],
            ],
          ),
          actions: <Widget>[
            if (onCancel != null)
              TextButton(
                onPressed: onCancel,
                child: Text(AppLocalizations.of(context)!.cancel),
              ),
            if (showCamera && onCamera != null)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onCamera();
                },
                child: Text(AppLocalizations.of(context)!.camera),
              ),
            if (showRetry && onRetry != null)
              FilledButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onRetry();
                },
                child: Text(AppLocalizations.of(context)!.tryAgain),
              ),
          ],
        );
      },
    );
  }

  /// 显示错误提示条（用于一般错误）
  void _showErrorSnackBar({
    required String message,
    String? actionSuggestion,
    bool showRetry = false,
    bool showCamera = false,
    VoidCallback? onRetry,
    VoidCallback? onCamera,
  }) {
    final displayMessage = actionSuggestion != null 
        ? '$message\n$actionSuggestion'
        : message;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(displayMessage),
        backgroundColor: Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 5),
        action: showRetry && onRetry != null
            ? SnackBarAction(
                label: AppLocalizations.of(context)!.tryAgain,
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : (showCamera && onCamera != null
                ? SnackBarAction(
                    label: AppLocalizations.of(context)!.camera,
                    textColor: Colors.white,
                    onPressed: onCamera,
                  )
                : null),
      ),
    );
  }


  void _showAddMoreOptions() {
    final theme = Theme.of(context);
    showModalBottomSheet(
      context: context,
      backgroundColor: theme.colorScheme.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt_outlined),
              title: Text(AppLocalizations.of(context)!.takePhoto),
              iconColor: theme.colorScheme.onSurfaceVariant,
              textColor: theme.colorScheme.onSurface,
              onTap: () {
                Navigator.pop(context);
                _takePicture();
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library_outlined),
              title: Text(AppLocalizations.of(context)!.importFromAlbum),
              iconColor: theme.colorScheme.onSurfaceVariant,
              textColor: theme.colorScheme.onSurface,
              onTap: () {
                Navigator.pop(context);
                _pickFromGallery();
              },
            ),
            if (Platform.isIOS)
              ListTile(
                leading: const Icon(Icons.folder_outlined),
                title: Text(AppLocalizations.of(context)!.importFromOtherApps),
                iconColor: theme.colorScheme.onSurfaceVariant,
                textColor: theme.colorScheme.onSurface,
                onTap: () {
                  Navigator.pop(context);
                  _pickFromFiles();
                },
              ),
          ],
        ),
      ),
    );
  }

  void _openImagePreview(int initialIndex) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => ImagePreviewScreen(
          images: _imageItems.map((item) => item.currentFile).toList(),
          initialIndex: initialIndex,
        ),
      ),
    );
  }

  Future<void> _cropImage(int index) async {
    final imageItem = _imageItems[index];
    
    if (kDebugMode) {
      print('[EditorScreen] _cropImage called for index: $index');
      print('[EditorScreen] ImageItem status: ${imageItem.status}');
      print('[EditorScreen] ImageItem id: ${imageItem.id}');
      print('[EditorScreen] Original file: ${imageItem.originalFile.path}');
      print('[EditorScreen] Current file: ${imageItem.currentFile.path}');
      print('[EditorScreen] AI crop data available: ${imageItem.aiCropData != null}');
      if (imageItem.aiCropData != null) {
        print('[EditorScreen] AI corners count: ${imageItem.aiCropData!.corners.length}');
        print('[EditorScreen] AI corners: ${imageItem.aiCropData!.corners}');
      }
      print('[EditorScreen] Metadata: ${imageItem.metadata}');
    }
    
    // 优先使用用户历史裁剪数据，否则使用AI检测数据
    final cornersToUse = imageItem.userCropData?.corners ?? imageItem.aiCropData?.corners;
    
    if (kDebugMode) {
      final cornerSource = imageItem.userCropData?.corners != null ? "用户历史数据" : 
                          (imageItem.aiCropData?.corners != null ? "AI检测数据" : "默认数据");
      print('[EditorScreen] 传递给CropScreen的角点来源: $cornerSource');
      print('[EditorScreen] 角点坐标: $cornersToUse');
    }
    
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => CropScreen(
          image: imageItem.originalFile, // 使用原图
          aiCorners: cornersToUse, // 优先使用用户数据
        ),
      ),
    );

    if (result != null && result is CropResult) {
      _pushUndoOperation(
        OperationType.cropImage,
        AppLocalizations.of(context)!.cropImage,
        metadata: {'index': index},
      );
      
      // 创建用户裁剪数据
      final userCropData = CropData(
        corners: result.userCorners,
        originalWidth: result.originalImageSize.width,
        originalHeight: result.originalImageSize.height,
      );
      
      if (kDebugMode) {
        print('[EditorScreen] 保存用户裁剪数据 - 图片${index + 1}');
        print('[EditorScreen] 用户调整的角点: ${result.userCorners}');
      }
      
      setState(() {
        _imageItems[index] = _imageItems[index].copyWith(
          processedFile: result.croppedFile,
          status: ImageProcessingStatus.cropped,
          userCropData: userCropData, // 保存用户裁剪数据
        );
      });
    }
  }

  Future<void> _showDeleteConfirmation(int index) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.deleteImage),
        content: Text(AppLocalizations.of(context)!.confirmDeleteImage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(AppLocalizations.of(context)!.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(AppLocalizations.of(context)!.delete),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      _pushUndoOperation(
        OperationType.deleteImage,
        AppLocalizations.of(context)!.deleteImage,
        metadata: {'index': index, 'deletedImage': _imageItems[index].currentFile.path},
      );
      setState(() {
        _imageItems.removeAt(index);
      });
    }
  }

  Future<void> _showUndoConfirmation() async {
    final description = _getUndoDescription();
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.undo),
        content: Text(AppLocalizations.of(context)!.confirmUndo(description)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(AppLocalizations.of(context)!.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(AppLocalizations.of(context)!.undo),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      _performUndo();
    }
  }

  void _performUndo() {
    if (_undoStack.isEmpty) return;

    final operation = _undoStack.removeLast();
    setState(() {
      // Convert back from Files to ImageItems for undo
      _imageItems.clear();
      for (int i = 0; i < operation.previousImages.length; i++) {
        final file = operation.previousImages[i];
        _imageItems.add(ImageItem(
          id: 'undo_${DateTime.now().millisecondsSinceEpoch}_$i',
          originalFile: file,
          createdAt: DateTime.now(),
          status: ImageProcessingStatus.original,
        ));
      }
    });
  }

  Future<void> _savePdf() async {
    // 1. Ask for filename
    final fileName = await _showSaveFileNameDialog();
    if (fileName == null || fileName.isEmpty) {
      return; // User cancelled
    }

    setState(() => _saving = true);

    try {
      // Get the correct directory from the centralized storage service
      final pdfDir = _localStorage.generatedPdfsDir;
      if (pdfDir == null) {
        throw Exception("PDF storage directory is not available.");
      }
      final outPath = p.join(pdfDir.path, '$fileName.pdf');
      
      // Generate PDF locally using already processed images
      if (kDebugMode) {
        print('[EditorScreen] Generating PDF locally with processed images...');
      }
      
      final outFile = await _generatePdfLocally(outPath);

      // Add to documents list in AppState
      final appState = context.read<AppState>();
      final doc = DocumentFile(
        path: outFile.path,
        displayName: p.basename(outFile.path),
        sizeBytes: await outFile.length(),
        modified: await outFile.lastModified(),
      );
      appState.scanner.addFiles([doc]);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.pdfSavedSuccessfully)),
        );
        // Navigate to documents tab
        appState.setBottomTab(0);
        Navigator.pop(context);
      }
    } catch (e) {
      if (kDebugMode) {
        print('[EditorScreen] Error saving PDF: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.pdfSaveFailed)),
        );
      }
    } finally {
      if (mounted) setState(() => _saving = false);
    }
  }

  Future<File> _generatePdfLocally(String outputPath) async {
    if (kDebugMode) {
      print('[EditorScreen] Generating PDF locally...');
    }
    
    final pdf = pw.Document();

    for (final imageItem in _imageItems) {
      final imgFile = imageItem.currentFile;
      final bytes = await imgFile.readAsBytes();
      final image = pw.MemoryImage(bytes);
      if (image.width != null && image.height != null) {
        final pageFormat = PdfPageFormat(
          image.width!.toDouble(),
          image.height!.toDouble(),
          marginAll: 0,
        );
        pdf.addPage(
          pw.Page(
            pageFormat: pageFormat,
            build: (context) => pw.Image(image, fit: pw.BoxFit.fill),
          ),
        );
      }
    }

    final outFile = File(outputPath);
    await outFile.writeAsBytes(await pdf.save());
    
    if (kDebugMode) {
      print('[EditorScreen] Local PDF generation completed');
    }
    
    return outFile;
  }

  Future<String?> _showSaveFileNameDialog() async {
    final now = DateTime.now();
    final scanPrefix = AppLocalizations.of(context)!.scanPrefix;
    final formattedDate = '${now.year.toString().padLeft(4, '0')}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';
    final controller = TextEditingController(text: '${scanPrefix}_$formattedDate');
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.saveAs),
        content: TextField(
          controller: controller,
          autofocus: true,
          decoration: InputDecoration(
            hintText: AppLocalizations.of(context)!.fileName,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context)!.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(controller.text);
            },
            child: Text(AppLocalizations.of(context)!.save),
          ),
        ],
      ),
    );
  }
}

class _ImageThumbnail extends StatelessWidget {
  final ImageItem imageItem;
  final int index;
  final VoidCallback onCrop;
  final VoidCallback onDelete;
  final VoidCallback onTap;

  const _ImageThumbnail({
    super.key,
    required this.imageItem,
    required this.index,
    required this.onCrop,
    required this.onDelete,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Image with tap detector and status overlay
          GestureDetector(
            onTap: onTap,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: AspectRatio(
                  aspectRatio: 1.0,
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      // Display current image (processed or original)
                      Image.file(
                        imageItem.currentFile,
                        fit: BoxFit.cover,
                      ),
                      // Processing overlay
                      if (imageItem.isProcessing)
                        Container(
                          color: Colors.black.withOpacity(0.5),
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  localizations.aiProcessing,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      // Status badges
                      if (imageItem.isLowResolution)
                        Positioned(
                          bottom: 8,
                          right: 8,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              localizations.lowResolution,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          // Crop button bottom-left (only show when appropriate)
          if (imageItem.shouldShowCropButton)
            Positioned(
              bottom: 8,
              left: 8,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.9),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(Icons.crop, color: Colors.grey),
                  onPressed: onCrop,
                  iconSize: 16,
                  padding: const EdgeInsets.all(6),
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
              ),
            ),
          // Delete button top-right (smaller)
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: onDelete,
              child: Container(
                width: 18,
                height: 18,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Center(
                  child: Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 12,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _AddMorePlaceholder extends StatelessWidget {
  final VoidCallback onTap;

  const _AddMorePlaceholder({
    super.key,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.grey.shade300,
            width: 2,
            style: BorderStyle.solid,
          ),
          color: Colors.grey.shade50,
        ),
        child: AspectRatio(
          aspectRatio: 1.0,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add,
                size: 32,
                color: Colors.grey.shade600,
              ),
              const SizedBox(height: 8),
              Text(
                AppLocalizations.of(context)!.addMoreImages,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Full-screen image preview
class ImagePreviewScreen extends StatefulWidget {
  final List<File> images;
  final int initialIndex;

  const ImagePreviewScreen({
    super.key,
    required this.images,
    required this.initialIndex,
  });

  @override
  State<ImagePreviewScreen> createState() => _ImagePreviewScreenState();
}

class _ImagePreviewScreenState extends State<ImagePreviewScreen> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text(localizations.imagePreviewTitle(_currentIndex + 1, widget.images.length)),
        elevation: 0,
      ),
      body: PageView.builder(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        itemCount: widget.images.length,
        itemBuilder: (context, index) {
          return InteractiveViewer(
            child: Center(
              child: Image.file(
                widget.images[index],
                fit: BoxFit.contain,
              ),
            ),
          );
        },
      ),
    );
  }
}

// ReorderableGridView implementation
class ReorderableGridView extends StatefulWidget {
  final List<Widget> children;
  final Function(int, int) onReorder;
  final int crossAxisCount;
  final double crossAxisSpacing;
  final double mainAxisSpacing;

  const ReorderableGridView.count({
    super.key,
    required this.children,
    required this.onReorder,
    required this.crossAxisCount,
    this.crossAxisSpacing = 0,
    this.mainAxisSpacing = 0,
  });

  @override
  State<ReorderableGridView> createState() => _ReorderableGridViewState();
}

class _ReorderableGridViewState extends State<ReorderableGridView> {
  @override
  Widget build(BuildContext context) {
    return GridView.count(
      crossAxisCount: widget.crossAxisCount,
      crossAxisSpacing: widget.crossAxisSpacing,
      mainAxisSpacing: widget.mainAxisSpacing,
      children: widget.children.asMap().entries.map((entry) {
        final index = entry.key;
        final child = entry.value;

        return LongPressDraggable<int>(
          data: index,
          feedback: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(12),
            child: SizedBox(
              width: 100,
              height: 100,
              child: child,
            ),
          ),
          childWhenDragging: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.grey.shade300,
            ),
          ),
          child: DragTarget<int>(
            onAcceptWithDetails: (details) {
              final draggedIndex = details.data;
              if (draggedIndex != index) {
                widget.onReorder(draggedIndex, index);
              }
            },
            builder: (context, candidateData, rejectedData) {
              return child;
            },
          ),
        );
      }).toList(),
    );
  }
}
