import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:provider/provider.dart';
import 'package:path_provider/path_provider.dart';

import '../../l10n/app_localizations.dart';
import '../services/external_file_service.dart';
import '../services/document_scanner.dart';
import '../state/app_state.dart';

class DocumentViewerScreen extends StatefulWidget {
  final DocumentFile documentFile;

  const DocumentViewerScreen({
    super.key,
    required this.documentFile,
  });

  @override
  State<DocumentViewerScreen> createState() => _DocumentViewerScreenState();
}

class _DocumentViewerScreenState extends State<DocumentViewerScreen> {
  InAppWebViewController? _controller;
  String? _filePathToLoad;
  bool _isLoading = true;
  String? _errorMessage;
  String? _tempFilePath; // For external files copied to temp directory

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  Future<void> _initializeWebView() async {
    try {
      if (kDebugMode) {
        print('[DocumentViewerScreen] Starting initialization for file: ${widget.documentFile.path}');
        print('[DocumentViewerScreen] File name: ${widget.documentFile.displayName}');
        print('[DocumentViewerScreen] Is external file: ${widget.documentFile.isExternal}');
        print('[DocumentViewerScreen] Platform: ${Platform.operatingSystem}');
      }
      

      // Handle external files vs local files differently
      String filePathToLoad;
      
      if (widget.documentFile.isExternal && widget.documentFile.externalBytes != null) {
        // External file - copy to temporary directory first
        if (kDebugMode) {
          print('[DocumentViewerScreen] Processing external file with ${widget.documentFile.externalBytes!.length} bytes');
        }
        
        try {
          _tempFilePath = await ExternalFileService.copyExternalFileToTemp(
            fileBytes: widget.documentFile.externalBytes!,
            originalFileName: widget.documentFile.displayName,
          );
          filePathToLoad = _tempFilePath!;
          
          if (kDebugMode) {
            print('[DocumentViewerScreen] External file copied to: $filePathToLoad');
          }
        } catch (copyError) {
          if (kDebugMode) {
            print('[DocumentViewerScreen] Error copying external file: $copyError');
          }
          if (mounted) {
            setState(() {
              _isLoading = false;
              _errorMessage = 'Cannot copy external file: $copyError';
            });
          }
          return;
        }
      } else {
        // Local file - use original path
        filePathToLoad = widget.documentFile.path;
        if (kDebugMode) {
          print('[DocumentViewerScreen] Using local file path: $filePathToLoad');
          try {
            final appDocs = await getApplicationDocumentsDirectory();
            final appDocsPath = appDocs.path;
            final inAppContainer = filePathToLoad.startsWith(appDocsPath);
            print('[DocumentViewerScreen] App documents path: $appDocsPath');
            print('[DocumentViewerScreen] Path in app container: $inAppContainer');
          } catch (e) {
            print('[DocumentViewerScreen] Error getting app documents path: $e');
          }
        }
      }

      // Verify the file to load exists and is accessible
      final file = File(filePathToLoad);
      if (kDebugMode) {
        print('[DocumentViewerScreen] Checking file existence: $filePathToLoad');
      }
      
      if (await file.exists()) {
        final fileStat = await file.stat();
        if (kDebugMode) {
          print('[DocumentViewerScreen] File exists: $filePathToLoad');
          print('[DocumentViewerScreen] File size: ${fileStat.size} bytes');
          print('[DocumentViewerScreen] File modified: ${fileStat.modified}');
          print('[DocumentViewerScreen] File type: ${fileStat.type}');
        }
        
        // Check if file is readable
        try {
          final uri = Uri.file(filePathToLoad);
          if (kDebugMode) {
            print('[DocumentViewerScreen] File URI: ${uri.toString()}');
            print('[DocumentViewerScreen] File URI scheme: ${uri.scheme}');
            print('[DocumentViewerScreen] File URI path: ${uri.path}');
          }
          
          // Try to read a small portion to verify access
          final bytes = await file.openRead(0, 100).toList();
          if (kDebugMode) {
            print('[DocumentViewerScreen] File is readable, first bytes length: ${bytes.length}');
          }
          
          // Store the file path for WebView loading
          _filePathToLoad = filePathToLoad;
          
          if (kDebugMode) {
            print('[DocumentViewerScreen] File path prepared for WebView: $filePathToLoad');
          }
        } catch (readError) {
          if (kDebugMode) {
            print('[DocumentViewerScreen] Error reading file: $readError');
          }
          if (mounted) {
            setState(() {
              _isLoading = false;
              _errorMessage = 'Cannot read file: $readError';
            });
          }
        }
      } else {
        if (kDebugMode) {
          print('[DocumentViewerScreen] File does not exist: $filePathToLoad');
          // Try to list parent directory contents
          final parentDir = file.parent;
          try {
            if (await parentDir.exists()) {
              final entities = await parentDir.list().toList();
              print('[DocumentViewerScreen] Parent directory contents:');
              for (var entity in entities) {
                print('[DocumentViewerScreen]   - ${entity.path}');
              }
            } else {
              print('[DocumentViewerScreen] Parent directory does not exist: ${parentDir.path}');
            }
          } catch (e) {
            print('[DocumentViewerScreen] Cannot list parent directory: $e');
          }
        }
        if (mounted) {
          setState(() {
            _isLoading = false;
            _errorMessage = 'File not found at: $filePathToLoad';
          });
        }
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('[DocumentViewerScreen] Error initializing WebView: $e');
        print('[DocumentViewerScreen] Stack trace: $stackTrace');
      }
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Initialization error: $e';
        });
      }
    }
  }

  Widget _buildInAppWebView() {
    if (_filePathToLoad == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return InAppWebView(
      initialUrlRequest: URLRequest(url: WebUri('file://$_filePathToLoad')),
      initialSettings: InAppWebViewSettings(
        javaScriptEnabled: false,
        allowsInlineMediaPlayback: true,
        mediaPlaybackRequiresUserGesture: false,
        allowsBackForwardNavigationGestures: true,
        supportZoom: true,
        builtInZoomControls: true,
        displayZoomControls: false,
        // iOS specific settings for local file access
        allowFileAccessFromFileURLs: true,
        allowUniversalAccessFromFileURLs: true,
        allowFileAccess: true,
      ),
      onWebViewCreated: (controller) {
        _controller = controller;
        if (kDebugMode) {
          print('[DocumentViewerScreen] InAppWebView created');
        }
      },
      onLoadStart: (controller, url) {
        if (kDebugMode) {
          print('[DocumentViewerScreen] Page started loading: $url');
        }
      },
      onLoadStop: (controller, url) async {
        if (kDebugMode) {
          print('[DocumentViewerScreen] Page finished loading: $url');
        }
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
        
        // Update recents with the loaded file path
        if (mounted && _filePathToLoad != null) {
          try {
            context.read<AppState>().recents.add(_filePathToLoad!);
            if (kDebugMode) {
              print('[DocumentViewerScreen] Added to recents: $_filePathToLoad');
            }
          } catch (e) {
            if (kDebugMode) {
              print('[DocumentViewerScreen] Failed to add to recents: $e');
            }
          }
        }
      },
      onReceivedError: (controller, request, error) {
        if (kDebugMode) {
          print('[DocumentViewerScreen] WebView error: ${error.description}');
          print('[DocumentViewerScreen] WebView error type: ${error.type}');
        }
        if (mounted) {
          setState(() {
            _isLoading = false;
            _errorMessage = '${error.description}';
          });
        }
      },
      onReceivedHttpError: (controller, request, errorResponse) {
        if (kDebugMode) {
          print('[DocumentViewerScreen] HTTP error: ${errorResponse.statusCode} - ${errorResponse.reasonPhrase}');
        }
        if (mounted) {
          setState(() {
            _isLoading = false;
            _errorMessage = 'HTTP Error: ${errorResponse.statusCode}';
          });
        }
      },
    );
  }


  @override
  void dispose() {
    // Clean up temporary file if created
    if (_tempFilePath != null) {
      ExternalFileService.deleteTempFile(_tempFilePath!);
    }
    super.dispose();
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.documentFile.displayName,
          overflow: TextOverflow.ellipsis,
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context)!.cannotOpenFile,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context)!.loadingDocument,
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ],
        ),
      );
    }

    // Only show WebView on iOS platforms where this functionality is intended
    if (Platform.isIOS) {
      return _buildInAppWebView();
    } else {
      // Fallback for non-iOS platforms (shouldn't normally reach here based on our logic)
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.info_outline,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'WebView document viewing is only supported on iOS',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }
  }
}
