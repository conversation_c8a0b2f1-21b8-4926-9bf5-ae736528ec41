import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart' as painting;
import 'package:provider/provider.dart';

import '../models/signature.dart';
import '../services/signature_service.dart';
import '../screens/signature_creation_screen.dart';
import '../state/app_state.dart';
import '../../l10n/app_localizations.dart';

class SignatureManagementScreen extends StatefulWidget {
  const SignatureManagementScreen({super.key});

  @override
  State<SignatureManagementScreen> createState() => _SignatureManagementScreenState();
}

class _SignatureManagementScreenState extends State<SignatureManagementScreen> {
  List<Signature> _signatures = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSignatures();
  }

  Future<void> _loadSignatures() async {
    try {
      await SignatureService.instance.initialize();
      setState(() {
        _signatures = SignatureService.instance.all;
        _isLoading = false;
      });
      // Ensure thumbnails refresh by clearing cached FileImage entries
      painting.PaintingBinding.instance.imageCache.clear();
      
      if (kDebugMode) {
        print('[SignatureManagementScreen] Loaded ${_signatures.length} signatures');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[SignatureManagementScreen] Error loading signatures: $e');
      }
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final state = context.watch<AppState>();
    final brand = state.activeBrandTheme;

    const fabSize = 56.0;
    const fabMargin = 16.0;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: brand.brandColor,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        title: Text(l10n.manageSignatures),
      ),
      body: Stack(
        children: [
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _signatures.isEmpty
                  ? _buildEmptyState(context)
                  : _buildSignatureGrid(context),
          Positioned(
            right: fabMargin,
            bottom: fabMargin,
            child: FloatingActionButton(
              onPressed: () => _navigateToCreateSignature(context),
              tooltip: l10n.addSignature,
              child: const Icon(Icons.add),
            ),
          ),
          if (_signatures.isNotEmpty)
            Positioned(
              left: 0,
              right: fabMargin + fabSize,
              bottom: fabMargin,
              height: fabSize,
              child: Container(
                alignment: Alignment.center,
                child: Text(
                  l10n.signatureUsageHint,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.draw,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              l10n.manageSignaturesHint,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSignatureGrid(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: GridView.builder(
              padding: const EdgeInsets.only(bottom: 80.0),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                // Slightly taller tiles to better fit various signatures
                childAspectRatio: 4 / 3,
              ),
              itemCount: _signatures.length,
              itemBuilder: (context, index) {
                final signature = _signatures[index];
                return _buildSignatureCard(context, signature);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSignatureCard(BuildContext context, Signature signature) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          GestureDetector(
            onTap: () => _navigateToEditSignature(context, signature),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.white,
                child: FutureBuilder<String>(
                  future: SignatureService.instance.generateThumbnail(signature),
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      final thumbnailPath = snapshot.data!;
                      final file = File(thumbnailPath);
                      
                      if (file.existsSync()) {
                        return Image.file(
                          file,
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildSignaturePreview(signature);
                          },
                        );
                      }
                    }
                    
                    return _buildSignaturePreview(signature);
                  },
                ),
              ),
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () => _confirmDeleteSignature(context, signature),
              child: Container(
                width: 24,
                height: 24,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSignaturePreview(Signature signature) {
    return CustomPaint(
      painter: SignaturePreviewPainter(signature: signature),
      size: Size.infinite,
    );
  }

  Future<void> _confirmDeleteSignature(BuildContext context, Signature signature) async {
    final l10n = AppLocalizations.of(context)!;
    
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.deleteSignature),
        content: Text(l10n.confirmDeleteSignature),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(l10n.delete),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _deleteSignature(signature);
    }
  }

  Future<void> _deleteSignature(Signature signature) async {
    try {
      await SignatureService.instance.deleteSignature(signature.id);
      
      // Reload signatures from service instead of modifying unmodifiable list
      setState(() {
        _signatures = SignatureService.instance.all;
      });
      
      if (kDebugMode) {
        print('[SignatureManagementScreen] Deleted signature: ${signature.id}');
      }
      
      if (context.mounted) {
        final l10n = AppLocalizations.of(context)!;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(l10n.signatureDeleted)),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('[SignatureManagementScreen] Error deleting signature: $e');
      }
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to delete signature')),
        );
      }
    }
  }

  Future<void> _navigateToCreateSignature(BuildContext context) async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => const SignatureCreationScreen(),
      ),
    );

    if (result == true) {
      await _loadSignatures();
    }
  }

  Future<void> _navigateToEditSignature(BuildContext context, Signature signature) async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => SignatureCreationScreen(existingSignature: signature),
      ),
    );

    if (result == true) {
      await _loadSignatures();
    }
  }
}

class SignaturePreviewPainter extends CustomPainter {
  final Signature signature;

  SignaturePreviewPainter({required this.signature});

  @override
  void paint(Canvas canvas, Size size) {
    if (signature.strokes.isEmpty) return;

    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final scaleX = size.width / signature.canvasSize.width;
    final scaleY = size.height / signature.canvasSize.height;
    final scale = scaleX < scaleY ? scaleX : scaleY;

    final offsetX = (size.width - signature.canvasSize.width * scale) / 2;
    final offsetY = (size.height - signature.canvasSize.height * scale) / 2;

    for (final stroke in signature.strokes) {
      if (stroke.points.length < 2) continue;
      
      paint.color = stroke.color;
      paint.strokeWidth = stroke.strokeWidth * scale;
      
      final path = Path();
      final firstPoint = stroke.points.first;
      path.moveTo(
        offsetX + firstPoint.dx * scale,
        offsetY + firstPoint.dy * scale,
      );
      
      for (int i = 1; i < stroke.points.length; i++) {
        final point = stroke.points[i];
        path.lineTo(
          offsetX + point.dx * scale,
          offsetY + point.dy * scale,
        );
      }
      
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(SignaturePreviewPainter oldDelegate) {
    return signature != oldDelegate.signature;
  }
}
