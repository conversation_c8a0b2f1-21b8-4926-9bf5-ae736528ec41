import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../state/app_state.dart';
import '../navigation/app_shell.dart';
import '../../l10n/app_localizations.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;
  Timer? _timer;
  
  // Debug switch to stay on splash screen
  static const bool _stayOnSplash = false;

  @override
  void initState() {
    super.initState();
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    // Start the progress animation and timer
    _startSplashSequence();
  }

  void _startSplashSequence() {
    if (kDebugMode) {
      print('[SplashScreen] Starting splash sequence');
    }
    
    _progressController.forward();
    
    _timer = Timer(const Duration(milliseconds: 2000), () {
      if (mounted && !_stayOnSplash) {
        if (kDebugMode) {
          print('[SplashScreen] Splash sequence complete, navigating to main app');
        }
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const AppShell()),
        );
      } else if (_stayOnSplash && kDebugMode) {
        if (kDebugMode) {
          print('[SplashScreen] Debug mode: staying on splash screen');
        }
      }
    });
  }

  @override
  void dispose() {
    _progressController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenHeight < 700;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(minHeight: constraints.maxHeight),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildTitleSection(l10n, theme, isSmallScreen),
                      _buildFeaturePreview(l10n, theme, isSmallScreen),
                      _buildProgressSection(l10n, theme, isSmallScreen),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTitleSection(AppLocalizations l10n, ThemeData theme, bool isSmallScreen) {
    return Column(
      children: [
        Image.asset(
          'assets/images/app_icon.png',
          height: isSmallScreen ? 50 : 70,
          width: isSmallScreen ? 50 : 70,
        ),
        const SizedBox(height: 16),
        Text(
          l10n.appName,
          style: theme.textTheme.headlineLarge?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: isSmallScreen ? 28 : 36,
            color: const Color(0xFF1a1a1a),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          l10n.splashTitle, // Slogan
          style: theme.textTheme.titleMedium?.copyWith(
            fontSize: isSmallScreen ? 15 : 17,
            color: const Color(0xFF666666),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildFeaturePreview(AppLocalizations l10n, ThemeData theme, bool isSmallScreen) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            l10n.splashSubtitle, // Feature description
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontSize: isSmallScreen ? 14 : 16,
              color: const Color(0xFF333333),
              height: 1.4,
            ),
          ),
        ),
        SizedBox(height: isSmallScreen ? 16 : 24),
        _buildImageComparison(l10n, theme, isSmallScreen),
      ],
    );
  }

  Widget _buildImageComparison(AppLocalizations l10n, ThemeData theme, bool isSmallScreen) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildImageCard(l10n.splashOriginalPhoto, 'assets/images/before.jpg', theme, isSmallScreen),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8.0 : 16.0, vertical: 50),
          child: Icon(
            Icons.arrow_forward_rounded,
            size: isSmallScreen ? 28 : 36,
            color: theme.primaryColor,
          ),
        ),
        _buildImageCard(l10n.splashGeneratedPdf, 'assets/images/after.jpg', theme, isSmallScreen),
      ],
    );
  }

  Widget _buildImageCard(String title, String imagePath, ThemeData theme, bool isSmallScreen) {
    return Expanded(
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!, width: 1),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.asset(
                imagePath,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[200],
                    child: Icon(Icons.broken_image, size: 48, color: Colors.grey[400]),
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: theme.textTheme.labelLarge?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: isSmallScreen ? 12 : 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection(AppLocalizations l10n, ThemeData theme, bool isSmallScreen) {
    final brand = context.watch<AppState>().activeBrandTheme;
    return Column(
      children: [
        Text(
          l10n.splashWorkflow,
          style: TextStyle(
            fontSize: isSmallScreen ? 12 : 14,
            color: const Color(0xFF888888),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 12),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 40.0),
          child: AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return LinearProgressIndicator(
                value: _progressAnimation.value,
                backgroundColor: Colors.grey[200],
                valueColor: AlwaysStoppedAnimation<Color>(brand.brandColor),
                minHeight: 5,
                borderRadius: BorderRadius.circular(5),
              );
            },
          ),
        ),
      ],
    );
  }
}