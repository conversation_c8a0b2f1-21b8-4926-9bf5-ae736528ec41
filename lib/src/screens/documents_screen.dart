import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../state/app_state.dart';
import '../services/directory_picker_service.dart';
import '../services/authorized_directories_service.dart';
import 'editor_screen.dart';
import '../widgets/file_tile.dart';
import '../../l10n/app_localizations.dart';
import '../utils/device_utils.dart';
import '../widgets/global_type_tabs.dart';

class DocumentsScreen extends StatefulWidget {
  const DocumentsScreen({super.key});

  @override
  State<DocumentsScreen> createState() => _DocumentsScreenState();
}

class _DocumentsScreenState extends State<DocumentsScreen> {
  final GlobalKey _fabKey = GlobalKey();
  OverlayEntry? _overlayEntry;
  static const bool _forceShowTutorial = false; // For debugging
  bool _isTutorialShowing = false;

  String _getFileTypeName(DocType docType) {
    // Get the file type name that matches the tab labels
    switch (docType) {
      case DocType.pdf:
        return 'PDF';
      case DocType.word:
        return 'Word';
      case DocType.excel:
        return 'Excel';
      case DocType.ppt:
        return 'PPT';
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _refreshAll();
        _checkAndShowTutorial();
      }
    });
  }

  Future<void> _checkAndShowTutorial() async {
    if (_forceShowTutorial) {
      _showTutorialOverlay();
      return;
    }
    
    final prefs = await SharedPreferences.getInstance();
    final bool tutorialShown = prefs.getBool('documents_tutorial_shown') ?? false;

    if (!tutorialShown) {
      _showTutorialOverlay();
      await prefs.setBool('documents_tutorial_shown', true);
    }
  }

  void _showTutorialOverlay() {
    setState(() {
      _isTutorialShowing = true;
    });
    _overlayEntry = OverlayEntry(
      builder: (context) {
        final RenderBox fabRenderBox = _fabKey.currentContext!.findRenderObject() as RenderBox;
        final fabSize = fabRenderBox.size;
        final fabOffset = fabRenderBox.localToGlobal(Offset.zero);

        return _TutorialOverlay(
          fabOffset: fabOffset,
          fabSize: fabSize,
          onDismiss: () {
            _overlayEntry?.remove();
            _overlayEntry = null;
            setState(() {
              _isTutorialShowing = false;
            });
          },
        );
      },
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  Future<void> _refreshAll() async {
    // Scan authorized directories
    await context.read<AppState>().refreshScan();
    // Scan app's own directory
    if (mounted) {
      await _scanAppDirectory(context);
    }
  }

  Future<void> _scanAppDirectory(BuildContext context) async {
    final appState = context.read<AppState>();
    List<FileInfo> allFileInfos = [];

    try {
      // Scan internal app directory
      await _scanSingleAppDirectory(
        await getApplicationDocumentsDirectory(),
        'internal app directory',
        allFileInfos,
      );

      // Scan external app directory (where LocalImageStorage saves files)
      if (Platform.isAndroid) {
        try {
          final dirs = await getExternalStorageDirectories(type: StorageDirectory.documents);
          if (dirs != null && dirs.isNotEmpty) {
            const String appName = "All PDF Editor";
            final appDir = Directory(p.join(dirs.first.path, appName));
            await _scanSingleAppDirectory(
              appDir,
              'external app directory',
              allFileInfos,
            );
          }
        } catch (e) {
          if (kDebugMode) {
            print('[DocumentsScreen] Error scanning external app directory: $e');
          }
        }
      }

      if (kDebugMode) {
        print('[DocumentsScreen] Found ${allFileInfos.length} total files in app directories');
        for (var file in allFileInfos) {
          print('[DocumentsScreen]  - ${file.path}');
        }
      }

      await appState.addDirectoryFiles(allFileInfos);
      
      if (context.mounted && allFileInfos.isNotEmpty) {
        final totalFiles = appState.scanner.pdfs.length + 
                          appState.scanner.words.length + 
                          appState.scanner.excels.length + 
                          appState.scanner.ppts.length;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.loadedFiles(totalFiles))),
        );
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error scanning app directories: $e');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.cannotLoadDirectoryFiles)),
        );
      }
    }
  }

  // Helper method to scan a single app directory
  Future<void> _scanSingleAppDirectory(
    Directory dir,
    String dirDescription,
    List<FileInfo> allFileInfos,
  ) async {
    try {
      final path = dir.path;
      if (kDebugMode) {
        print('[DocumentsScreen] Scanning $dirDescription: $path');
      }

      if (!await dir.exists()) {
        if (kDebugMode) {
          print('[DocumentsScreen] $dirDescription does not exist: $path');
        }
        return;
      }

      final List<FileSystemEntity> entities = await dir.list(recursive: true).toList();
      final List<File> dartIoFiles = entities.whereType<File>().where((file) {
        final extension = file.path.split('.').last.toLowerCase();
        return ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].contains(extension);
      }).toList();

      if (kDebugMode) {
        print('[DocumentsScreen] Found ${dartIoFiles.length} files in $dirDescription');
      }

      final fileInfos = await Future.wait(dartIoFiles.map((file) async {
        final fileStat = await file.stat();
        return FileInfo(
          name: file.path.split('/').last,
          path: file.path,
          size: fileStat.size,
          isDirectory: false,
          modificationDate: fileStat.modified,
        );
      }));

      allFileInfos.addAll(fileInfos);
    } catch (e) {
      if (kDebugMode) {
        print('[DocumentsScreen] Error scanning $dirDescription: $e');
      }
    }
  }

  Future<void> _pickFiles(BuildContext context) async {
    final appState = context.read<AppState>();
    try {
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
        withData: true, // Ensure we get file bytes for external files
      );
      
      if (kDebugMode) {
        if (result != null && result.files.isNotEmpty) {
          print('[DocumentsScreen] FilePicker returned ${result.files.length} files');
          for (var file in result.files) {
            print('[DocumentsScreen] File: ${file.name}');
            print('[DocumentsScreen] Path: ${file.path}');
            print('[DocumentsScreen] Size: ${file.size}');
            print('[DocumentsScreen] Has bytes: ${file.bytes != null}');
            if (file.bytes != null) {
              print('[DocumentsScreen] Bytes length: ${file.bytes!.length}');
            }
          }
        }
      }
      if (result != null && result.files.isNotEmpty) {
        await appState.addFiles(result.files);
      }
    } catch (e) {
      // Handle exceptions
      if (kDebugMode) {
        print('Error picking files: $e');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.cannotSelectFiles)),
        );
      }
    }
  }

  Future<void> _pickDirectory(BuildContext context) async {
    try {
      if (!(Platform.isIOS || Platform.isAndroid)) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context)!.directorySelectionOnlyMobile)),
          );
        }
        return;
      }

      if (kDebugMode) {
        print('[DocumentsScreen] Calling DirectoryPickerService.pickDirectory()');
      }
      final result = await DirectoryPickerService.pickDirectory();
      if (kDebugMode) {
        print('[DocumentsScreen] pickDirectory result: $result');
      }
      
      if (result != null && result.success) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context)!.selectingDirectory)),
          );
        }
        
        // Add directory to authorized directories service for Settings page sync
        if (kDebugMode) {
          print('[DocumentsScreen] Adding directory to AuthorizedDirectoriesService: ${result.path}');
        }
        await AuthorizedDirectoriesService.instance.addDirectory(result.path);
        
        // Notify AppState that a directory was authorized to update UI immediately
        if (context.mounted) {
          final appState = context.read<AppState>();
          await appState.onDirectoryAuthorized();
        }
        
        if (kDebugMode) {
          print('[DocumentsScreen] About to load directory files for ${result.path}');
        }
        if (context.mounted) {
          await _loadDirectoryFiles(context, result.path);
        }
      } else {
        if (kDebugMode) {
          print('[DocumentsScreen] No result or failed: $result');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error picking directory: $e');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.cannotSelectDirectory)),
        );
      }
    }
  }

  Future<void> _loadDirectoryFiles(BuildContext context, String path) async {
    final appState = context.read<AppState>();
    try {
      if (kDebugMode) {
        print('[DocumentsScreen] Calling DirectoryPickerService.getDirectoryFilesForPath()');
      }
      final files = await DirectoryPickerService.getDirectoryFilesForPath(path);
      if (kDebugMode) {
        print('[DocumentsScreen] getDirectoryFiles returned: ${files?.length ?? 0} files');
      }
      
      if (files != null) {
        if (kDebugMode) {
          print('[DocumentsScreen] About to call appState.addDirectoryFiles with ${files.length} files');
        }
        await appState.addDirectoryFiles(files);
        if (kDebugMode) {
          print('[DocumentsScreen] Finished adding directory files to app state');
        }
        
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context)!.loadedFiles(files.length))),
          );
        }
      } else {
        if (kDebugMode) {
          print('[DocumentsScreen] getDirectoryFiles returned null');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading directory files: $e');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.cannotLoadDirectoryFiles)),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _refreshAll,
        child: _FilesList(
          onPickFiles: () => _pickFiles(context),
          onPickDirectory: () => _pickDirectory(context),
          isTutorialShowing: _isTutorialShowing,
        ),
      ),
      floatingActionButton: _FabCreatePdf(
        key: _fabKey,
        onPickFiles: () => _pickFiles(context),
        onPickDirectory: () => _pickDirectory(context),
      ),
    );
  }
}


class _FilesList extends StatelessWidget {
  final VoidCallback onPickFiles;
  final VoidCallback onPickDirectory;
  final bool isTutorialShowing;
  const _FilesList({required this.onPickFiles, required this.onPickDirectory, required this.isTutorialShowing});

  String _getFileTypeName(DocType docType) {
    // Get the file type name that matches the tab labels
    switch (docType) {
      case DocType.pdf:
        return 'PDF';
      case DocType.word:
        return 'Word';
      case DocType.excel:
        return 'Excel';
      case DocType.ppt:
        return 'PPT';
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AppState>();
    final files = state.currentDocuments;

    if (!state.initialized) {
      return const Center(child: CircularProgressIndicator());
    }

    // Show progress indicator during scanning, but not during tutorial
    final showProgress = state.isScanning && !isTutorialShowing;

    if (files.isEmpty) {
      if (Platform.isIOS) {
        if (!state.hasAuthorizedDirectories) {
          // No authorized directories - show iOS permission message
          return LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: constraints.maxHeight - 48),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Flexible(
                        child: Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              if (showProgress) ...[
                                Text(
                                  AppLocalizations.of(context)!.scanningFiles,
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(fontSize: 16),
                                ),
                                const SizedBox(height: 16),
                                LinearProgressIndicator(
                                  value: state.scanProgress,
                                  backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                              ] else ...[
                                _DeviceAwarePermissionMessage(),
                                const SizedBox(height: 16),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    ElevatedButton.icon(
                                      icon: const Icon(Icons.folder_special_outlined),
                                      label: Text(AppLocalizations.of(context)!.authorizeFolder),
                                      onPressed: onPickDirectory,
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        } else {
          // Has authorized directories but no files found - show no files message
          return LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(24.0),
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: constraints.maxHeight - 48),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Flexible(
                        child: Center(
                          child: showProgress
                              ? Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      AppLocalizations.of(context)!.scanningFiles,
                                      textAlign: TextAlign.center,
                                      style: const TextStyle(fontSize: 16),
                                    ),
                                    const SizedBox(height: 16),
                                    LinearProgressIndicator(
                                      value: state.scanProgress,
                                      backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Theme.of(context).colorScheme.primary,
                                      ),
                                    ),
                                  ],
                                )
                              : Text(
                                  AppLocalizations.of(context)!.noFilesFoundForType(
                                    0, 
                                    _getFileTypeName(state.activeDocType),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        }
      } else {
        return LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(24.0),
              child: ConstrainedBox(
                constraints: BoxConstraints(minHeight: constraints.maxHeight - 48),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Flexible(
                      child: Center(
                        child: showProgress
                            ? Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    AppLocalizations.of(context)!.scanningFiles,
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(fontSize: 16),
                                  ),
                                  const SizedBox(height: 16),
                                  LinearProgressIndicator(
                                    value: state.scanProgress,
                                    backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Theme.of(context).colorScheme.primary,
                                    ),
                                  ),
                                ],
                              )
                            : Text(
                                AppLocalizations.of(context)!.noFilesFoundForType(
                                  0, 
                                  _getFileTypeName(state.activeDocType),
                                ),
                                textAlign: TextAlign.center,
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }
    }

    return Column(
      children: [
        if (showProgress) ...[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: LinearProgressIndicator(
              value: state.scanProgress,
              backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
        ],
        Expanded(
          child: ListView.separated(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            itemBuilder: (context, index) {
              final f = files[index];
              return FileTile(file: f);
            },
            separatorBuilder: (_, __) => const SizedBox(height: 4),
            itemCount: files.length,
          ),
        ),
      ],
    );
  }
}

class _FabCreatePdf extends StatelessWidget {
  final VoidCallback onPickFiles;
  final VoidCallback onPickDirectory;
  const _FabCreatePdf({super.key, required this.onPickFiles, required this.onPickDirectory});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final menuTextStyle = textTheme.titleMedium?.copyWith(color: colorScheme.onSurface);

    return FloatingActionButton(
      child: const Icon(Icons.add),
      onPressed: () async {
        await showModalBottomSheet(
          context: context,
          showDragHandle: true,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
          ),
          builder: (context) {
            return SafeArea(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                  if (Platform.isIOS) ...[
                    ListTile(
                      leading: const Icon(Icons.folder_special_outlined),
                      title: Text(AppLocalizations.of(context)!.selectFolder, style: menuTextStyle),
                      subtitle: Text(AppLocalizations.of(context)!.selectFolderSubtitle, 
                        style: TextStyle(
                          fontSize: 12, 
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        onPickDirectory();
                      },
                    ),
                  ],
                  ListTile(
                    leading: const Icon(Icons.camera_alt_outlined),
                    title: Text(AppLocalizations.of(context)!.photoToPdf, style: menuTextStyle),
                    subtitle: Text(AppLocalizations.of(context)!.photoToPdfSubtitle, 
                      style: TextStyle(
                        fontSize: 12, 
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.of(context).push(
                        MaterialPageRoute(builder: (_) => const EditorScreen(fromCamera: true)),
                      );
                    },
                  ),
                  ListTile(
                    leading: const Icon(Icons.image_outlined),
                    title: Text(AppLocalizations.of(context)!.mergeImagesToPdf, style: menuTextStyle),
                    subtitle: Text(AppLocalizations.of(context)!.mergeImagesToPdfSubtitle, 
                      style: TextStyle(
                        fontSize: 12, 
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.of(context).push(
                        MaterialPageRoute(builder: (_) => const EditorScreen(fromGallery: true)),
                      );
                    },
                  ),
                ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class _TutorialOverlay extends StatefulWidget {
  final Offset fabOffset;
  final Size fabSize;
  final VoidCallback onDismiss;

  const _TutorialOverlay({required this.fabOffset, required this.fabSize, required this.onDismiss});

  @override
  State<_TutorialOverlay> createState() => _TutorialOverlayState();
}

class _TutorialOverlayState extends State<_TutorialOverlay> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();
    _animation = Tween(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final fabCenterX = widget.fabOffset.dx + widget.fabSize.width / 2;
    final fabCenterY = widget.fabOffset.dy + widget.fabSize.height / 2;
    final fabRadius = widget.fabSize.width / 2;
    final isEnglish = AppLocalizations.of(context)!.localeName == 'en';

    return Material(
      color: Colors.black.withOpacity(0.5),
      child: Stack(
        children: [
          CustomPaint(
            size: Size.infinite,
            painter: _CircleHolePainter(
              center: Offset(fabCenterX, fabCenterY),
              radius: fabRadius + 8,
              animation: _animation,
            ),
          ),
          Positioned(
            left: (MediaQuery.of(context).size.width - 300) / 2,
            top: fabCenterY - (isEnglish ? 180 : 150),
            width: 300,
            child: _SpeechBubble(
              text: AppLocalizations.of(context)!.fabTutorial,
              arrowXOffset: fabCenterX,
              bubbleLeft: (MediaQuery.of(context).size.width - 300) / 2,
            ),
          ),
          Positioned(
            left: 0,
            right: 0,
            top: fabCenterY + fabRadius + (isEnglish ? 20 : 40),
            child: Center(
              child: ElevatedButton(
                onPressed: widget.onDismiss,
                child: Text(AppLocalizations.of(context)!.iGotIt),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _CircleHolePainter extends CustomPainter {
  final Offset center;
  final double radius;
  final Animation<double> animation;

  _CircleHolePainter({required this.center, required this.radius, required this.animation}) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final screenRect = Rect.fromLTWH(0, 0, size.width, size.height);

    // Draw the background overlay
    canvas.saveLayer(screenRect, Paint());
    paint.color = Colors.black.withOpacity(0.5);
    canvas.drawRect(screenRect, paint);

    // Draw the pulsating ripple
    final rippleRadius = radius + 20 * animation.value;
    final rippleOpacity = 1.0 - animation.value;
    paint.color = Colors.white.withOpacity(rippleOpacity.clamp(0.0, 1.0));
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;
    canvas.drawCircle(center, rippleRadius, paint);

    // Clear the area for the FAB
    paint.blendMode = BlendMode.clear;
    canvas.drawCircle(center, radius, paint);
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class _SpeechBubble extends StatelessWidget {
  final String text;
  final double arrowXOffset;
  final double bubbleLeft;
  const _SpeechBubble({required this.text, required this.arrowXOffset, required this.bubbleLeft});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _BubblePainter(arrowXOffset: arrowXOffset, bubbleLeft: bubbleLeft),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
        child: Text(
          text,
          style: const TextStyle(
            color: Colors.black,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.left,
        ),
      ),
    );
  }
}

class _BubblePainter extends CustomPainter {
  final double arrowXOffset;
  final double bubbleLeft;
  _BubblePainter({required this.arrowXOffset, required this.bubbleLeft});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(0, 12);
    path.quadraticBezierTo(0, 0, 12, 0); // Top-left corner
    path.lineTo(size.width - 12, 0);
    path.quadraticBezierTo(size.width, 0, size.width, 12); // Top-right corner
    path.lineTo(size.width, size.height - 20);
    path.quadraticBezierTo(size.width, size.height - 8, size.width - 12, size.height - 8); // Bottom-right corner
    
    // Arrow
    final arrowTipX = arrowXOffset - bubbleLeft;
    path.lineTo(arrowTipX + 8, size.height - 8);
    path.lineTo(arrowTipX, size.height);
    path.lineTo(arrowTipX - 8, size.height - 8);

    path.lineTo(12, size.height - 8);
    path.quadraticBezierTo(0, size.height - 8, 0, size.height - 20); // Bottom-left corner
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}


class _DeviceAwarePermissionMessage extends StatefulWidget {
  const _DeviceAwarePermissionMessage();

  @override
  State<_DeviceAwarePermissionMessage> createState() => _DeviceAwarePermissionMessageState();
}

class _DeviceAwarePermissionMessageState extends State<_DeviceAwarePermissionMessage> {
  String? _deviceType;

  @override
  void initState() {
    super.initState();
    _getDeviceType();
  }

  Future<void> _getDeviceType() async {
    final deviceType = await DeviceUtils.getDeviceType();
    if (mounted) {
      setState(() {
        _deviceType = deviceType;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_deviceType == null) {
      return const SizedBox(
        height: 20,
        width: 20,
        child: CircularProgressIndicator(strokeWidth: 2),
      );
    }

    final localizations = AppLocalizations.of(context)!;
    
    // Create device-aware message based on current locale
    final message = localizations.iosPermissionMessage(_deviceType!);

    return Text(
      message,
      textAlign: TextAlign.center,
      style: const TextStyle(fontSize: 16),
    );
  }
}