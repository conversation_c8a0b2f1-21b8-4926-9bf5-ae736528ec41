import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../models/image_item.dart';
import '../utils/perspective_transform.dart';
import '../../l10n/app_localizations.dart';

/// 裁剪结果数据结构
class CropResult {
  final File croppedFile;           // 裁剪后的图像文件
  final List<Point> userCorners;    // 用户调整后的角点坐标(归一化)
  final Size originalImageSize;     // 原图尺寸

  CropResult({
    required this.croppedFile,
    required this.userCorners,
    required this.originalImageSize,
  });
}

/// 裁剪屏幕 - 合并后直接显示手动裁剪界面
class CropScreen extends StatefulWidget {
  final File image;
  final List<Point>? aiCorners;

  const CropScreen({super.key, required this.image, this.aiCorners});

  @override
  State<CropScreen> createState() => _CropScreenState();
}

class _CropScreenState extends State<CropScreen> {
  List<Point> _corners = [];
  int _dragIndex = -1;
  bool _isProcessing = false;
  Offset _imageOffset = Offset.zero;
  Size _displaySize = Size.zero;
  ui.Image? _loadedImage;
  Size _imageSize = Size.zero;
  bool _hasUserModifiedCorners = false;

  @override
  void initState() {
    super.initState();
    
    if (kDebugMode) {
      print('[CropScreen] initState called');
      print('[CropScreen] Image path: ${widget.image.path}');
      print('[CropScreen] AI corners provided: ${widget.aiCorners != null}');
      if (widget.aiCorners != null) {
        print('[CropScreen] AI corners count: ${widget.aiCorners!.length}');
        print('[CropScreen] AI corners: ${widget.aiCorners!}');
      }
    }
    
    _initializeCorners();
    _loadImage();
  }

  Future<void> _loadImage() async {
    try {
      final bytes = await widget.image.readAsBytes();
      final codec = await ui.instantiateImageCodec(bytes);
      final frame = await codec.getNextFrame();

      setState(() {
        _loadedImage = frame.image;
        _imageSize = Size(frame.image.width.toDouble(), frame.image.height.toDouble());
      });

      if (kDebugMode) {
        print('[CropScreen] Image loaded: ${_imageSize.width}x${_imageSize.height}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[CropScreen] Failed to load image: $e');
      }
    }
  }

  @override
  void dispose() {
    _loadedImage?.dispose();
    super.dispose();
  }

  void _initializeCorners() {
    // 优先使用服务端返回的AI检测角点
    if (widget.aiCorners != null && widget.aiCorners!.length == 4) {
      _corners = List.from(widget.aiCorners!);
      if (kDebugMode) {
        print('[CropScreen] Using AI-detected corners: $_corners');
      }
      return;
    }
    
    // 兜底：设置默认的裁剪区域（图片中心80%）
    const inset = 0.1; // 10% inset from each edge
    _corners = [
      const Point(inset, inset), // 左上
      const Point(1 - inset, inset), // 右上
      const Point(1 - inset, 1 - inset), // 右下
      const Point(inset, 1 - inset), // 左下
    ];
    if (kDebugMode) {
      print('[CropScreen] Using default fallback corners');
    }
  }

  void _updateImageLayout(Size containerSize) {
    if (_imageSize.width == 0 || _imageSize.height == 0) return;

    // 计算适合容器的图片显示尺寸
    final containerAspect = containerSize.width / containerSize.height;
    final imageAspect = _imageSize.width / _imageSize.height;

    Size displaySize;
    if (imageAspect > containerAspect) {
      // 图片更宽，以宽度为准
      displaySize = Size(
        containerSize.width,
        containerSize.width / imageAspect,
      );
    } else {
      // 图片更高，以高度为准
      displaySize = Size(
        containerSize.height * imageAspect,
        containerSize.height,
      );
    }

    setState(() {
      _displaySize = displaySize;
      _imageOffset = Offset(
        (containerSize.width - displaySize.width) / 2,
        (containerSize.height - displaySize.height) / 2,
      );
    });
  }

  Offset _getEventPosition(Offset globalPosition, RenderBox renderBox) {
    final localPosition = renderBox.globalToLocal(globalPosition);
    // 转换为相对于图片的归一化坐标
    final relativeX = (localPosition.dx - _imageOffset.dx) / _displaySize.width;
    final relativeY = (localPosition.dy - _imageOffset.dy) / _displaySize.height;
    return Offset(relativeX.clamp(0.0, 1.0), relativeY.clamp(0.0, 1.0));
  }

  void _onPanStart(DragStartDetails details, RenderBox renderBox) {
    final position = _getEventPosition(details.globalPosition, renderBox);

    // 查找最近的控制点
    double minDistance = double.infinity;
    int closestIndex = -1;

    for (int i = 0; i < _corners.length; i++) {
      final distance = (Offset(_corners[i].x, _corners[i].y) - position).distance;
      if (distance < minDistance) {
        minDistance = distance;
        closestIndex = i;
      }
    }

    // 如果点击在控制点附近（阈值）
    const threshold = 0.08; // 8% of image size
    if (minDistance < threshold) {
      setState(() {
        _dragIndex = closestIndex;
      });
    }
  }

  void _onPanUpdate(DragUpdateDetails details, RenderBox renderBox) {
    if (_dragIndex == -1) return;

    final position = _getEventPosition(details.globalPosition, renderBox);

    setState(() {
      _corners[_dragIndex] = Point(position.dx, position.dy);
      _hasUserModifiedCorners = true;
    });
  }

  void _onPanEnd(DragEndDetails details) {
    setState(() {
      _dragIndex = -1;
    });
  }

  Future<void> _completeCrop() async {
    final localizations = AppLocalizations.of(context)!;
    if (_imageSize.width == 0 || _imageSize.height == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(localizations.imageNotLoadedError),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      // 读取原始图像数据
      final imageBytes = await widget.image.readAsBytes();

      // 转换相对坐标为绝对坐标
      final absoluteCorners = _corners.map((corner) => Point(
        corner.x * _imageSize.width,
        corner.y * _imageSize.height,
      )).toList();

      // 计算输出尺寸
      final outputSize = PerspectiveTransform.calculateOutputDimensions(absoluteCorners, maxWidth: 800);

      // 应用透视变换
      final transformedData = await PerspectiveTransform.applyPerspectiveTransform(
        imageBytes,
        absoluteCorners,
        outputSize.width.toInt(),
        outputSize.height.toInt(),
      );

      // 保存变换后的图像到临时文件
      final tempDir = Directory.systemTemp;
      final tempFile = File('${tempDir.path}/cropped_${DateTime.now().millisecondsSinceEpoch}.png');
      await tempFile.writeAsBytes(transformedData);

      // 创建裁剪结果，包含用户调整的角点坐标
      final cropResult = CropResult(
        croppedFile: tempFile,
        userCorners: List.from(_corners), // 复制当前角点坐标
        originalImageSize: _imageSize,
      );

      if (kDebugMode) {
        print('[CropScreen] Crop completed, returning user corners: ${cropResult.userCorners}');
      }

      // 返回裁剪结果（包含文件和用户角点）
      if (mounted) {
        Navigator.of(context).pop(cropResult);
      }
    } catch (e) {
      if (kDebugMode) {
        print('[CropScreen] Crop failed: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(localizations.imageProcessingFailedError),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  void _performReset() {
    setState(() {
      _initializeCorners();
      _hasUserModifiedCorners = false;
    });
  }

  Future<void> _showResetConfirmationDialog() async {
    final localizations = AppLocalizations.of(context)!;
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.restoreDefault),
        content: Text(localizations.confirmRestoreDefaultContent),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(localizations.continueAdjusting),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text(localizations.discardChangesConfirmation),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      _performReset();
    }
  }

  Future<bool> _onWillPop() async {
    if (!_hasUserModifiedCorners) {
      return true;
    }

    final localizations = AppLocalizations.of(context)!;
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.confirmExit),
        content: Text(localizations.discardCropChangesContent),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(localizations.continueAdjusting),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text(localizations.discardChangesConfirmation),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(
          backgroundColor: Colors.black,
          foregroundColor: Colors.white,
          title: Text(localizations.cropImage),
        ),
        body: Column(
          children: [
            Expanded(
              child: Center(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // 使用真实的图像尺寸
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      _updateImageLayout(
                        Size(constraints.maxWidth, constraints.maxHeight),
                      );
                    });

                    return GestureDetector(
                      onPanStart: (details) {
                        final renderBox =
                            context.findRenderObject() as RenderBox;
                        _onPanStart(details, renderBox);
                      },
                      onPanUpdate: (details) {
                        final renderBox =
                            context.findRenderObject() as RenderBox;
                        _onPanUpdate(details, renderBox);
                      },
                      onPanEnd: _onPanEnd,
                      child: SizedBox(
                        width: constraints.maxWidth,
                        height: constraints.maxHeight,
                        child: CustomPaint(
                          painter: CropOverlayPainter(
                            loadedImage: _loadedImage,
                            corners: _corners,
                            displaySize: _displaySize,
                            imageOffset: _imageOffset,
                            dragIndex: _dragIndex,
                          ),
                          size:
                              Size(constraints.maxWidth, constraints.maxHeight),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.black87,
                border: Border(
                  top: BorderSide(color: Colors.grey, width: 0.5),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    localizations.dragCornersHint,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _hasUserModifiedCorners
                              ? _showResetConfirmationDialog
                              : null,
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.white,
                            disabledForegroundColor: Colors.white54,
                            side: const BorderSide(color: Colors.white54),
                          ),
                          child: Text(
                            localizations.restoreDefault,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isProcessing ? null : _completeCrop,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                          ),
                          child: Text(localizations.save),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 自定义画笔绘制裁剪覆盖层
class CropOverlayPainter extends CustomPainter {
  final ui.Image? loadedImage;
  final List<Point> corners;
  final Size displaySize;
  final Offset imageOffset;
  final int dragIndex;

  CropOverlayPainter({
    required this.loadedImage,
    required this.corners,
    required this.displaySize,
    required this.imageOffset,
    required this.dragIndex,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (displaySize.width == 0 || displaySize.height == 0) return;

    // 绘制图像
    final imageRect = Rect.fromLTWH(
      imageOffset.dx,
      imageOffset.dy,
      displaySize.width,
      displaySize.height,
    );
    
    if (loadedImage != null) {
      // 绘制真实图像
      canvas.drawImageRect(
        loadedImage!,
        Rect.fromLTWH(0, 0, loadedImage!.width.toDouble(), loadedImage!.height.toDouble()),
        imageRect,
        Paint(),
      );
    } else {
      // 如果图像还未加载，显示占位符
      final imagePaint = Paint()..color = Colors.grey.shade800;
      canvas.drawRect(imageRect, imagePaint);
    }

    // 绘制半透明遮罩
    final overlayPaint = Paint()..color = Colors.black.withOpacity(0.5);
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), overlayPaint);

    // 绘制裁剪区域（清除遮罩）
    if (corners.length == 4) {
      final path = Path();
      final firstCorner = _cornerToScreenCoordinate(corners[0]);
      path.moveTo(firstCorner.dx, firstCorner.dy);
      
      for (int i = 1; i < corners.length; i++) {
        final corner = _cornerToScreenCoordinate(corners[i]);
        path.lineTo(corner.dx, corner.dy);
      }
      path.close();

      // 清除裁剪区域的遮罩
      canvas.saveLayer(Rect.fromLTWH(0, 0, size.width, size.height), Paint());
      canvas.drawPath(path, Paint()..blendMode = BlendMode.clear);
      canvas.restore();

      // 绘制边框
      final borderPaint = Paint()
        ..color = Colors.blue
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;
      canvas.drawPath(path, borderPaint);

      // 绘制控制点
      for (int i = 0; i < corners.length; i++) {
        final screenPos = _cornerToScreenCoordinate(corners[i]);
        final isSelected = i == dragIndex;
        
        // 外圈
        final outerPaint = Paint()..color = Colors.blue;
        canvas.drawCircle(screenPos, isSelected ? 12 : 10, outerPaint);
        
        // 内圈
        final innerPaint = Paint()..color = Colors.white;
        canvas.drawCircle(screenPos, isSelected ? 8 : 6, innerPaint);
      }
    }
  }

  Offset _cornerToScreenCoordinate(Point corner) {
    return Offset(
      imageOffset.dx + corner.x * displaySize.width,
      imageOffset.dy + corner.y * displaySize.height,
    );
  }

  @override
  bool shouldRepaint(CropOverlayPainter oldDelegate) {
    return loadedImage != oldDelegate.loadedImage ||
           corners != oldDelegate.corners ||
           dragIndex != oldDelegate.dragIndex ||
           displaySize != oldDelegate.displaySize ||
           imageOffset != oldDelegate.imageOffset;
  }
}