import 'dart:io';
import 'dart:math' as math;
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:pdfx/pdfx.dart' as pdfx;
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart' as pdf;
import 'package:syncfusion_flutter_pdf/pdf.dart' as sPdf;

import '../state/app_state.dart';
import '../models/signature.dart';
import '../services/signature_service.dart';
import 'signature_management_screen.dart';
import 'signature_creation_screen.dart';
import '../../l10n/app_localizations.dart';

class PdfViewerScreen extends StatefulWidget {
  final String filePath;
  final String fileName;
  final bool initialEditMode;
  final bool initialRotateOnce;

  const PdfViewerScreen({
    super.key,
    required this.filePath,
    required this.fileName,
    this.initialEditMode = false,
    this.initialRotateOnce = false,
  });

  @override
  State<PdfViewerScreen> createState() => _PdfViewerScreenState();
}

class _PdfViewerScreenState extends State<PdfViewerScreen> {
  // Viewer
  final PdfViewerController _viewerController = PdfViewerController();
  PdfTextSearchResult _sfSearchResult = PdfTextSearchResult();
  // Bump this to force rebuilding the viewer when bytes change
  int _viewerVersion = 0;

  bool _isEditMode = false;
  int _currentPage = 1;
  int _totalPages = 1;
  final Map<int, Size> _pageSizes = {};
  final TextEditingController _searchController = TextEditingController();
  Uint8List? _docBytes; // if set, viewer uses memory source instead of file
  bool _isBusy = false;
  bool _hasUnsavedChanges = false;
  bool _pendingInitialRotate = false;

  // Edit tools
  EditTool _activeTool = EditTool.none;

  // Debug helper
  void _log(Object msg) {
    if (kDebugMode) debugPrint('[PdfViewer] $msg');
  }

  // Helper to determine if text selection should be enabled
  bool get _shouldEnableTextSelection {
    return !_isEditMode;
  }

  // Ink strokes per page (normalized points)
  final Map<int, List<_InkStroke>> _strokesByPage = {};
  List<Offset> _currentStroke = [];
  Color _strokeColor = Colors.red;
  double _strokeWidth = 3.0;

  // Signatures per page
  final Map<int, List<_SignatureAnno>> _signaturesByPage = {};
  _SignatureAnno? _placingSignature;
  bool _isResizingSignature = false;
  String _resizeCorner = '';
  _SignatureAnno? _resizingSignature;
  // Overlay sizes per page (for coordinate conversion when saving)
  final Map<int, Size> _overlaySizes = {};

  // Rotate & thumbnails state
  final Map<int, int> _pageRotations = {}; // page -> degrees (0/90/180/270)
  bool _showThumbnails = false;
  final Map<int, Uint8List> _thumbCache = {}; // page -> thumbnail bytes

  @override
  void initState() {
    super.initState();
    _sfSearchResult.addListener(_onSearchChanged);
    
    // Auto-enable edit mode if requested
    if (widget.initialEditMode) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _toggleEditMode();
      });
    }

    // Schedule initial rotate after document load if requested
    _pendingInitialRotate = widget.initialRotateOnce;
  }

  @override
  void dispose() {
    _sfSearchResult.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    if (!mounted) return;
    setState(() {});
  }

  

  void _toggleEditMode() {
    setState(() {
      _isEditMode = !_isEditMode;
      _activeTool = EditTool.none;
      _log('Toggle edit mode -> $_isEditMode');
      
      if (kDebugMode) {
        debugPrint('[PdfViewer] Edit mode toggled to: $_isEditMode');
        debugPrint('  - Active tool reset to: $_activeTool');
      }
      
      // Cancel any placing signature when exiting edit mode
      _placingSignature = null;
      // NOTE: Removed forced zoom reset to allow zooming in markup mode
    });
  }

  void _toggleThumbnails() {
    setState(() => _showThumbnails = !_showThumbnails);
  }

  void _onDocumentLoaded(PdfDocumentLoadedDetails details) {
    setState(() {
      _totalPages = details.document.pages.count;
      _pageSizes.clear();
      for (int i = 0; i < _totalPages; i++) {
        _pageSizes[i + 1] = details.document.pages[i].size;
      }
    });

    // Perform a one-time initial rotate if requested
    if (_pendingInitialRotate) {
      _pendingInitialRotate = false;
      // Delay slightly to ensure viewer controller is ready
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _rotateCurrentPage();
      });
    }
  }

  Future<void> _rotateCurrentPage() async {
    // Update rotation state
    final current = _pageRotations[_currentPage] ?? 0;
    final next = (current + 90) % 360;
    _pageRotations[_currentPage] = next;

    // Rebuild a rotated PDF into memory and reload viewer
    setState(() => _isBusy = true);
    try {
      final bytes = await _buildRotatedPdfBytes();
      setState(() {
        _docBytes = bytes;
        _hasUnsavedChanges = true;
      });
      if (mounted) {
        // Force viewer rebuild key and keep current page
        setState(() {
          _viewerVersion++;
        });
        _viewerController.jumpToPage(_currentPage);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.pageRotated(_currentPage, next))),
        );
      }
    } catch (e) {
      if (kDebugMode) debugPrint('Rotate failed: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.rotationFailed)),
        );
      }
    } finally {
      if (mounted) setState(() => _isBusy = false);
    }
  }

  Future<Uint8List> _buildRotatedPdfBytes() async {
    // Open original file (always start from the on-disk source to avoid drift)
    final doc = await pdfx.PdfDocument.openFile(widget.filePath);
    final out = pw.Document();

    for (int i = 1; i <= doc.pagesCount; i++) {
      final page = await doc.getPage(i);
      final render = await page.render(width: page.width, height: page.height);
      final bytes = render?.bytes;
      page.close();
      if (bytes == null) {
        // Create an empty page to keep page count consistent
        out.addPage(pw.Page(
            pageFormat:
                pdf.PdfPageFormat(page.width.toDouble(), page.height.toDouble()),
            build: (_) => pw.Container()));
        continue;
      }

      final rotation = _pageRotations[i] ?? 0;
      final img = pw.MemoryImage(bytes);
      final srcW = page.width.toDouble();
      final srcH = page.height.toDouble();
      final isSwap = rotation == 90 || rotation == 270;
      final destW = isSwap ? srcH : srcW;
      final destH = isSwap ? srcW : srcH;

      out.addPage(
        pw.Page(
          pageFormat: pdf.PdfPageFormat(destW, destH),
          build: (_) {
            final angle = rotation * 3.141592653589793 / 180.0;
            return pw.Stack(children: [
              pw.Positioned.fill(
                child: pw.Transform.rotate(
                  angle: angle,
                  child: pw.Center(
                    child: pw.Image(img, fit: pw.BoxFit.contain),
                  ),
                ),
              ),
            ]);
          },
        ),
      );
    }

    final bytes = await out.save();
    doc.close();
    return Uint8List.fromList(bytes);
  }

  Future<Uint8List?> _getThumbnail(int page) async {
    if (_thumbCache.containsKey(page)) return _thumbCache[page]!;
    try {
      pdfx.PdfDocument doc;
      if (_docBytes != null) {
        doc = await pdfx.PdfDocument.openData(_docBytes!);
      } else {
        doc = await pdfx.PdfDocument.openFile(widget.filePath);
      }
      final p = await doc.getPage(page);
      // Keep aspect ratio while targeting ~100px height
      final targetH = 100.0;
      final ratio = targetH / p.height;
      final w = (p.width * ratio).clamp(60.0, 300.0);
      final h = (p.height * ratio).clamp(60.0, 300.0);
      final img = await p.render(width: w, height: h);
      p.close();
      doc.close();
      if (img?.bytes != null) {
        _thumbCache[page] = img!.bytes;
        return img.bytes;
      }
    } catch (e) {
      if (kDebugMode) debugPrint('Thumbnail render failed for page $page: $e');
    }
    return null;
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.searchInPdf),
        content: ConstrainedBox(
          constraints: const BoxConstraints(minWidth: 280, maxWidth: 400),
          child: TextField(
            controller: _searchController,
            autofocus: true,
            decoration: InputDecoration(
              hintText: AppLocalizations.of(context)!.searchText,
              border: const OutlineInputBorder(),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            onSubmitted: (v) {
              Navigator.of(context).pop();
              _performSearch(v);
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context)!.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performSearch(_searchController.text);
            },
            child: Text(AppLocalizations.of(context)!.search),
          ),
        ],
      ),
    );
  }

  // Switch back to the original Office viewer and open its search UI
  void _switchToOfficeSearch() {
    if (mounted) {
      Navigator.of(context).pop('open_office_search');
    }
  }

  Future<void> _performSearch(String query) async {
    if (query.isEmpty) return;
    _sfSearchResult.clear();
    // Use default search options for broader compatibility across Syncfusion versions
    final result = _viewerController.searchText(query);
    // Attach listener to the new result so UI updates immediately/asynchronously
    _sfSearchResult.removeListener(_onSearchChanged);
    _sfSearchResult = result;
    _sfSearchResult.addListener(_onSearchChanged);
  }

  void _goToPreviousSearchResult() => _sfSearchResult.previousInstance();
  void _goToNextSearchResult() => _sfSearchResult.nextInstance();
  void _clearSearch() => _sfSearchResult.clear();

  // 坐标转换工具方法：PDF坐标系 <-> Overlay像素坐标系
  
  /// PDF坐标点转换为Overlay像素坐标点
  Offset _pdfToOverlay(Offset pdfPoint, Size pdfSize, Size overlaySize) {
    if (pdfSize.width == 0 || pdfSize.height == 0) return Offset.zero;
    return Offset(
      pdfPoint.dx * overlaySize.width / pdfSize.width,
      pdfPoint.dy * overlaySize.height / pdfSize.height,
    );
  }
  
  /// Overlay像素坐标点转换为PDF坐标点
  Offset _overlayToPdf(Offset overlayPoint, Size pdfSize, Size overlaySize) {
    if (overlaySize.width == 0 || overlaySize.height == 0) return Offset.zero;
    return Offset(
      overlayPoint.dx * pdfSize.width / overlaySize.width,
      overlayPoint.dy * pdfSize.height / overlaySize.height,
    );
  }
  
  /// PDF尺寸转换为Overlay像素尺寸
  Size _pdfSizeToOverlay(Size pdfSizeValue, Size pdfPageSize, Size overlaySize) {
    if (pdfPageSize.width == 0 || pdfPageSize.height == 0) return Size.zero;
    return Size(
      pdfSizeValue.width * overlaySize.width / pdfPageSize.width,
      pdfSizeValue.height * overlaySize.height / pdfPageSize.height,
    );
  }
  
  /// Overlay像素尺寸转换为PDF尺寸
  Size _overlaySizeToPdf(Size overlaySizeValue, Size pdfPageSize, Size overlaySize) {
    if (overlaySize.width == 0 || overlaySize.height == 0) return Size.zero;
    return Size(
      overlaySizeValue.width * pdfPageSize.width / overlaySize.width,
      overlaySizeValue.height * pdfPageSize.height / overlaySize.height,
    );
  }

  // Edit: drawing actions
  void _onPanDraw(Offset localPos, Size overlaySize) {
    if (_activeTool != EditTool.draw) return;
    if (overlaySize.width == 0 || overlaySize.height == 0) return;
    final norm = Offset(
        localPos.dx / overlaySize.width, localPos.dy / overlaySize.height);
    setState(() {
      _currentStroke.add(norm);
    });
    if (_currentStroke.length % 6 == 1) {
      final ndx = norm.dx.toStringAsFixed(3);
      final ndy = norm.dy.toStringAsFixed(3);
      _log('Drawing: page=$_currentPage local=$localPos overlay=$overlaySize norm=($ndx,$ndy) len=${_currentStroke.length}');
    }
  }

  void _onPanEnd(Size overlaySize) {
    if (_activeTool != EditTool.draw || _currentStroke.length < 2) {
      if (_currentStroke.isNotEmpty) {
        _log('Pan end ignored: points=${_currentStroke.length}');
      }
      _currentStroke = [];
      return;
    }
    _log('Pan end commit stroke: points=${_currentStroke.length} page=$_currentPage');
    final list = _strokesByPage.putIfAbsent(_currentPage, () => []);
    list.add(_InkStroke(
        points: List.of(_currentStroke),
        color: _strokeColor,
        width: _strokeWidth));
    _currentStroke = [];
    setState(() {
      _hasUnsavedChanges = true;
    });
  }

  void _onEraseAt(Offset localPos, Size overlaySize) {
    if (_activeTool != EditTool.eraser) return;
    
    final pdfPageSize = _pageSizes[_currentPage] ?? Size.zero;
    if (pdfPageSize == Size.zero) return;
    
    // Convert overlay position to PDF coordinates for signature deletion
    final pdfPos = _overlayToPdf(localPos, pdfPageSize, overlaySize);
    
    // Keep normalized position for ink stroke deletion (they still use normalized coordinates)
    final normalizedPos = Offset(
        localPos.dx / overlaySize.width, localPos.dy / overlaySize.height);
    
    bool hasChanges = false;
    
    // Check for signature deletion first (using PDF coordinates)
    final pageSignatures = _signaturesByPage[_currentPage];
    if (pageSignatures != null) {
      final signaturesToRemove = <_SignatureAnno>[];
      for (final signature in pageSignatures) {
        if (signature.hitTest(pdfPos)) {
          signaturesToRemove.add(signature);
          hasChanges = true;
        }
      }
      for (final signature in signaturesToRemove) {
        pageSignatures.remove(signature);
      }
    }
    
    // Then check for ink stroke deletion
    final pageStrokes = _strokesByPage[_currentPage];
    if (pageStrokes != null) {
      final initialCount = pageStrokes.length;
      pageStrokes.removeWhere((stroke) => stroke.hitTest(normalizedPos, overlaySize));
      if (pageStrokes.length < initialCount) hasChanges = true;
    }
    
    if (hasChanges) {
      setState(() {
        _hasUnsavedChanges = true;
      });
    }
  }

  Future<void> _showSignatureSelection() async {
    final selectedSignature = await Navigator.of(context).push<Signature>(
      MaterialPageRoute(
        builder: (context) => const SignatureSelectionScreen(),
      ),
    );
    
    if (selectedSignature != null && mounted) {
      setState(() {
        _activeTool = EditTool.signature;
        // Create a default sized signature annotation for placement using PDF coordinates
        final pdfPageSize = _pageSizes[_currentPage] ?? Size.zero;
        if (pdfPageSize != Size.zero) {
          // Default size: 20% of page width, maintaining aspect ratio
          final defaultWidth = pdfPageSize.width * 0.2;
          final defaultHeight = defaultWidth * selectedSignature.canvasSize.height / selectedSignature.canvasSize.width;
          final defaultSize = Size(defaultWidth, defaultHeight);
          
          // Center-ish position in PDF coordinates
          final centerPosition = Offset(
            pdfPageSize.width * 0.4,
            pdfPageSize.height * 0.4,
          );
          
          _placingSignature = _SignatureAnno(
            signature: selectedSignature,
            position: centerPosition,
            size: defaultSize,
            isSelected: true,
          );
        }
      });
    }
  }

  void _onSignatureMove(Offset delta, Size overlaySize) {
    if (_placingSignature != null) {
      final pdfPageSize = _pageSizes[_currentPage] ?? Size.zero;
      if (pdfPageSize == Size.zero) return;
      
      // Convert overlay pixel delta to PDF coordinate delta
      final pdfDelta = _overlayToPdf(delta, pdfPageSize, overlaySize) - _overlayToPdf(Offset.zero, pdfPageSize, overlaySize);
      
      setState(() {
        _placingSignature!.position = Offset(
          (_placingSignature!.position.dx + pdfDelta.dx).clamp(0.0, pdfPageSize.width - _placingSignature!.size.width),
          (_placingSignature!.position.dy + pdfDelta.dy).clamp(0.0, pdfPageSize.height - _placingSignature!.size.height),
        );
        _hasUnsavedChanges = true;
      });
    }
  }

  void _onSignatureResize(Size newSize, Size overlaySize) {
    if (_placingSignature != null) {
      final pdfPageSize = _pageSizes[_currentPage] ?? Size.zero;
      if (pdfPageSize == Size.zero) return;
      
      // Convert overlay pixel size to PDF coordinate size
      final pdfSize = _overlaySizeToPdf(newSize, pdfPageSize, overlaySize);
      
      // Clamp size within reasonable bounds (5% to 90% of page size)
      final clampedSize = Size(
        pdfSize.width.clamp(pdfPageSize.width * 0.05, pdfPageSize.width * 0.9),
        pdfSize.height.clamp(pdfPageSize.height * 0.05, pdfPageSize.height * 0.9),
      );
      
      // Maintain aspect ratio
      final signature = _placingSignature!.signature;
      final aspectRatio = signature.canvasSize.width / signature.canvasSize.height;
      final correctedSize = Size(
        clampedSize.width,
        clampedSize.width / aspectRatio,
      );
      
      setState(() {
        _placingSignature!.size = correctedSize;
        _hasUnsavedChanges = true;
      });
    }
  }

  void _onResizeStart(String corner, _SignatureAnno sigAnno) {
    setState(() {
      _isResizingSignature = true;
      _resizeCorner = corner;
      _resizingSignature = sigAnno;
    });
  }

  void _onResizeUpdate(Offset delta, Size overlaySize) {
    if (_resizingSignature == null) return;
    
    final signature = _resizingSignature!;
    final originalAspectRatio = signature.signature.canvasSize.width / signature.signature.canvasSize.height;
    final pdfPageSize = _pageSizes[_currentPage] ?? Size.zero;
    if (pdfPageSize == Size.zero) return;
    
    // Convert overlay pixel delta to PDF coordinate delta
    final pdfDelta = _overlayToPdf(delta, pdfPageSize, overlaySize) - _overlayToPdf(Offset.zero, pdfPageSize, overlaySize);
    
    // Calculate new size and position in PDF coordinates
    double newWidth = signature.size.width;
    double newHeight = signature.size.height;
    double newLeft = signature.position.dx;
    double newTop = signature.position.dy;
    
    switch (_resizeCorner) {
      case 'top-left':
        newWidth = signature.size.width - pdfDelta.dx;
        newHeight = newWidth / originalAspectRatio;
        newLeft = signature.position.dx + pdfDelta.dx;
        newTop = signature.position.dy + (signature.size.height - newHeight);
        break;
      case 'top-right':
        newWidth = signature.size.width + pdfDelta.dx;
        newHeight = newWidth / originalAspectRatio;
        newTop = signature.position.dy + (signature.size.height - newHeight);
        break;
      case 'bottom-left':
        newWidth = signature.size.width - pdfDelta.dx;
        newHeight = newWidth / originalAspectRatio;
        newLeft = signature.position.dx + pdfDelta.dx;
        break;
      case 'bottom-right':
        newWidth = signature.size.width + pdfDelta.dx;
        newHeight = newWidth / originalAspectRatio;
        break;
    }
    
    // Apply constraints in PDF coordinates (5% to 90% of page size)
    newWidth = newWidth.clamp(pdfPageSize.width * 0.05, pdfPageSize.width * 0.9);
    newHeight = newHeight.clamp(pdfPageSize.height * 0.05, pdfPageSize.height * 0.9);
    newLeft = newLeft.clamp(0.0, pdfPageSize.width - newWidth);
    newTop = newTop.clamp(0.0, pdfPageSize.height - newHeight);
    
    setState(() {
      signature.size = Size(newWidth, newHeight);
      signature.position = Offset(newLeft, newTop);
      _hasUnsavedChanges = true;
    });
  }

  void _onResizeEnd() {
    setState(() {
      _isResizingSignature = false;
      _resizeCorner = '';
      _resizingSignature = null;
    });
  }

  void _onSignatureTap(Offset tapPos, Size overlaySize) {
    // Only handle selection of existing signatures, not placement confirmation
    // Signature placement is now only confirmed via the "确定放置" button
    final pdfPageSize = _pageSizes[_currentPage] ?? Size.zero;
    if (pdfPageSize == Size.zero) return;
    
    // Convert overlay pixel position to PDF coordinates
    final pdfPos = _overlayToPdf(tapPos, pdfPageSize, overlaySize);
    
    final pageSignatures = _signaturesByPage[_currentPage];
    if (pageSignatures != null) {
      for (final sig in pageSignatures) {
        if (sig.hitTest(pdfPos)) {
          setState(() {
            // Deselect all signatures first
            for (final signatures in _signaturesByPage.values) {
              for (final s in signatures) {
                s.isSelected = false;
              }
            }
            sig.isSelected = true;
          });
          break;
        }
      }
    }
  }

  Future<void> _handleBackPressed() async {
    if (!_hasUnsavedChanges) {
      if (mounted) Navigator.of(context).pop();
      return;
    }
    final action = await _showUnsavedChangesDialog();
    if (action == _UnsavedAction.discard && mounted) {
      Navigator.of(context).pop();
    } else if (action == _UnsavedAction.save) {
      await _saveAnnotationsToFile();
      if (mounted && !_hasUnsavedChanges) {
        Navigator.of(context).pop();
      }
    }
  }

  Future<_UnsavedAction?> _showUnsavedChangesDialog() {
    return showDialog<_UnsavedAction>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.unsavedChanges),
        content: Text(AppLocalizations.of(context)!.unsavedChangesMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(_UnsavedAction.cancel),
            child: Text(AppLocalizations.of(context)!.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(_UnsavedAction.discard),
            child: Text(AppLocalizations.of(context)!.discardChanges),
          ),
          ElevatedButton(
            onPressed: _isBusy
                ? null
                : () => Navigator.of(context).pop(_UnsavedAction.save),
            child: Text(AppLocalizations.of(context)!.save),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final brand = context.watch<AppState>().activeBrandTheme;

    return PopScope(
      canPop: !_hasUnsavedChanges,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop && _hasUnsavedChanges) {
          await _handleBackPressed();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: brand.brandColor,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          title: Text(widget.fileName),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: _handleBackPressed,
          ),
          actions: [
            // Save annotations to the file (with unsaved indicator)
            Stack(
              children: [
                IconButton(
                  tooltip: AppLocalizations.of(context)!.save,
                  icon: const Icon(Icons.save),
                  onPressed: _isBusy ? null : _saveAnnotationsToFile,
                ),
                if (_hasUnsavedChanges && !_isBusy)
                  Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: Colors.orange,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
        body: Stack(
          children: [
            // Syncfusion PDF Viewer in continuous layout with vertical scrolling
            Positioned.fill(
              child: (_docBytes == null)
                  ? SfPdfViewer.file(
                      File(widget.filePath),
                      key: ValueKey('viewer_${_viewerVersion}'),
                      controller: _viewerController,
                      enableTextSelection: _shouldEnableTextSelection,
                      pageLayoutMode: PdfPageLayoutMode.continuous,
                      scrollDirection: PdfScrollDirection.vertical,
                      canShowScrollStatus: true,
                      onDocumentLoaded: _onDocumentLoaded,
                      onPageChanged: (details) {
                        setState(() {
                          _currentPage = details.newPageNumber;
                        });
                      },
                    )
                  : SfPdfViewer.memory(
                      _docBytes!,
                      key: ValueKey('viewer_${_viewerVersion}'),
                      controller: _viewerController,
                      enableTextSelection: _shouldEnableTextSelection,
                      pageLayoutMode: PdfPageLayoutMode.continuous,
                      scrollDirection: PdfScrollDirection.vertical,
                      canShowScrollStatus: true,
                      onDocumentLoaded: _onDocumentLoaded,
                      onPageChanged: (details) {
                        setState(() {
                          _currentPage = details.newPageNumber;
                        });
                      },
                    ),
            ),
            if (_isBusy)
              Positioned.fill(
                child: Container(
                  color: Colors.black26,
                  child: const Center(child: CircularProgressIndicator()),
                ),
              ),
            // Thumbnails sidebar
            if (_showThumbnails)
              Positioned(
                right: 0,
                top: 0,
                bottom: 0,
                width: 120,
                child: Container(
                  color: Colors.grey.shade50,
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        color: Theme.of(context).primaryColor,
                        child: Row(
                          children: [
                            Text(AppLocalizations.of(context)!.thumbnails,
                                style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold)),
                            const Spacer(),
                            IconButton(
                              icon: const Icon(Icons.close,
                                  color: Colors.white, size: 20),
                              onPressed: _toggleThumbnails,
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: ListView.builder(
                          itemCount: _totalPages,
                          itemBuilder: (context, index) {
                            final pageNumber = index + 1;
                            final isCurrent = pageNumber == _currentPage;
                            return GestureDetector(
                              onTap: () =>
                                  _viewerController.jumpToPage(pageNumber),
                              child: Container(
                                margin: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: isCurrent
                                          ? Theme.of(context).primaryColor
                                          : Colors.grey.shade300,
                                      width: isCurrent ? 2 : 1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 100,
                                      child: FutureBuilder<Uint8List?>(
                                        future: _getThumbnail(pageNumber),
                                        builder: (context, snapshot) {
                                          if (snapshot.connectionState ==
                                              ConnectionState.waiting) {
                                            return const Center(
                                                child: SizedBox(
                                                    width: 18,
                                                    height: 18,
                                                    child:
                                                        CircularProgressIndicator(
                                                            strokeWidth: 2)));
                                          }
                                          final bytes = snapshot.data;
                                          if (bytes == null) {
                                            return const Center(
                                                child: Icon(
                                                    Icons.picture_as_pdf,
                                                    color: Colors.grey));
                                          }
                                          return Image.memory(bytes,
                                              fit: BoxFit.contain);
                                        },
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(2.0),
                                      child: Text(
                                        pageNumber.toString(),
                                        style: TextStyle(
                                            fontSize: 10,
                                            color: isCurrent
                                                ? Theme.of(context).primaryColor
                                                : Colors.grey.shade600),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            // Editing overlay (only for current page)
            if (_isEditMode)
              Positioned.fill(
                child: _EditOverlay(
                  page: _currentPage,
                  pageSize: _pageSizes[_currentPage] ?? Size.zero,
                  strokesByPage: _strokesByPage,
                  signaturesByPage: _signaturesByPage,
                  placingSignature: _placingSignature,
                  currentStroke: _currentStroke,
                  activeTool: _activeTool,
                  strokeColor: _strokeColor,
                  strokeWidth: _strokeWidth,
                  isResizingSignature: _isResizingSignature,
                  resizeCorner: _resizeCorner,
                  resizingSignature: _resizingSignature,
                  onPanDraw: _onPanDraw,
                  onPanEnd: _onPanEnd,
                  onEraseAt: _onEraseAt,
                  onSignatureMove: _onSignatureMove,
                  onSignatureResize: _onSignatureResize,
                  onSignatureTap: _onSignatureTap,
                  onResizeStart: _onResizeStart,
                  onResizeUpdate: _onResizeUpdate,
                  onResizeEnd: _onResizeEnd,
                  onOverlaySize: (s) => _overlaySizes[_currentPage] = s,
                  buildSignatureWidget: _buildSignatureWidget,
                ),
              ),
          ],
        ),
        bottomNavigationBar: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_sfSearchResult.totalInstanceCount > 0)
              _buildSfSearchBar(context),
            // Four-button toolbar: Rotate, Edit, Search, Thumbnails (hidden in edit mode)
            if (!_isEditMode)
              Container(
                padding: const EdgeInsets.all(12),
                color: Colors.white,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _toolbarButton(
                      icon: Icons.rotate_right,
                      label: AppLocalizations.of(context)!.rotate,
                      onPressed: _isBusy ? null : _rotateCurrentPage,
                    ),
                    _toolbarButton(
                      icon: Icons.edit,
                      label: AppLocalizations.of(context)!.edit,
                      onPressed: _toggleEditMode,
                      selected: _isEditMode,
                    ),
                    _toolbarButton(
                      icon: Icons.search,
                      label: AppLocalizations.of(context)!.search,
                      onPressed: _switchToOfficeSearch,
                    ),
                    _toolbarButton(
                      icon: Icons.view_list,
                      label: AppLocalizations.of(context)!.thumbnails,
                      onPressed: _toggleThumbnails,
                      selected: _showThumbnails,
                    ),
                  ],
                ),
              ),
            if (_isEditMode) _buildEditToolbar(context, brand.brandColor),
          ],
        ),
      ),
    );
  }

  Future<void> _saveAnnotationsToFile() async {
    setState(() => _isBusy = true);
    try {
      // Use currently displayed bytes if present (to preserve rotations), else read from disk
      final originalPath = widget.filePath;
      final Uint8List source =
          _docBytes ?? await File(originalPath).readAsBytes();

      final sPdf.PdfDocument doc = sPdf.PdfDocument(inputBytes: source);

      // Add signatures
      for (final entry in _signaturesByPage.entries) {
        final pageIndex = entry.key - 1;
        if (pageIndex < 0 || pageIndex >= doc.pages.count) continue;
        final page = doc.pages[pageIndex];
        final g = page.graphics;
        
        for (final sigAnno in entry.value) {
          if (sigAnno.signature.strokes.isEmpty) continue;
          
          // Signature position/size are already in PDF coordinates, use directly
          final sigLeft = sigAnno.position.dx;
          final sigTop = sigAnno.position.dy;
          final sigWidth = sigAnno.size.width;
          final sigHeight = sigAnno.size.height;
          
          // 保持宽高比的统一缩放
          final scaleX = sigWidth / sigAnno.signature.canvasSize.width;
          final scaleY = sigHeight / sigAnno.signature.canvasSize.height;
          final uniformScale = math.min(scaleX, scaleY); // 使用较小的缩放因子保持比例
          
          // 计算实际绘制尺寸和居中偏移
          final actualWidth = sigAnno.signature.canvasSize.width * uniformScale;
          final actualHeight = sigAnno.signature.canvasSize.height * uniformScale;
          final offsetX = (sigWidth - actualWidth) / 2;
          final offsetY = (sigHeight - actualHeight) / 2;
          
          // Draw each stroke of the signature
          for (final stroke in sigAnno.signature.strokes) {
            if (stroke.points.length < 2) continue;
            final pen = sPdf.PdfPen(
                sPdf.PdfColor(stroke.color.red, stroke.color.green, stroke.color.blue),
                width: stroke.strokeWidth * uniformScale);
                
            for (int i = 1; i < stroke.points.length; i++) {
              final a = stroke.points[i - 1];
              final b = stroke.points[i];
              final x1 = sigLeft + offsetX + a.dx * uniformScale;
              final y1 = sigTop + offsetY + a.dy * uniformScale;
              final x2 = sigLeft + offsetX + b.dx * uniformScale;
              final y2 = sigTop + offsetY + b.dy * uniformScale;
              
              try {
                // ignore: avoid_dynamic_calls
                (g as dynamic).drawLine(pen, x1, y1, x2, y2);
              } catch (_) {
                // ignore: avoid_dynamic_calls
                (g as dynamic).drawLine(pen, Offset(x1, y1), Offset(x2, y2));
              }
            }
          }
        }
      }

      // Add ink strokes
      for (final entry in _strokesByPage.entries) {
        final pageIndex = entry.key - 1;
        if (pageIndex < 0 || pageIndex >= doc.pages.count) continue;
        final page = doc.pages[pageIndex];
        final size = page.getClientSize();
        final g = page.graphics;
        // Determine overlay size used when editing this page
        final overlay = _overlaySizes[entry.key] ?? Size(size.width, size.height);
        final scaleToPageX = overlay.width == 0 ? 1.0 : size.width / overlay.width;
        final scaleToPageY = overlay.height == 0 ? 1.0 : size.height / overlay.height;
        for (final stroke in entry.value) {
          if (stroke.points.length < 2) continue;
          final pen = sPdf.PdfPen(
              sPdf.PdfColor(
                  stroke.color.red, stroke.color.green, stroke.color.blue),
              width: stroke.width);
          for (int i = 1; i < stroke.points.length; i++) {
            final a = stroke.points[i - 1];
            final b = stroke.points[i];
            // Points are overlay-normalized; convert to page coordinates
            final x1 = (a.dx * overlay.width) * scaleToPageX;
            final y1 = (a.dy * overlay.height) * scaleToPageY;
            final x2 = (b.dx * overlay.width) * scaleToPageX;
            final y2 = (b.dy * overlay.height) * scaleToPageY;
            // Use coordinates directly to avoid type mismatch across versions
            try {
              // Some versions support drawLine(PdfPen pen, double x1, double y1, double x2, double y2)
              // ignore: argument_type_not_assignable
              // @ts-ignore - dynamic invocation fallback
              // We attempt both signatures using dynamic.
              // First: with doubles
              // ignore: avoid_dynamic_calls
              // ignore: invalid_use_of_protected_member
              (g as dynamic).drawLine(pen, x1, y1, x2, y2);
            } catch (_) {
              // Fallback: if Offset signature exists
              // ignore: avoid_dynamic_calls
              (g as dynamic).drawLine(pen, Offset(x1, y1), Offset(x2, y2));
            }
          }
        }
      }

      final bytes = Uint8List.fromList(await doc.save());
      doc.dispose();

      // Backup and overwrite original file
      final file = File(originalPath);
      final backup = File('$originalPath.bak');
      try {
        if (await backup.exists()) await backup.delete();
      } catch (_) {}
      await file.copy(backup.path);
      await file.writeAsBytes(bytes, flush: true);

      // Clear annotations from memory as they are now flattened in the document
      _strokesByPage.clear();
      _signaturesByPage.clear();

      setState(() {
        _docBytes = bytes;
        _thumbCache.clear();
        _viewerVersion++;
        _hasUnsavedChanges = false;
      });
      _viewerController.jumpToPage(_currentPage);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.pdfSaved)),
        );
      }
    } catch (e) {
      if (kDebugMode) debugPrint('Save annotations failed: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.pdfSaveFailed)),
        );
      }
    } finally {
      if (mounted) setState(() => _isBusy = false);
    }
  }

  // Search bar for Syncfusion search
  Widget _buildSfSearchBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.grey.shade100,
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: _clearSearch,
            tooltip: AppLocalizations.of(context)!.closeSearch,
          ),
          Expanded(
            child: Center(
              child: Text(
                '${_sfSearchResult.currentInstanceIndex} / ${_sfSearchResult.totalInstanceCount}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.keyboard_arrow_up),
            onPressed:
                _sfSearchResult.hasResult ? _goToPreviousSearchResult : null,
            tooltip: AppLocalizations.of(context)!.previous,
          ),
          IconButton(
            icon: const Icon(Icons.keyboard_arrow_down),
            onPressed: _sfSearchResult.hasResult ? _goToNextSearchResult : null,
            tooltip: AppLocalizations.of(context)!.next,
          ),
        ],
      ),
    );
  }


  // Edit toolbar
  Widget _buildEditToolbar(BuildContext context, Color brandColor) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Top row with back arrow to exit edit mode
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                tooltip: _placingSignature != null ? AppLocalizations.of(context)!.cancelSignatureEdit : AppLocalizations.of(context)!.back,
                onPressed: () {
                  if (_placingSignature != null) {
                    // Cancel signature placement
                    setState(() {
                      _placingSignature = null;
                      _activeTool = EditTool.none;
                    });
                  } else {
                    // Exit edit mode
                    _toggleEditMode();
                  }
                },
              ),
              const SizedBox(width: 8),
              Text(AppLocalizations.of(context)!.editMode, style: Theme.of(context).textTheme.titleMedium),
              const Spacer(),
              Text(AppLocalizations.of(context)!.pageIndicator(_currentPage, _totalPages),
                  style: Theme.of(context).textTheme.bodySmall),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _toolbarButton(
                icon: Icons.brush,
                label: AppLocalizations.of(context)!.draw,
                onPressed: _placingSignature != null
                    ? null
                    : () {
                        setState(() => _activeTool = EditTool.draw);
                        _log('Active tool -> DRAW');
                      },
                selected: _activeTool == EditTool.draw,
              ),
              _toolbarButton(
                icon: Icons.cleaning_services,
                label: AppLocalizations.of(context)!.eraser,
                onPressed: _placingSignature != null ? null : () {
                  setState(() => _activeTool = EditTool.eraser);
                },
                selected: _activeTool == EditTool.eraser,
              ),
              _toolbarButton(
                icon: Icons.draw,
                label: AppLocalizations.of(context)!.signature,
                onPressed: _placingSignature != null ? null : () => _showSignatureSelection(),
                selected: _activeTool == EditTool.signature,
              ),
            ],
          ),
          if (_activeTool == EditTool.draw) ...[
            const SizedBox(height: 8),
            _buildDrawSettings(),
          ],
          if (_activeTool == EditTool.signature) ...[
            const SizedBox(height: 8),
            _buildSignatureSettings(),
          ],
        ],
      ),
    );
  }

  

  Widget _buildDrawSettings() {
    return Row(
      children: [
        Text(AppLocalizations.of(context)!.brushColor),
        const SizedBox(width: 8),
        _colorDot(Colors.red, _strokeColor == Colors.red,
            () => setState(() => _strokeColor = Colors.red)),
        _colorDot(Colors.blue, _strokeColor == Colors.blue,
            () => setState(() => _strokeColor = Colors.blue)),
        _colorDot(Colors.green, _strokeColor == Colors.green,
            () => setState(() => _strokeColor = Colors.green)),
        _colorDot(Colors.black, _strokeColor == Colors.black,
            () => setState(() => _strokeColor = Colors.black)),
        const SizedBox(width: 16),
        Text(AppLocalizations.of(context)!.thickness),
        Expanded(
          child: Slider(
            min: 1,
            max: 16,
            value: _strokeWidth,
            onChanged: (v) => setState(() => _strokeWidth = v),
          ),
        ),
        Text('${_strokeWidth.toStringAsFixed(0)}'),
      ],
    );
  }

  Widget _buildSignatureSettings() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (_placingSignature != null) ...[
          ElevatedButton(
            onPressed: () {
              // Confirm placement
              final pageSignatures = _signaturesByPage.putIfAbsent(_currentPage, () => []);
              pageSignatures.add(_SignatureAnno(
                signature: _placingSignature!.signature,
                position: _placingSignature!.position,
                size: _placingSignature!.size,
                isSelected: false,
              ));
              
              setState(() {
                _placingSignature = null;
                _activeTool = EditTool.none;
                _hasUnsavedChanges = true;
              });
            },
            child: Text(AppLocalizations.of(context)!.confirmPlacement),
          ),
          const SizedBox(width: 12),
          OutlinedButton(
            onPressed: () {
              setState(() {
                _placingSignature = null;
                _activeTool = EditTool.none;
              });
            },
            child: Text(AppLocalizations.of(context)!.cancel),
          ),
        ] else ...[
          Text(AppLocalizations.of(context)!.dragToAdjustHint),
        ]
      ],
    );
  }

  Widget _colorDot(Color color, bool selected, VoidCallback onTap) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: 22,
          height: 22,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            border: Border.all(
                color: selected ? Colors.orange : Colors.grey.shade400,
                width: selected ? 2 : 1),
          ),
        ),
      ),
    );
  }

  Widget _toolbarButton(
      {required IconData icon,
      required String label,
      VoidCallback? onPressed,
      bool selected = false}) {
    final color = selected ? Colors.orange : Colors.black87;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: onPressed,
          icon: Icon(icon, color: color),
        ),
        Text(label, style: TextStyle(color: color, fontSize: 12)),
      ],
    );
  }

  Widget _buildSignatureWidget(_SignatureAnno sigAnno, Size overlaySize) {
    // Signature position/size are in PDF coordinates. Convert to overlay pixels.
    final pdfPageSize = _pageSizes[_currentPage] ?? Size.zero;
    if (pdfPageSize == Size.zero) {
      return const SizedBox.shrink();
    }
    
    final overlayPos = _pdfToOverlay(sigAnno.position, pdfPageSize, overlaySize);
    final overlaySize_ = _pdfSizeToOverlay(sigAnno.size, pdfPageSize, overlaySize);
    
    final left = overlayPos.dx;
    final top = overlayPos.dy;
    final width = overlaySize_.width;
    final height = overlaySize_.height;
    final showHandles = sigAnno.isSelected || _placingSignature == sigAnno;

    return Positioned(
      left: left,
      top: top,
      width: width,
      height: height,
      child: Stack(
        children: [
          // Main signature gesture detector
          Positioned.fill(
            child: GestureDetector(
              onPanUpdate: (details) {
                // Only allow moving while placing and not resizing
                if (!_isResizingSignature && _placingSignature == sigAnno &&
                    _activeTool == EditTool.signature) {
                  _onSignatureMove(details.delta, overlaySize);
                }
              },
              onTap: (_activeTool == EditTool.eraser)
                  ? null
                  : () {
                      // Handle signature selection/tap when not erasing
                      final tapPos = Offset(left + width / 2, top + height / 2);
                      _onSignatureTap(tapPos, overlaySize);
                    },
              // Allow resize handles to take priority over main gesture detector
              behavior: HitTestBehavior.deferToChild,
              child: Container(
                decoration: BoxDecoration(
                  border: showHandles
                      ? Border.all(color: Colors.blue, width: 2)
                      : null,
                ),
                child: CustomPaint(
                  painter: _SignaturePainter(signature: sigAnno.signature),
                  size: Size(width, height),
                ),
              ),
            ),
          ),
          // Corner resize handles
          if (showHandles) ...[ 
            // Top-left handle
            Positioned(
              left: -6,
              top: -6,
              child: _buildResizeHandle('top-left', sigAnno, overlaySize),
            ),
            // Top-right handle
            Positioned(
              right: -6,
              top: -6,
              child: _buildResizeHandle('top-right', sigAnno, overlaySize),
            ),
            // Bottom-left handle
            Positioned(
              left: -6,
              bottom: -6,
              child: _buildResizeHandle('bottom-left', sigAnno, overlaySize),
            ),
            // Bottom-right handle
            Positioned(
              right: -6,
              bottom: -6,
              child: _buildResizeHandle('bottom-right', sigAnno, overlaySize),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildResizeHandle(String corner, _SignatureAnno sigAnno, Size overlaySize) {
    return GestureDetector(
      onPanStart: (details) {
        _onResizeStart(corner, sigAnno);
      },
      onPanUpdate: (details) {
        if (_isResizingSignature && _resizingSignature == sigAnno) {
          _onResizeUpdate(details.delta, overlaySize);
        }
      },
      onPanEnd: (_) {
        _onResizeEnd();
      },
      // Ensure this gesture detector takes priority
      behavior: HitTestBehavior.opaque,
      child: Container(
        width: 12,
        height: 12,
        decoration: BoxDecoration(
          color: Colors.blue,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 2),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 2,
              offset: Offset(1, 1),
            ),
          ],
        ),
      ),
    );
  }
}

// Edit overlay for ink drawing
class _EditOverlay extends StatefulWidget {
  final int page;
  final Size pageSize;
  final Map<int, List<_InkStroke>> strokesByPage;
  final Map<int, List<_SignatureAnno>> signaturesByPage;
  final _SignatureAnno? placingSignature;
  final List<Offset> currentStroke;
  final EditTool activeTool;
  final Color strokeColor;
  final double strokeWidth;
  final bool isResizingSignature;
  final String resizeCorner;
  final _SignatureAnno? resizingSignature;
  final void Function(Offset localPos, Size overlaySize) onPanDraw;
  final void Function(Size overlaySize) onPanEnd;
  final void Function(Offset localPos, Size overlaySize) onEraseAt;
  final void Function(Offset delta, Size overlaySize) onSignatureMove;
  final void Function(Size newSize, Size overlaySize) onSignatureResize;
  final void Function(Offset tapPos, Size overlaySize) onSignatureTap;
  final void Function(String corner, _SignatureAnno sigAnno) onResizeStart;
  final void Function(Offset delta, Size overlaySize) onResizeUpdate;
  final void Function() onResizeEnd;
  final ValueChanged<Size> onOverlaySize;
  final Widget Function(_SignatureAnno sigAnno, Size overlaySize) buildSignatureWidget;

  const _EditOverlay({
    required this.page,
    required this.pageSize,
    required this.strokesByPage,
    required this.signaturesByPage,
    required this.placingSignature,
    required this.currentStroke,
    required this.activeTool,
    required this.strokeColor,
    required this.strokeWidth,
    required this.isResizingSignature,
    required this.resizeCorner,
    required this.resizingSignature,
    required this.onPanDraw,
    required this.onPanEnd,
    required this.onEraseAt,
    required this.onSignatureMove,
    required this.onSignatureResize,
    required this.onSignatureTap,
    required this.onResizeStart,
    required this.onResizeUpdate,
    required this.onResizeEnd,
    required this.onOverlaySize,
    required this.buildSignatureWidget,
  });

  @override
  State<_EditOverlay> createState() => _EditOverlayState();
}

class _EditOverlayState extends State<_EditOverlay> {
  Offset? _panStartPosition;
  bool _isHorizontalGesture = false;
  bool _gestureDecided = false;

  // Determine if we should intercept the gesture based on the tool and gesture direction
  bool _shouldInterceptGesture() {
    // Always intercept if we're resizing a signature
    if (widget.isResizingSignature || widget.placingSignature != null) {
      return true;
    }
    
    // For drawing tool, always intercept to allow drawing
    if (widget.activeTool == EditTool.draw) {
      return true;
    }
    
    // For eraser tool, intercept taps and vertical gestures, but allow horizontal swipes
    if (widget.activeTool == EditTool.eraser) {
      return !_isHorizontalGesture;
    }
    
    // For signature tool (not placing), allow horizontal gestures for page switching
    if (widget.activeTool == EditTool.signature && widget.placingSignature == null) {
      return !_isHorizontalGesture;
    }
    
    // For no tool, allow horizontal gestures for page switching
    if (widget.activeTool == EditTool.none) {
      return !_isHorizontalGesture;
    }
    
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final overlaySize = constraints.biggest;
        // Page rect inside overlay using BoxFit.contain logic
        Rect pageRect = Rect.zero;
        if (widget.pageSize.width > 0 && widget.pageSize.height > 0 &&
            overlaySize.width > 0 && overlaySize.height > 0) {
          final pageAspect = widget.pageSize.width / widget.pageSize.height;
          final overlayAspect = overlaySize.width / overlaySize.height;
          if (overlayAspect > pageAspect) {
            // Fit height
            final h = overlaySize.height;
            final w = h * pageAspect;
            final left = (overlaySize.width - w) / 2;
            pageRect = Rect.fromLTWH(left, 0, w, h);
          } else {
            // Fit width
            final w = overlaySize.width;
            final h = w / pageAspect;
            final top = (overlaySize.height - h) / 2;
            pageRect = Rect.fromLTWH(0, top, w, h);
          }
        }

        // Report the effective interactive area size (page rect size)
        widget.onOverlaySize(pageRect.size);

        final pageStrokes = widget.strokesByPage[widget.page] ?? const <_InkStroke>[];
        final pageSignatures = widget.signaturesByPage[widget.page] ?? const <_SignatureAnno>[];

        if (kDebugMode) {
          debugPrint('[PdfViewer] Overlay layout: page=${widget.page} pageSize=${widget.pageSize} overlay=$overlaySize pageRect=$pageRect');
        }

        return Stack(
          children: [
            // Page-rect scoped interactive and drawing stack
            Positioned.fromRect(
              rect: pageRect,
              child: Stack(
                children: [
                  // Gesture layer
                  Positioned.fill(
                    child: GestureDetector(
                        onPanStart: (d) {
                          _panStartPosition = d.localPosition;
                          _isHorizontalGesture = false;
                          _gestureDecided = false;
                          
                          if (widget.activeTool == EditTool.draw && _shouldInterceptGesture()) {
                            widget.onPanDraw(d.localPosition, pageRect.size);
                            if (kDebugMode) {
                              debugPrint('[PdfViewer] onPanStart local=${d.localPosition}');
                            }
                          }
                        },
                        onPanUpdate: (d) {
                          // Determine gesture direction if not decided yet
                          if (!_gestureDecided && _panStartPosition != null) {
                            final dx = (d.localPosition.dx - _panStartPosition!.dx).abs();
                            final dy = (d.localPosition.dy - _panStartPosition!.dy).abs();
                            
                            // Only decide after minimum movement threshold
                            if (dx > 10 || dy > 10) {
                              _isHorizontalGesture = dx > dy;
                              _gestureDecided = true;
                              
                              if (kDebugMode) {
                                debugPrint('[PdfViewer] Gesture decided: ${_isHorizontalGesture ? "horizontal" : "vertical"}, dx=$dx, dy=$dy');
                              }
                            }
                          }
                          
                          // Handle gesture based on tool and direction
                          if (_shouldInterceptGesture()) {
                            if (widget.activeTool == EditTool.draw) {
                              widget.onPanDraw(d.localPosition, pageRect.size);
                              if (kDebugMode) {
                                debugPrint('[PdfViewer] onPanUpdate local=${d.localPosition}');
                              }
                            } else if (widget.activeTool == EditTool.eraser) {
                              widget.onEraseAt(d.localPosition, pageRect.size);
                            } else if (widget.activeTool == EditTool.signature && widget.placingSignature != null) {
                              widget.onSignatureMove(d.delta, pageRect.size);
                            }
                          }
                        },
                        onPanEnd: (d) {
                          if (_shouldInterceptGesture() && widget.activeTool == EditTool.draw) {
                            widget.onPanEnd(pageRect.size);
                            if (kDebugMode) {
                              debugPrint('[PdfViewer] onPanEnd');
                            }
                          }
                          
                          // Reset gesture state
                          _panStartPosition = null;
                          _isHorizontalGesture = false;
                          _gestureDecided = false;
                        },
                        onTapDown: (d) {
                          if (_shouldInterceptGesture()) {
                            if (widget.activeTool == EditTool.eraser) {
                              widget.onEraseAt(d.localPosition, pageRect.size);
                            } else if (widget.placingSignature == null) {
                              // Only handle signature tap when not in signature placement mode
                              widget.onSignatureTap(d.localPosition, pageRect.size);
                            }
                          }
                        },
                        // Use deferToChild to allow page switching gestures to pass through when appropriate
                        behavior: _shouldInterceptGesture() ? HitTestBehavior.opaque : HitTestBehavior.deferToChild,
                      ),
                    ),

                  // Ink strokes (above, but do not block gestures)
                  Positioned.fill(
                    child: IgnorePointer(
                      ignoring: true,
                      child: CustomPaint(
                        painter: _InkPainter(
                          pageStrokes,
                          strokeWidthScale: 1.0,
                        ),
                        foregroundPainter: _InkPainter(
                          // draw current stroke in foreground while drawing
                          widget.activeTool == EditTool.draw && widget.currentStroke.isNotEmpty
                              ? [
                                  _InkStroke(
                                      points: widget.currentStroke,
                                      color: widget.strokeColor,
                                      width: widget.strokeWidth)
                                ]
                              : const <_InkStroke>[],
                          strokeWidthScale: 1.0,
                        ),
                      ),
                    ),
                  ),

                  // Draw placed signatures (above gesture layer so handles can capture)
                  ...pageSignatures.map((sigAnno) => widget.buildSignatureWidget(sigAnno, pageRect.size)),

                  // Draw placing signature
                  if (widget.placingSignature != null)
                    widget.buildSignatureWidget(widget.placingSignature!, pageRect.size),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}


class _InkPainter extends CustomPainter {
  final List<_InkStroke> strokes;
  final double strokeWidthScale;
  _InkPainter(this.strokes, {this.strokeWidthScale = 1.0});

  @override
  void paint(Canvas canvas, Size size) {
    if (kDebugMode) {
      debugPrint('[PdfViewer] InkPainter.paint size=$size strokes=${strokes.length}'
          '${strokes.isNotEmpty ? ' firstLen=${strokes.first.points.length}' : ''}');
    }
    for (final s in strokes) {
      final paint = Paint()
        ..color = s.color
        ..strokeWidth = s.width
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round
        ..isAntiAlias = true;
      final path = Path();
      for (int i = 0; i < s.points.length; i++) {
        final p =
            Offset(s.points[i].dx * size.width, s.points[i].dy * size.height);
        if (i == 0) {
          path.moveTo(p.dx, p.dy);
        } else {
          path.lineTo(p.dx, p.dy);
        }
      }
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant _InkPainter oldDelegate) => true;
}

class _SignaturePainter extends CustomPainter {
  final Signature signature;

  _SignaturePainter({required this.signature});

  @override
  void paint(Canvas canvas, Size size) {
    if (signature.strokes.isEmpty) return;

    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;

    final scaleX = size.width / signature.canvasSize.width;
    final scaleY = size.height / signature.canvasSize.height;
    final uniformScale = math.min(scaleX, scaleY); // 保持宽高比
    
    // 计算居中偏移
    final actualWidth = signature.canvasSize.width * uniformScale;
    final actualHeight = signature.canvasSize.height * uniformScale;
    final offsetX = (size.width - actualWidth) / 2;
    final offsetY = (size.height - actualHeight) / 2;

    for (final stroke in signature.strokes) {
      if (stroke.points.length < 2) continue;
      
      paint.color = stroke.color;
      paint.strokeWidth = stroke.strokeWidth * uniformScale;
      
      final path = Path();
      final firstPoint = stroke.points.first;
      path.moveTo(
        offsetX + firstPoint.dx * uniformScale,
        offsetY + firstPoint.dy * uniformScale,
      );
      
      for (int i = 1; i < stroke.points.length; i++) {
        final point = stroke.points[i];
        path.lineTo(
          offsetX + point.dx * uniformScale,
          offsetY + point.dy * uniformScale,
        );
      }
      
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(_SignaturePainter oldDelegate) {
    return signature != oldDelegate.signature;
  }
}

enum _UnsavedAction { cancel, discard, save }

class _InkStroke {
  final List<Offset> points; // normalized [0..1]
  final Color color;
  final double width; // logical pixels

  _InkStroke({required this.points, required this.color, required this.width});

  bool hitTest(Offset p, Size size) {
    // consider near any segment
    final threshold = (width + 6) / size.width; // approximate
    for (int i = 1; i < points.length; i++) {
      final a = points[i - 1];
      final b = points[i];
      final d = _distancePointToSegment(p, a, b);
      if (d < threshold) return true;
    }
    return false;
  }

  double _distancePointToSegment(Offset p, Offset a, Offset b) {
    final ap = p - a;
    final ab = b - a;
    final ab2 = ab.dx * ab.dx + ab.dy * ab.dy;
    double t = ab2 == 0 ? 0 : (ap.dx * ab.dx + ap.dy * ab.dy) / ab2;
    t = t.clamp(0.0, 1.0);
    final proj = Offset(a.dx + ab.dx * t, a.dy + ab.dy * t);
    return (p - proj).distance;
  }
}

class _SignatureAnno {
  final Signature signature;
  Offset position; // PDF coordinates (top-left corner)
  Size size; // PDF dimensions
  bool isSelected;

  _SignatureAnno({
    required this.signature,
    required this.position,
    required this.size,
    this.isSelected = false,
  });

  bool hitTest(Offset p) {
    return p.dx >= position.dx &&
        p.dx <= position.dx + size.width &&
        p.dy >= position.dy &&
        p.dy <= position.dy + size.height;
  }

  Rect get bounds => Rect.fromLTWH(position.dx, position.dy, size.width, size.height);
}

enum EditTool { none, draw, eraser, signature }

class SignatureSelectionScreen extends StatefulWidget {
  const SignatureSelectionScreen({super.key});

  @override
  State<SignatureSelectionScreen> createState() => _SignatureSelectionScreenState();
}

class _SignatureSelectionScreenState extends State<SignatureSelectionScreen> {
  List<Signature> _signatures = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSignatures();
  }

  Future<void> _loadSignatures() async {
    try {
      await SignatureService.instance.initialize();
      setState(() {
        _signatures = SignatureService.instance.all;
        _isLoading = false;
      });
      
      if (kDebugMode) {
        print('[SignatureSelectionScreen] Loaded ${_signatures.length} signatures');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[SignatureSelectionScreen] Error loading signatures: $e');
      }
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AppState>();
    final brand = state.activeBrandTheme;
    
    return Scaffold(
      appBar: AppBar(
        backgroundColor: brand.brandColor,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        title: Text(AppLocalizations.of(context)!.selectSignature),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _signatures.isEmpty
              ? _buildEmptyState(context)
              : _buildSignatureGrid(context),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.draw,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context)!.noSignaturesPleaseCreate,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _navigateToCreateSignature(context),
              child: Text(AppLocalizations.of(context)!.createSignature),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSignatureGrid(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 3 / 2,
        ),
        itemCount: _signatures.length,
        itemBuilder: (context, index) {
          final signature = _signatures[index];
          return _buildSignatureCard(context, signature);
        },
      ),
    );
  }

  Widget _buildSignatureCard(BuildContext context, Signature signature) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(signature),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.white,
            child: FutureBuilder<String>(
              future: SignatureService.instance.generateThumbnail(signature),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final thumbnailPath = snapshot.data!;
                  final file = File(thumbnailPath);
                  
                  if (file.existsSync()) {
                    return Image.file(
                      file,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildSignaturePreview(signature);
                      },
                    );
                  }
                }
                
                return _buildSignaturePreview(signature);
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSignaturePreview(Signature signature) {
    return CustomPaint(
      painter: SignaturePreviewPainter(signature: signature),
      size: Size.infinite,
    );
  }

  Future<void> _navigateToCreateSignature(BuildContext context) async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => const SignatureCreationScreen(),
      ),
    );

    if (result == true) {
      await _loadSignatures();
    }
  }
}
