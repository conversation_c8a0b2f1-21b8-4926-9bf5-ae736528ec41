import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as p;
import 'package:provider/provider.dart';

import '../../l10n/app_localizations.dart';
import '../services/document_scanner.dart';
import '../state/app_state.dart';
import '../widgets/file_tile.dart';

class RecentsScreen extends StatefulWidget {
  const RecentsScreen({super.key});

  @override
  State<RecentsScreen> createState() => _RecentsScreenState();
}

class _RecentsScreenState extends State<RecentsScreen> {
  DocType? _getFileType(String filePath) {
    final extension = p.extension(filePath).toLowerCase();
    switch (extension) {
      case '.pdf':
        return DocType.pdf;
      case '.doc':
      case '.docx':
        return DocType.word;
      case '.xls':
      case '.xlsx':
        return DocType.excel;
      case '.ppt':
      case '.pptx':
        return DocType.ppt;
      default:
        return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AppState>();
    final recentPaths = state.recents.paths;
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    // 过滤出存在的文件并按当前活动的文档类型进行筛选
    final existingFiles = <DocumentFile>[];
    if (kDebugMode) {
      print('[RecentsScreen] Current activeDocType: ${state.activeDocType}');
      print('[RecentsScreen] Total recent paths: ${recentPaths.length}');
    }
    
    for (final path in recentPaths) {
      final file = File(path);
      if (file.existsSync()) {
        final fileType = _getFileType(path);
        if (kDebugMode) {
          print('[RecentsScreen] File: $path, detected type: $fileType, active type: ${state.activeDocType}, match: ${fileType == state.activeDocType}');
        }
        if (fileType == state.activeDocType) {
          final stat = file.statSync();
          existingFiles.add(DocumentFile(
            path: path,
            displayName: p.basename(path),
            sizeBytes: stat.size,
            modified: stat.modified,
          ));
        }
      }
    }
    
    if (kDebugMode) {
      print('[RecentsScreen] Filtered files count: ${existingFiles.length}');
    }

    return existingFiles.isEmpty
        ? LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(24.0),
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: constraints.maxHeight - 48),
                  child: Center(
                    child: Text(
                      l10n.noRecentFiles,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              );
            },
          )
        : ListView.separated(
            padding: const EdgeInsets.all(12),
            itemBuilder: (context, index) {
              final doc = existingFiles[index];
              return FileTile(file: doc);
            },
            separatorBuilder: (_, __) => const SizedBox(height: 6),
            itemCount: existingFiles.length,
          );
  }
}
