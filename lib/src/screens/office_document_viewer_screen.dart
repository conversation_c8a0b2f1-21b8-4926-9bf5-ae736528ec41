import 'dart:io';
import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:provider/provider.dart';

import '../../l10n/app_localizations.dart';
import '../services/external_file_service.dart';
import '../services/document_scanner.dart';
import '../services/pdf_conversion_service.dart';
import '../services/document_search_manager.dart';
import '../services/generated_pdfs_cache_service.dart';
import '../state/app_state.dart';
import '../widgets/thumbnail_manager.dart';
import '../widgets/thumbnail_sidebar.dart';
import 'slideshow_screen.dart';
import 'pdf_viewer_screen.dart';

class OfficeDocumentViewerScreen extends StatefulWidget {
  final DocumentFile documentFile;

  const OfficeDocumentViewerScreen({
    super.key,
    required this.documentFile,
  });

  @override
  State<OfficeDocumentViewerScreen> createState() => _OfficeDocumentViewerScreenState();
}

class _OfficeDocumentViewerScreenState extends State<OfficeDocumentViewerScreen> {
  InAppWebViewController? _controller;
  bool _isLoading = true;
  String? _errorMessage;
  String? _tempFilePath; // For external files copied to temp directory
  String? _filePathToLoad; // Path to the file that will be loaded in WebView
  bool _hasUnsavedChanges = false;
  int _rotation = 0; // 0, 90, 180, 270 degrees
  bool _showSearch = false;
  bool _mainWebViewLoaded = false;
  final TextEditingController _searchController = TextEditingController();
  
  // Search manager
  late final DocumentSearchManager _searchManager;
  
  // Thumbnail manager
  late final ThumbnailManager _thumbnailManager;
  
  // PDF conversion state management
  bool _isConverting = false;
  bool _conversionCancelled = false;
  String? _pendingPdfPath;
  
  // WebView loading timeout
  Timer? _loadingTimer;

  @override
  void initState() {
    super.initState();
    _searchManager = DocumentSearchManager();
    _searchManager.addListener(() {
      if (mounted) setState(() {});
    });
    _thumbnailManager = ThumbnailManager();
    _thumbnailManager.addListener(() {
      if (mounted) setState(() {});
    });
    _initializeWebView();
  }

  Future<void> _initializeWebView() async {
    try {
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Starting initialization for file: ${widget.documentFile.path}');
        print('[OfficeDocumentViewerScreen] File name: ${widget.documentFile.displayName}');
        print('[OfficeDocumentViewerScreen] Is external file: ${widget.documentFile.isExternal}');
        print('[OfficeDocumentViewerScreen] Platform: ${Platform.operatingSystem}');
      }

      // Handle external files vs local files differently
      String filePathToLoad;
      
      if (widget.documentFile.isExternal && widget.documentFile.externalBytes != null) {
        // External file - copy to temporary directory first
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] Processing external file with ${widget.documentFile.externalBytes!.length} bytes');
        }
        
        try {
          _tempFilePath = await ExternalFileService.copyExternalFileToTemp(
            fileBytes: widget.documentFile.externalBytes!,
            originalFileName: widget.documentFile.displayName,
          );
          filePathToLoad = _tempFilePath!;
          
          if (kDebugMode) {
            print('[OfficeDocumentViewerScreen] External file copied to: $filePathToLoad');
          }
        } catch (copyError) {
          if (kDebugMode) {
            print('[OfficeDocumentViewerScreen] Error copying external file: $copyError');
          }
          if (mounted) {
            setState(() {
              _isLoading = false;
              _errorMessage = 'Cannot copy external file: $copyError';
            });
          }
          return;
        }
      } else {
        // Local file - use original path
        filePathToLoad = widget.documentFile.path;
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] Using local file path: $filePathToLoad');
        }
      }

      // Verify the file to load exists and is accessible
      final file = File(filePathToLoad);
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Checking file existence: $filePathToLoad');
      }
      
      if (await file.exists()) {
        final fileStat = await file.stat();
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] File exists: $filePathToLoad');
          print('[OfficeDocumentViewerScreen] File size: ${fileStat.size} bytes');
          print('[OfficeDocumentViewerScreen] File modified: ${fileStat.modified}');
        }
        
        // Store the file path for WebView loading
        _filePathToLoad = filePathToLoad;
        
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] File path prepared for WebView: $filePathToLoad');
        }
        
        // File path is ready, WebView will load it when created
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
          if (kDebugMode) {
            print('[OfficeDocumentViewerScreen] Set _isLoading = false, ready to show WebView');
          }
          
          // Set a timeout for WebView loading
          _startLoadingTimeout();
        }
      } else {
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] File does not exist: $filePathToLoad');
        }
        if (mounted) {
          setState(() {
            _isLoading = false;
            _errorMessage = 'File not found at: $filePathToLoad';
          });
        }
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Error initializing WebView: $e');
        print('[OfficeDocumentViewerScreen] Stack trace: $stackTrace');
      }
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Initialization error: $e';
        });
      }
    }
  }

  Future<void> _saveDocument() async {
    // Convert current document to PDF
    await _convertToPdf();
  }

  Future<void> _convertToPdf() async {
    if (_isConverting) return; // Prevent multiple conversions
    
    if (kDebugMode) {
      print('[OfficeDocumentViewerScreen] === Starting PDF conversion ===');
      print('[OfficeDocumentViewerScreen] File: ${widget.documentFile.displayName}');
      print('[OfficeDocumentViewerScreen] Controller available: ${_controller != null}');
    }
    
    setState(() {
      _isConverting = true;
      _conversionCancelled = false;
      _pendingPdfPath = null;
    });

    try {
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Step 1: Checking WebView readiness...');
      }
      
      // Ensure WebView is fully loaded before PDF generation
      if (!_mainWebViewLoaded || _controller == null) {
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] WebView not ready - _mainWebViewLoaded: $_mainWebViewLoaded, controller: ${_controller != null}');
        }
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)!.waitForDocLoad),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }
      
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Step 2: Getting document type...');
      }
      
      final documentType = PdfConversionService.getDocumentType(widget.documentFile.displayName);
      
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Step 3: Document type determined: $documentType');
        print('[OfficeDocumentViewerScreen] Step 4: Calling PdfConversionService.convertToPdf...');
      }
      
      // Show user feedback about generating PDF
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.generatingPdf),
            duration: Duration(seconds: 1),
          ),
        );
      }
      
      final pdfPath = await PdfConversionService.convertToPdf(
        controller: _controller!,
        originalFileName: widget.documentFile.displayName,
        documentType: documentType,
        sourceFilePath: _filePathToLoad, // Pass source file path for caching
      );

      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Step 5: PdfConversionService returned: $pdfPath');
      }

      // Store the PDF path for potential cleanup
      _pendingPdfPath = pdfPath;

      if (mounted && pdfPath != null) {
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] Step 6: PDF conversion successful, showing dialog...');
        }
        
        // Success - show confirmation dialog
        final fileName = pdfPath.split('/').last;
        final result = await _showPdfSavedDialog(fileName);
        
        if (result == true) {
          // User wants to open the PDF - open directly
          await _openPdfDirectly(pdfPath);
        }
        
        setState(() {
          _hasUnsavedChanges = false;
        });
        
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] Step 7: Conversion flow completed successfully');
        }
      } else if (mounted) {
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] Step 6 (Error): PDF conversion failed, pdfPath is null');
        }
        
        // Failed
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.pdfGenerationFailed),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] === PDF CONVERSION ERROR ===');
        print('[OfficeDocumentViewerScreen] Error: $e');
        print('[OfficeDocumentViewerScreen] Stack trace: $stackTrace');
        print('[OfficeDocumentViewerScreen] === END ERROR INFO ===');
      }
      
      // Cleanup partial PDF on error
      await _cleanupPartialPdf();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.pdfConversionFailed(e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isConverting = false;
          _conversionCancelled = false;
          _pendingPdfPath = null;
        });
      }
      
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] === PDF conversion process finished ===');
      }
    }
  }

  Future<bool?> _showPdfSavedDialog(String fileName) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.conversionSuccess),
        content: Text(AppLocalizations.of(context)!.pdfSavedMessage(fileName)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(AppLocalizations.of(context)!.stayOnPage),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(AppLocalizations.of(context)!.openPdf),
          ),
        ],
      ),
    );
  }

  Future<bool?> _showConvertToPdfDialog() {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.generatePdfVersion),
        content: Text(AppLocalizations.of(context)!.generatePdfToEdit),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(AppLocalizations.of(context)!.cancel),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(AppLocalizations.of(context)!.continueAction),
          ),
        ],
      ),
    );
  }


  Future<void> _cleanupPartialPdf() async {
    if (_pendingPdfPath != null) {
      try {
        final file = File(_pendingPdfPath!);
        if (await file.exists()) {
          await file.delete();
          if (kDebugMode) {
            print('[OfficeDocumentViewerScreen] Cleaned up partial PDF: $_pendingPdfPath');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] Error cleaning up partial PDF: $e');
        }
      } finally {
        _pendingPdfPath = null;
      }
    }
  }

  Future<void> _openPdfDirectly(String pdfPath) async {
    try {
      if (mounted) {
        // Navigate directly to the app's PDF viewer for consistency
        final fileName = pdfPath.split('/').last;
        final result = await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (_) => PdfViewerScreen(
              filePath: pdfPath,
              fileName: fileName,
            ),
          ),
        );
        // If PDF viewer requested Office search, open search here
        if (result == 'open_office_search' && mounted) {
          setState(() {
            _showSearch = true;
          });
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Error opening PDF directly: $e');
      }
    }
  }

  Future<void> _openPdfInEditMode(String pdfPath) async {
    try {
      if (mounted) {
        // Navigate directly to the app's PDF viewer in edit mode
        final fileName = pdfPath.split('/').last;
        final result = await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (_) => PdfViewerScreen(
              filePath: pdfPath,
              fileName: fileName,
              initialEditMode: true, // Open in edit mode
            ),
          ),
        );
        if (result == 'open_office_search' && mounted) {
          setState(() {
            _showSearch = true;
          });
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Error opening PDF in edit mode: $e');
      }
    }
  }

  Future<void> _openPdfWithRotate(String pdfPath) async {
    try {
      if (mounted) {
        final fileName = pdfPath.split('/').last;
        final result = await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (_) => PdfViewerScreen(
              filePath: pdfPath,
              fileName: fileName,
              initialRotateOnce: true, // Rotate current page after load
            ),
          ),
        );
        if (result == 'open_office_search' && mounted) {
          setState(() {
            _showSearch = true;
          });
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Error opening PDF with rotate: $e');
      }
    }
  }

  Future<void> _handleRotateDocument() async {
    if (_filePathToLoad == null) {
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Cannot check cache: _filePathToLoad is null');
      }
      return;
    }
    
    if (kDebugMode) {
      print('[OfficeDocumentViewerScreen] Checking cache for rotate...');
    }
    
    // Check if we have a cached PDF first
    final cachedPdfPath = await GeneratedPdfsCacheService.getCachedPdfPath(
      sourceFilePath: _filePathToLoad!,
      sourceFileName: widget.documentFile.displayName,
    );
    
    if (cachedPdfPath != null) {
      // We have a cached PDF, use it directly
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Found cached PDF for rotate: $cachedPdfPath');
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.openingCachedPdf),
            duration: Duration(milliseconds: 800),
          ),
        );
      }
      
      // Open cached PDF with rotate
      await _openPdfWithRotate(cachedPdfPath);
      return;
    }
    
    if (kDebugMode) {
      print('[OfficeDocumentViewerScreen] No cached PDF found for rotate, showing dialog...');
    }
    
    // No cached PDF, show conversion dialog first
    final shouldConvert = await _showConvertToPdfDialog();
    
    if (shouldConvert == true) {
      // Convert to PDF and open with an initial rotation
      await _convertToPdfAndRotate();
    }
    // If user selected "No" or cancelled, do nothing
  }

  Future<void> _rotateDocument() async {
    setState(() {
      _rotation = (_rotation + 90) % 360;
      _hasUnsavedChanges = true;
    });
    
    // Apply CSS transform to rotate the WebView content
    if (_controller != null) {
      await _controller!.evaluateJavascript(source: '''
        document.body.style.transform = 'rotate(${_rotation}deg)';
        document.body.style.transformOrigin = 'center center';
        if ($_rotation == 90 || $_rotation == 270) {
          document.body.style.width = '${_rotation == 90 ? '100vh' : '100vh'}';
          document.body.style.height = '${_rotation == 90 ? '100vw' : '100vw'}';
        } else {
          document.body.style.width = '100%';
          document.body.style.height = '100%';
        }
      ''');
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppLocalizations.of(context)!.pageRotated(1, _rotation))),
      );
    }
  }

  void _toggleSearch() {
    setState(() {
      _showSearch = !_showSearch;
      if (!_showSearch) {
        _searchController.clear();
        _searchManager.clearSearch();
      }
    });
  }

  Future<void> _performSearch(String query) async {
    await _searchManager.performSearch(query);
  }
  
  
  
  Future<void> _navigateToNextSearchMatch() async {
    await _searchManager.navigateToNext();
  }
  
  Future<void> _navigateToPreviousSearchMatch() async {
    await _searchManager.navigateToPrevious();
  }
  


  Future<void> _showEditMode() async {
    if (_filePathToLoad == null) {
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Cannot check cache: _filePathToLoad is null');
      }
      return;
    }
    
    if (kDebugMode) {
      print('[OfficeDocumentViewerScreen] Checking cache for edit mode...');
    }
    
    // Check if we have a cached PDF first
    final cachedPdfPath = await GeneratedPdfsCacheService.getCachedPdfPath(
      sourceFilePath: _filePathToLoad!,
      sourceFileName: widget.documentFile.displayName,
    );
    
    if (cachedPdfPath != null) {
      // We have a cached PDF, use it directly
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Found cached PDF for edit mode: $cachedPdfPath');
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.openingCachedPdf),
            duration: Duration(milliseconds: 800),
          ),
        );
      }
      
      // Open cached PDF directly in edit mode
      await _openPdfInEditMode(cachedPdfPath);
      return;
    }
    
    if (kDebugMode) {
      print('[OfficeDocumentViewerScreen] No cached PDF found, showing dialog...');
    }
    
    // No cached PDF, show conversion dialog first
    final shouldConvert = await _showConvertToPdfDialog();
    
    if (shouldConvert == true) {
      // Convert to PDF and open in edit mode
      await _convertToPdfAndEdit();
    }
    // If user selected "No" or cancelled, do nothing
  }

  Future<void> _convertToPdfAndEdit() async {
    if (_isConverting) return; // Prevent multiple conversions
    
    if (kDebugMode) {
      print('[OfficeDocumentViewerScreen] === Starting PDF conversion for edit mode ===');
      print('[OfficeDocumentViewerScreen] File: ${widget.documentFile.displayName}');
      print('[OfficeDocumentViewerScreen] Controller available: ${_controller != null}');
    }
    
    setState(() {
      _isConverting = true;
      _conversionCancelled = false;
      _pendingPdfPath = null;
    });

    try {
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Step 1: Checking WebView readiness...');
      }
      
      // Ensure WebView is fully loaded before PDF generation
      if (!_mainWebViewLoaded || _controller == null) {
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] WebView not ready - _mainWebViewLoaded: $_mainWebViewLoaded, controller: ${_controller != null}');
        }
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)!.waitForDocLoad),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }
      
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Step 2: Getting document type...');
      }
      
      final documentType = PdfConversionService.getDocumentType(widget.documentFile.displayName);
      
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Step 3: Document type determined: $documentType');
        print('[OfficeDocumentViewerScreen] Step 4: Calling PdfConversionService.convertToPdf...');
      }
      
      // Show user feedback about generating PDF
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.generatingPdf),
            duration: Duration(seconds: 1),
          ),
        );
      }
      
      final pdfPath = await PdfConversionService.convertToPdf(
        controller: _controller!,
        originalFileName: widget.documentFile.displayName,
        documentType: documentType,
        sourceFilePath: _filePathToLoad, // Pass source file path for caching
      );

      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Step 5: PdfConversionService returned: $pdfPath');
      }

      // Store the PDF path for potential cleanup
      _pendingPdfPath = pdfPath;

      if (mounted && pdfPath != null) {
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] Step 6: PDF conversion successful, opening in edit mode...');
        }
        
        // Success - open PDF directly in edit mode (no dialog)
        await _openPdfInEditMode(pdfPath);
        
        setState(() {
          _hasUnsavedChanges = false;
        });
        
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] Step 7: Conversion and edit mode opening completed successfully');
        }
      } else if (mounted) {
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] Step 6 (Error): PDF conversion failed, pdfPath is null');
        }
        
        // Failed
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.pdfGenerationFailed),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] === PDF CONVERSION ERROR (Edit Mode) ===');
        print('[OfficeDocumentViewerScreen] Error: $e');
        print('[OfficeDocumentViewerScreen] Stack trace: $stackTrace');
        print('[OfficeDocumentViewerScreen] === END ERROR INFO ===');
      }
      
      // Cleanup partial PDF on error
      await _cleanupPartialPdf();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.pdfConversionFailed(e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isConverting = false;
          _conversionCancelled = false;
          _pendingPdfPath = null;
        });
      }
      
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] === PDF conversion process finished (Edit Mode) ===');
      }
    }
  }

  Future<void> _convertToPdfAndRotate() async {
    if (_isConverting) return;
    setState(() {
      _isConverting = true;
      _conversionCancelled = false;
      _pendingPdfPath = null;
    });

    try {
      if (!_mainWebViewLoaded || _controller == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)!.waitForDocLoad),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      final documentType = PdfConversionService.getDocumentType(widget.documentFile.displayName);
      final pdfPath = await PdfConversionService.convertToPdf(
        controller: _controller!,
        originalFileName: widget.documentFile.displayName,
        documentType: documentType,
        sourceFilePath: _filePathToLoad, // Pass source file path for caching
      );

      _pendingPdfPath = pdfPath;

      if (mounted && pdfPath != null) {
        // Open PDF and rotate immediately
        await _openPdfWithRotate(pdfPath);
        setState(() {
          _hasUnsavedChanges = false;
        });
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.pdfGenerationFailed),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      await _cleanupPartialPdf();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.pdfConversionFailed(e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isConverting = false;
          _conversionCancelled = false;
          _pendingPdfPath = null;
        });
      }
    }
  }

  void _toggleThumbnails() {
    _thumbnailManager.toggleVisibility();
  }

  bool _isPowerPointFile() {
    final path = widget.documentFile.path.toLowerCase();
    final displayName = widget.documentFile.displayName.toLowerCase();
    final isPPT = path.endsWith('.ppt') || path.endsWith('.pptx') || 
                  displayName.endsWith('.ppt') || displayName.endsWith('.pptx');
    
    if (kDebugMode) {
      print('[OfficeDocumentViewerScreen] Checking PPT file:');
      print('[OfficeDocumentViewerScreen]   File path: ${widget.documentFile.path}');
      print('[OfficeDocumentViewerScreen]   Display name: ${widget.documentFile.displayName}');
      print('[OfficeDocumentViewerScreen]   Path lower: $path');
      print('[OfficeDocumentViewerScreen]   Display name lower: $displayName');
      print('[OfficeDocumentViewerScreen]   Is PPT file: $isPPT');
    }
    
    return isPPT;
  }

  void _playSlideshow() {
    if (kDebugMode) {
      print('[OfficeDocumentViewerScreen] Starting slideshow for: ${widget.documentFile.displayName}');
    }
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SlideshowScreen(
          documentFile: widget.documentFile,
          tempFilePath: _tempFilePath,
        ),
      ),
    );
  }

  Future<void> _handleBackPressed() async {
    if (!_hasUnsavedChanges) {
      if (mounted) Navigator.of(context).pop();
      return;
    }
    
    final action = await _showUnsavedChangesDialog();
    if (action == _UnsavedAction.discard && mounted) {
      Navigator.of(context).pop();
    } else if (action == _UnsavedAction.save) {
      await _saveDocument();
      if (mounted && !_hasUnsavedChanges) {
        Navigator.of(context).pop();
      }
    }
  }

  Future<_UnsavedAction?> _showUnsavedChangesDialog() {
    return showDialog<_UnsavedAction>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.unsavedChanges),
        content: Text(AppLocalizations.of(context)!.unsavedChangesMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(_UnsavedAction.cancel),
            child: Text(AppLocalizations.of(context)!.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(_UnsavedAction.discard),
            child: Text(AppLocalizations.of(context)!.discardChanges),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(_UnsavedAction.save),
            child: Text(AppLocalizations.of(context)!.save),
          ),
        ],
      ),
    );
  }


  @override
  Widget build(BuildContext context) {
    final brand = context.watch<AppState>().activeBrandTheme;

    return PopScope(
      canPop: !_hasUnsavedChanges,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop && _hasUnsavedChanges) {
          await _handleBackPressed();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: brand.brandColor,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          title: Text(widget.documentFile.displayName),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: _handleBackPressed,
          ),
          actions: [
            // Save button with PDF label and unsaved changes indicator
            Tooltip(
              message: AppLocalizations.of(context)!.saveAsPdf,
              child: InkWell(
                onTap: _saveDocument,
                customBorder: const CircleBorder(),
                child: SizedBox(
                  width: 48,
                  height: 48,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      const Icon(Icons.save),
                      // Red PDF label
                      Positioned(
                        right: 4,
                        top: 4,
                        child: IgnorePointer(
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              'PDF',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                      if (_hasUnsavedChanges)
                        Positioned(
                          right: 8,
                          top: 28,
                          child: IgnorePointer(
                            child: Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Colors.orange,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        body: Stack(
          children: [
            // Main WebView content
            Positioned.fill(
              child: _buildBody(),
            ),
            // Search overlay
            if (_showSearch)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  color: Colors.grey.shade100,
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _searchController,
                              autofocus: true,
                              decoration: InputDecoration(
                                hintText: AppLocalizations.of(context)!.searchText,
                                border: const OutlineInputBorder(),
                                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                suffixIcon: _searchManager.isSearching 
                                  ? const Padding(
                                      padding: EdgeInsets.all(12.0),
                                      child: SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(strokeWidth: 2),
                                      ),
                                    )
                                  : null,
                              ),
                              onSubmitted: _performSearch,
                              onChanged: (value) {
                                _searchManager.performDebouncedSearch(value);
                              },
                            ),
                          ),
                          const SizedBox(width: 8),
                          // Case sensitivity toggle
                          Tooltip(
                            message: AppLocalizations.of(context)!.caseSensitive,
                            child: IconButton(
                              icon: Icon(
                                _searchManager.caseSensitive ? Icons.text_fields : Icons.text_fields_outlined,
                                color: _searchManager.caseSensitive ? Colors.orange : Colors.grey,
                              ),
                              onPressed: () {
                                _searchManager.setCaseSensitive(!_searchManager.caseSensitive);
                                // Re-search with new case sensitivity if there's a query
                                if (_searchController.text.isNotEmpty) {
                                  _performSearch(_searchController.text);
                                }
                              },
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.search),
                            onPressed: () => _performSearch(_searchController.text),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: _toggleSearch,
                          ),
                        ],
                      ),
                      // Search results and navigation row
                      if (_searchManager.hasResults || (_searchManager.lastSearchQuery.isNotEmpty && !_searchManager.hasResults))
                        Container(
                          margin: const EdgeInsets.only(top: 8),
                          child: Row(
                            children: [
                              // Search results count
                              Expanded(
                                child: Text(
                                  !_searchManager.hasResults && _searchManager.lastSearchQuery.isNotEmpty
                                    ? AppLocalizations.of(context)!.notFound(_searchManager.lastSearchQuery)
                                    : !_searchManager.hasResults
                                      ? ''
                                      : '${_searchManager.currentSearchIndex + 1} / ${_searchManager.totalResults}',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: !_searchManager.hasResults && _searchManager.lastSearchQuery.isNotEmpty 
                                      ? Colors.red 
                                      : Colors.grey.shade700,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              // Navigation buttons
                              if (_searchManager.totalResults > 1) ...[
                                IconButton(
                                  icon: const Icon(Icons.keyboard_arrow_up),
                                  onPressed: _navigateToPreviousSearchMatch,
                                  tooltip: AppLocalizations.of(context)!.previousMatch,
                                  iconSize: 20,
                                  constraints: const BoxConstraints(
                                    minWidth: 32,
                                    minHeight: 32,
                                  ),
                                  padding: const EdgeInsets.all(4),
                                ),
                                const SizedBox(width: 4),
                                IconButton(
                                  icon: const Icon(Icons.keyboard_arrow_down),
                                  onPressed: _navigateToNextSearchMatch,
                                  tooltip: AppLocalizations.of(context)!.nextMatch,
                                  iconSize: 20,
                                  constraints: const BoxConstraints(
                                    minWidth: 32,
                                    minHeight: 32,
                                  ),
                                  padding: const EdgeInsets.all(4),
                                ),
                              ],
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            // Thumbnails sidebar
            if (_thumbnailManager.isVisible)
              Positioned.fill(
                child: Row(
                  children: [
                    // Main content area (dimmed when thumbnails shown)
                    Expanded(
                      flex: 3,
                      child: GestureDetector(
                        onTap: _thumbnailManager.toggleVisibility,
                        child: Container(
                          color: Colors.black26,
                        ),
                      ),
                    ),
                    // Thumbnails sidebar
                    ThumbnailSidebar(
                      manager: _thumbnailManager,
                      onClose: _thumbnailManager.toggleVisibility,
                      onThumbnailTap: _handleThumbnailTap,
                      mainWebViewLoaded: _mainWebViewLoaded,
                    ),
                  ],
                ),
              ),
          ],
        ),
        bottomNavigationBar: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Bottom toolbar with buttons
            Container(
              padding: const EdgeInsets.all(12),
              color: Colors.white,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: _buildToolbarButtons(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildToolbarButtons() {
    final isPPT = _isPowerPointFile();
    final screenWidth = MediaQuery.of(context).size.width;
    final canShowAllButtons = screenWidth > 400; // 阈值：400像素
    
    if (kDebugMode) {
      print('[OfficeDocumentViewerScreen] Building toolbar buttons:');
      print('[OfficeDocumentViewerScreen]   isPPT: $isPPT');
      print('[OfficeDocumentViewerScreen]   screenWidth: $screenWidth');
      print('[OfficeDocumentViewerScreen]   canShowAllButtons: $canShowAllButtons');
    }

    List<Widget> buttons = [];

    if (isPPT) {
      // 添加播放幻灯片按钮
      buttons.add(_toolbarButton(
        icon: Icons.play_circle_filled,
        label: AppLocalizations.of(context)!.playSlideshow,
        onPressed: _errorMessage == null && !_isLoading ? _playSlideshow : null,
      ));
      
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen]   Added slideshow button');
      }
    }

    // 决定是否显示旋转按钮（如果是PPT且屏幕太小，则跳过旋转按钮）
    final shouldShowRotate = !isPPT || canShowAllButtons;
    if (shouldShowRotate) {
      buttons.add(_toolbarButton(
        icon: Icons.rotate_right,
        label: AppLocalizations.of(context)!.rotate,
        onPressed: _handleRotateDocument,
      ));
      
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen]   Added rotate button');
      }
    } else if (kDebugMode) {
      print('[OfficeDocumentViewerScreen]   Skipped rotate button (screen too small for PPT)');
    }

    // 始终显示的核心按钮
    buttons.addAll([
      _toolbarButton(
        icon: Icons.edit,
        label: AppLocalizations.of(context)!.edit,
        onPressed: _showEditMode,
        selected: false, // Not implemented yet
      ),
      _toolbarButton(
        icon: Icons.search,
        label: AppLocalizations.of(context)!.search,
        onPressed: _toggleSearch,
        selected: _showSearch,
      ),
      _toolbarButton(
        icon: Icons.view_list,
        label: AppLocalizations.of(context)!.thumbnails,
        onPressed: _toggleThumbnails,
        selected: false, // Not implemented yet
      ),
    ]);

    if (kDebugMode) {
      print('[OfficeDocumentViewerScreen]   Total buttons: ${buttons.length}');
    }

    return buttons;
  }

  Widget _buildBody() {
    if (kDebugMode) {
      print('[OfficeDocumentViewerScreen] _buildBody called - _isLoading: $_isLoading, _errorMessage: $_errorMessage, _filePathToLoad: $_filePathToLoad');
    }
    
    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context)!.cannotOpenFile,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_isLoading) {
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Showing loading screen - _isLoading: $_isLoading');
      }
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context)!.loadingDocument,
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ],
        ),
      );
    }

    // Only show WebView on iOS platforms where this functionality is intended
    if (Platform.isIOS) {
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] Building InAppWebView for iOS');
      }
      return _buildInAppWebView();
    } else {
      // Fallback for non-iOS platforms (shouldn't normally reach here based on our logic)
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.info_outline,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context)!.webViewOnlyIos,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }
  }

  Widget _buildInAppWebView() {
    if (kDebugMode) {
      print('[OfficeDocumentViewerScreen] _buildInAppWebView called - _filePathToLoad: $_filePathToLoad');
    }
    
    if (_filePathToLoad == null) {
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] _filePathToLoad is null, showing loading indicator');
      }
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (kDebugMode) {
      print('[OfficeDocumentViewerScreen] Creating InAppWebView with file: $_filePathToLoad');
    }

    // Create file URL for iOS - iOS can handle Chinese characters directly in file paths
    final fileUrl = 'file://$_filePathToLoad';
    
    if (kDebugMode) {
      print('[OfficeDocumentViewerScreen] File URL: $fileUrl');
    }
    
    return InAppWebView(
      initialUrlRequest: URLRequest(url: WebUri(fileUrl)),
      initialSettings: InAppWebViewSettings(
        javaScriptEnabled: true,
        allowsInlineMediaPlayback: true,
        mediaPlaybackRequiresUserGesture: false,
        allowsBackForwardNavigationGestures: true,
        supportZoom: true,
        builtInZoomControls: true,
        displayZoomControls: false,
        // iOS specific settings for local file access
        allowFileAccessFromFileURLs: true,
        allowUniversalAccessFromFileURLs: true,
        allowFileAccess: true,
      ),
      onWebViewCreated: (controller) {
        _controller = controller;
        _searchManager.setController(controller);
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] InAppWebView created');
        }
      },
      onLoadStart: (controller, url) {
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] Page started loading: $url');
        }
      },
      onLoadStop: (controller, url) async {
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] Page finished loading: $url');
        }
        
        // Cancel the loading timeout since loading completed successfully
        _cancelLoadingTimeout();
        
        if (mounted) {
          setState(() {
            _isLoading = false;
            _mainWebViewLoaded = true;
          });
          
          // Generate thumbnail after main WebView loads
          Future.delayed(const Duration(seconds: 1), () {
            if (mounted && _thumbnailManager.thumbnailData == null && _filePathToLoad != null) {
              _thumbnailManager.generateThumbnail(_filePathToLoad!);
            }
          });
        }
        
        // Update recents with the loaded file path
        if (mounted && _filePathToLoad != null) {
          try {
            context.read<AppState>().recents.add(_filePathToLoad!);
            if (kDebugMode) {
              print('[OfficeDocumentViewerScreen] Added to recents: $_filePathToLoad');
            }
          } catch (e) {
            if (kDebugMode) {
              print('[OfficeDocumentViewerScreen] Failed to add to recents: $e');
            }
          }
        }
      },
      onReceivedError: (controller, request, error) {
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] WebView error: ${error.description}');
          print('[OfficeDocumentViewerScreen] WebView error type: ${error.type}');
          print('[OfficeDocumentViewerScreen] Failed URL: ${request.url}');
        }
        if (mounted) {
          setState(() {
            _isLoading = false;
            _errorMessage = AppLocalizations.of(context)!.webViewFailed(error.description);
          });
        }
      },
      onReceivedHttpError: (controller, request, errorResponse) {
        if (kDebugMode) {
          print('[OfficeDocumentViewerScreen] HTTP error: ${errorResponse.statusCode} - ${errorResponse.reasonPhrase}');
        }
        if (mounted) {
          setState(() {
            _isLoading = false;
            _errorMessage = 'HTTP Error: ${errorResponse.statusCode}';
          });
        }
      },
    );
  }

  void _startLoadingTimeout() {
    _loadingTimer = Timer(const Duration(seconds: 30), () {
      if (kDebugMode) {
        print('[OfficeDocumentViewerScreen] WebView loading timeout after 30 seconds');
      }
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = AppLocalizations.of(context)!.officeTimeout;
        });
      }
    });
  }

  void _cancelLoadingTimeout() {
    _loadingTimer?.cancel();
    _loadingTimer = null;
  }


  Future<void> _handleThumbnailTap(double ratio) async {
    if (_controller != null) {
      await _thumbnailManager.scrollToRatio(ratio, _controller!);
    }
  }




  @override
  void dispose() {
    _cancelLoadingTimeout();
    _searchManager.dispose();
    _thumbnailManager.dispose();
    // Clean up temporary file if created
    if (_tempFilePath != null) {
      ExternalFileService.deleteTempFile(_tempFilePath!);
    }
    _searchController.dispose();
    super.dispose();
  }

  Widget _toolbarButton({
    required IconData icon,
    required String label,
    VoidCallback? onPressed,
    bool selected = false,
  }) {
    final color = selected ? Colors.orange : Colors.black87;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: onPressed,
          icon: Icon(icon, color: color),
        ),
        Text(label, style: TextStyle(color: color, fontSize: 12)),
      ],
    );
  }
}

enum _UnsavedAction { cancel, discard, save }
