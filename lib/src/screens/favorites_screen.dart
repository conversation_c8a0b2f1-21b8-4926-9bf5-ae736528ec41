import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as p;
import 'package:provider/provider.dart';

import '../../l10n/app_localizations.dart';
import '../services/document_scanner.dart';
import '../state/app_state.dart';
import '../widgets/file_tile.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  DocType? _getFileType(String filePath) {
    final extension = p.extension(filePath).toLowerCase();
    switch (extension) {
      case '.pdf':
        return DocType.pdf;
      case '.doc':
      case '.docx':
        return DocType.word;
      case '.xls':
      case '.xlsx':
        return DocType.excel;
      case '.ppt':
      case '.pptx':
        return DocType.ppt;
      default:
        return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AppState>();
    final favPaths = state.favorites.all;
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    // 过滤出存在的文件并按当前活动的文档类型进行筛选
    final existingFiles = <DocumentFile>[];
    if (kDebugMode) {
      print('[FavoritesScreen] Current activeDocType: ${state.activeDocType}');
      print('[FavoritesScreen] Total favorite paths: ${favPaths.length}');
    }
    
    for (final path in favPaths) {
      final file = File(path);
      if (file.existsSync()) {
        final fileType = _getFileType(path);
        if (kDebugMode) {
          print('[FavoritesScreen] File: $path, detected type: $fileType, active type: ${state.activeDocType}, match: ${fileType == state.activeDocType}');
        }
        if (fileType == state.activeDocType) {
          final stat = file.statSync();
          existingFiles.add(DocumentFile(
            path: path,
            displayName: p.basename(path),
            sizeBytes: stat.size,
            modified: stat.modified,
          ));
        }
      }
    }
    
    if (kDebugMode) {
      print('[FavoritesScreen] Filtered files count: ${existingFiles.length}');
    }

    return existingFiles.isEmpty
        ? LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                padding: const EdgeInsets.all(32.0),
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: constraints.maxHeight - 64),
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.star_border,
                          size: 64,
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          l10n.noFavoriteFiles,
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.colorScheme.onSurface,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          l10n.noFavoriteFilesHint,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                            height: 1.4,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          )
        : ListView.separated(
            padding: const EdgeInsets.all(12),
            itemBuilder: (context, index) {
              final doc = existingFiles[index];
              return FileTile(file: doc);
            },
            separatorBuilder: (_, __) => const SizedBox(height: 6),
            itemCount: existingFiles.length,
          );
  }
}
