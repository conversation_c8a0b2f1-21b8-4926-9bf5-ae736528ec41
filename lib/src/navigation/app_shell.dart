import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../l10n/app_localizations.dart';

import '../state/app_state.dart';
import '../screens/documents_screen.dart';
import '../screens/recents_screen.dart';
import '../screens/favorites_screen.dart';
import '../screens/settings_screen.dart';
import '../widgets/global_app_bar.dart';
import '../widgets/global_type_tabs.dart';

class AppShell extends StatelessWidget {
  const AppShell({super.key});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AppState>();
    final color = Theme.of(context).colorScheme.primary;
    final l10n = AppLocalizations.of(context)!;

    final pages = [
      const DocumentsScreen(),
      const RecentsScreen(),
      const FavoritesScreen(),
      const SettingsScreen(),
    ];

    final showGlobalElements = state.bottomTabIndex != 3; // Hide on settings page

    return Scaffold(
      appBar: showGlobalElements ? const GlobalAppBar() : null,
      body: Column(
        children: [
          if (showGlobalElements) const GlobalTypeTabs(),
          if (showGlobalElements) const SizedBox(height: 8),
          Expanded(
            child: IndexedStack(
              index: state.bottomTabIndex,
              children: pages,
            ),
          ),
        ],
      ),
      bottomNavigationBar: NavigationBar(
        selectedIndex: state.bottomTabIndex,
        onDestinationSelected: state.setBottomTab,
        indicatorColor: color.withValues(alpha: 0.15),
        destinations: [
          NavigationDestination(icon: const Icon(Icons.folder_open), label: l10n.documents),
          NavigationDestination(icon: const Icon(Icons.history), label: l10n.recent),
          NavigationDestination(icon: const Icon(Icons.star), label: l10n.favorite),
          NavigationDestination(icon: const Icon(Icons.settings), label: l10n.settings),
        ],
      ),
    );
  }
}
