import 'package:flutter/foundation.dart';
import 'package:read_pdf_text/read_pdf_text.dart';

class PdfTextService {
  /// Extract text using read_pdf_text package with intelligent page distribution
  static Future<List<PageText>> extractAllText(String filePath) async {
    try {
      if (kDebugMode) {
        print('[PdfTextService] Using read_pdf_text to extract from: $filePath');
      }
      
      // First, try to get the actual page count
      int totalPages = 60; // Default for this specific PDF
      try {
        totalPages = await ReadPdfText.getPDFlength(filePath);
        if (kDebugMode) {
          print('[PdfTextService] PDF has $totalPages pages');
        }
      } catch (e) {
        if (kDebugMode) {
          print('[PdfTextService] Could not get page count, using default: $e');
        }
      }
      
      // Method 1: Try paginated extraction first
      try {
        final pages = await ReadPdfText.getPDFtextPaginated(filePath);
        
        if (kDebugMode) {
          print('[PdfTextService] Paginated extraction: ${pages.length} pages');
        }
        
        if (pages.isNotEmpty) {
          final pageTexts = <PageText>[];
          for (int i = 0; i < pages.length; i++) {
            final pageText = pages[i];
            pageTexts.add(PageText(
              pageNumber: i + 1,
              text: pageText,
            ));
            
            if (kDebugMode && pageText.contains('订单')) {
              final orderCount = '订单'.allMatches(pageText).length;
              print('[PdfTextService] Page ${i + 1} contains $orderCount "订单"');
            }
          }
          
          return pageTexts;
        }
      } catch (e) {
        if (kDebugMode) {
          print('[PdfTextService] Paginated extraction failed: $e');
        }
      }
      
      // Method 2: Full text extraction with intelligent page distribution
      try {
        final text = await ReadPdfText.getPDFtext(filePath);
        
        if (kDebugMode) {
          print('[PdfTextService] Full text extracted: ${text.length} characters');
          if (text.isNotEmpty) {
            final sample = text.length > 200 ? text.substring(0, 200) : text;
            print('[PdfTextService] Sample text: "$sample"');
            
            // Test for specific content
            if (text.contains('订单')) {
              final orderCount = '订单'.allMatches(text).length;
              print('[PdfTextService] ✅ Found $orderCount occurrences of "订单"!');
            }
            
            if (text.contains('DeepSeek')) {
              print('[PdfTextService] ✅ Found "DeepSeek" in text!');
            }
          }
        }
        
        if (text.isNotEmpty) {
          // Intelligent page distribution
          return _distributeTextAcrossPages(text, totalPages);
        }
      } catch (e) {
        if (kDebugMode) {
          print('[PdfTextService] Full text extraction failed: $e');
        }
      }
      
      // Method 2: Try paginated extraction
      try {
        if (kDebugMode) {
          print('[PdfTextService] Trying paginated extraction...');
        }
        
        final pages = await ReadPdfText.getPDFtextPaginated(filePath);
        
        if (kDebugMode) {
          print('[PdfTextService] Extracted ${pages.length} pages');
        }
        
        final pageTexts = <PageText>[];
        for (int i = 0; i < pages.length; i++) {
          final pageText = pages[i];
          pageTexts.add(PageText(
            pageNumber: i + 1,
            text: pageText,
          ));
          
          if (kDebugMode) {
            print('[PdfTextService] Page ${i + 1}: ${pageText.length} characters');
            if (pageText.contains('订单')) {
              final orderCount = '订单'.allMatches(pageText).length;
              print('[PdfTextService] Page ${i + 1} contains $orderCount "订单"');
            }
          }
        }
        
        return pageTexts;
        
      } catch (e) {
        if (kDebugMode) {
          print('[PdfTextService] getPDFtextPaginated failed: $e');
        }
      }
      
      return [];
      
    } catch (e) {
      if (kDebugMode) {
        print('[PdfTextService] Error: $e');
      }
      return [];
    }
  }
  
  /// Intelligent text distribution across PDF pages
  static List<PageText> _distributeTextAcrossPages(String fullText, int totalPages) {
    if (kDebugMode) {
      print('[PdfTextService] Distributing text across $totalPages pages');
    }
    
    final pageTexts = <PageText>[];
    
    // Strategy 1: Look for natural page breaks
    final pageBreakPatterns = [
      RegExp(r'\n\s*\n\s*\n+'), // Multiple line breaks
      RegExp(r'\f'), // Form feed character
      RegExp(r'第\s*\d+\s*页'), // Chinese page numbers
      RegExp(r'Page\s*\d+'), // English page numbers
      RegExp(r'\n\s*\d+\s*\n'), // Standalone numbers (page numbers)
    ];
    
    List<String> pages = [];
    
    // Try to split by natural breaks first
    String remainingText = fullText;
    for (final pattern in pageBreakPatterns) {
      if (pattern.hasMatch(remainingText)) {
        pages = remainingText.split(pattern);
        if (kDebugMode) {
          print('[PdfTextService] Found ${pages.length} natural page breaks');
        }
        break;
      }
    }
    
    // If natural breaks don't work well, use content-based distribution
    if (pages.isEmpty || pages.length < 2) {
      if (kDebugMode) {
        print('[PdfTextService] No clear page breaks found, using content analysis');
      }
      
      pages = _analyzeContentForPages(fullText, totalPages);
    }
    
    // Ensure we have the right number of pages
    while (pages.length < totalPages) {
      // Split the longest page
      int longestIndex = 0;
      for (int i = 1; i < pages.length; i++) {
        if (pages[i].length > pages[longestIndex].length) {
          longestIndex = i;
        }
      }
      
      final longPage = pages[longestIndex];
      final midPoint = longPage.length ~/ 2;
      final firstHalf = longPage.substring(0, midPoint);
      final secondHalf = longPage.substring(midPoint);
      
      pages[longestIndex] = firstHalf;
      pages.insert(longestIndex + 1, secondHalf);
    }
    
    // If we have too many pages, merge the shortest ones
    while (pages.length > totalPages) {
      int shortestIndex = 0;
      for (int i = 1; i < pages.length - 1; i++) {
        if (pages[i].length < pages[shortestIndex].length) {
          shortestIndex = i;
        }
      }
      
      // Merge with next page
      pages[shortestIndex] = '${pages[shortestIndex]} ${pages[shortestIndex + 1]}';
      pages.removeAt(shortestIndex + 1);
    }
    
    // Create PageText objects
    for (int i = 0; i < pages.length; i++) {
      pageTexts.add(PageText(
        pageNumber: i + 1,
        text: pages[i],
      ));
      
      if (kDebugMode) {
        final pageText = pages[i];
        if (pageText.contains('订单')) {
          final orderCount = '订单'.allMatches(pageText).length;
          print('[PdfTextService] Distributed page ${i + 1}: $orderCount "订单" matches');
        }
      }
    }
    
    if (kDebugMode) {
      print('[PdfTextService] Successfully distributed text to ${pageTexts.length} pages');
    }
    
    return pageTexts;
  }
  
  /// Analyze content patterns to intelligently split into pages
  static List<String> _analyzeContentForPages(String text, int targetPages) {
    final pages = <String>[];
    
    // Look for content sections that might indicate page boundaries
    final sectionPatterns = [
      RegExp(r'\n[A-Z][^\.]*\n'), // Section headings
      RegExp(r'\n\d+[\.\)]\s*[A-Z]'), // Numbered sections
      RegExp(r'\n[一二三四五六七八九十]+[\.\)、]\s*'), // Chinese numbering
      RegExp(r'\n\s*第[一二三四五六七八九十\d]+[章节部分]\s*'), // Chinese chapters
    ];
    
    // Find potential break points
    final breakPoints = <int>[0];
    
    for (final pattern in sectionPatterns) {
      final matches = pattern.allMatches(text);
      for (final match in matches) {
        if (!breakPoints.contains(match.start)) {
          breakPoints.add(match.start);
        }
      }
    }
    
    breakPoints.add(text.length);
    breakPoints.sort();
    
    // If we have good break points, use them
    if (breakPoints.length > 2) {
      final avgPageLength = text.length / targetPages;
      final selectedBreaks = <int>[0];
      
      for (int i = 1; i < breakPoints.length - 1; i++) {
        final position = breakPoints[i];
        final currentPageLength = position - selectedBreaks.last;
        
        if (currentPageLength >= avgPageLength * 0.5) {
          selectedBreaks.add(position);
        }
      }
      
      selectedBreaks.add(text.length);
      
      // Create pages from selected break points
      for (int i = 0; i < selectedBreaks.length - 1; i++) {
        final start = selectedBreaks[i];
        final end = selectedBreaks[i + 1];
        pages.add(text.substring(start, end));
      }
    }
    
    // If content analysis didn't work, fall back to simple equal division
    if (pages.isEmpty) {
      final pageLength = text.length ~/ targetPages;
      for (int i = 0; i < targetPages; i++) {
        final start = i * pageLength;
        final end = (i == targetPages - 1) ? text.length : (i + 1) * pageLength;
        pages.add(text.substring(start, end));
      }
    }
    
    return pages;
  }
  
  /// Get PDF length (number of pages)
  static Future<int> getPdfPageCount(String filePath) async {
    try {
      return await ReadPdfText.getPDFlength(filePath);
    } catch (e) {
      if (kDebugMode) {
        print('[PdfTextService] Error getting PDF length: $e');
      }
      return 0;
    }
  }
  
  /// Test different extraction methods
  static Future<Map<String, dynamic>> testAllMethods(String filePath) async {
    final results = <String, dynamic>{};
    
    try {
      // Test PDF length
      final length = await getPdfPageCount(filePath);
      results['page_count'] = length;
      
      // Test full text extraction
      try {
        final fullText = await ReadPdfText.getPDFtext(filePath);
        results['full_text'] = {
          'success': true,
          'length': fullText.length,
          'has_order': fullText.contains('订单'),
          'order_count': '订单'.allMatches(fullText).length,
          'has_deepseek': fullText.contains('DeepSeek'),
          'sample': fullText.length > 100 ? fullText.substring(0, 100) : fullText,
        };
      } catch (e) {
        results['full_text'] = {
          'success': false,
          'error': e.toString(),
        };
      }
      
      // Test paginated extraction
      try {
        final pages = await ReadPdfText.getPDFtextPaginated(filePath);
        results['paginated_text'] = {
          'success': true,
          'page_count': pages.length,
          'total_length': pages.join('').length,
          'pages_with_order': pages.where((page) => page.contains('订单')).length,
        };
      } catch (e) {
        results['paginated_text'] = {
          'success': false,
          'error': e.toString(),
        };
      }
      
      return results;
      
    } catch (e) {
      results['error'] = e.toString();
      return results;
    }
  }
  
  /// Search for text in the extracted page texts
  static List<SearchMatch> searchText(List<PageText> pageTexts, String query) {
    if (query.isEmpty) return [];
    
    final List<SearchMatch> matches = [];
    final String lowerQuery = query.toLowerCase();
    
    for (final pageText in pageTexts) {
      final String lowerText = pageText.text.toLowerCase();
      int startIndex = 0;
      
      while (true) {
        final int index = lowerText.indexOf(lowerQuery, startIndex);
        if (index == -1) break;
        
        // Get surrounding context for better UX
        final int contextStart = (index - 20).clamp(0, pageText.text.length);
        final int contextEnd = (index + lowerQuery.length + 20).clamp(0, pageText.text.length);
        final String context = pageText.text.substring(contextStart, contextEnd);
        
        matches.add(SearchMatch(
          pageNumber: pageText.pageNumber,
          startIndex: index,
          endIndex: index + query.length,
          matchedText: pageText.text.substring(index, index + query.length),
          context: context,
          query: query,
        ));
        
        startIndex = index + 1;
      }
    }
    
    if (kDebugMode) {
      print('[PdfTextService] Found ${matches.length} matches for "$query"');
    }
    
    return matches;
  }
}

/// Represents text content of a single page
class PageText {
  final int pageNumber;
  final String text;
  
  PageText({
    required this.pageNumber,
    required this.text,
  });
}

/// Represents a search match in the PDF
class SearchMatch {
  final int pageNumber;
  final int startIndex;
  final int endIndex;
  final String matchedText;
  final String context;
  final String query;
  
  SearchMatch({
    required this.pageNumber,
    required this.startIndex,
    required this.endIndex,
    required this.matchedText,
    required this.context,
    required this.query,
  });
  
  @override
  String toString() {
    return 'SearchMatch(page: $pageNumber, text: "$matchedText")';
  }
}