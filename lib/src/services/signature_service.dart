import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/signature.dart';

class SignatureService {
  static const _key = 'signatures_v1';
  static SignatureService? _instance;
  
  late SharedPreferences _prefs;
  final List<Signature> _signatures = [];
  late String _signaturesDir;

  static SignatureService get instance => _instance ??= SignatureService._();
  
  SignatureService._();

  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    
    final appDocDir = await getApplicationDocumentsDirectory();
    _signaturesDir = '${appDocDir.path}/signatures';
    final dir = Directory(_signaturesDir);
    if (!await dir.exists()) {
      await dir.create(recursive: true);
    }
    
    await _loadSignatures();
  }

  Future<void> _loadSignatures() async {
    final raw = _prefs.getString(_key);
    if (raw != null) {
      try {
        final list = List<Map<String, dynamic>>.from(jsonDecode(raw) as List);
        _signatures.clear();
        _signatures.addAll(list.map((json) => Signature.fromJson(json)));
        
        if (kDebugMode) {
          print('[SignatureService] Loaded ${_signatures.length} signatures');
        }
      } catch (e) {
        if (kDebugMode) {
          print('[SignatureService] Error loading signatures: $e');
        }
      }
    }
  }

  List<Signature> get all => List.unmodifiable(_signatures);

  Future<void> saveSignature(Signature signature) async {
    // If a signature with the same id exists, replace it (update);
    // otherwise, add as a new signature.
    final existingIndex = _signatures.indexWhere((s) => s.id == signature.id);
    if (existingIndex >= 0) {
      _signatures[existingIndex] = signature;
      // Remove existing thumbnail so it will be regenerated with updated strokes/size
      final thumbnailFile = File('$_signaturesDir/${signature.id}.png');
      if (await thumbnailFile.exists()) {
        await thumbnailFile.delete();
      }
    } else {
      _signatures.add(signature);
    }
    await _persistSignatures();
    
    if (kDebugMode) {
      print('[SignatureService] Saved signature: ${signature.id}');
    }
  }

  Future<void> deleteSignature(String id) async {
    _signatures.removeWhere((signature) => signature.id == id);
    await _persistSignatures();
    
    final thumbnailFile = File('$_signaturesDir/$id.png');
    if (await thumbnailFile.exists()) {
      await thumbnailFile.delete();
    }
    
    if (kDebugMode) {
      print('[SignatureService] Deleted signature: $id');
    }
  }

  Future<void> _persistSignatures() async {
    final json = jsonEncode(_signatures.map((s) => s.toJson()).toList());
    await _prefs.setString(_key, json);
  }

  Future<String> generateThumbnail(Signature signature) async {
    final thumbnailPath = '$_signaturesDir/${signature.id}.png';
    final file = File(thumbnailPath);
    
    if (await file.exists()) {
      return thumbnailPath;
    }

    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    const thumbnailSize = Size(200, 100);
    
    final scaleX = thumbnailSize.width / signature.canvasSize.width;
    final scaleY = thumbnailSize.height / signature.canvasSize.height;
    final scale = scaleX < scaleY ? scaleX : scaleY;

    final offsetX = (thumbnailSize.width - signature.canvasSize.width * scale) / 2;
    final offsetY = (thumbnailSize.height - signature.canvasSize.height * scale) / 2;

    canvas.drawRect(
      Rect.fromLTWH(0, 0, thumbnailSize.width, thumbnailSize.height),
      Paint()..color = Colors.white,
    );

    for (final stroke in signature.strokes) {
      if (stroke.points.length < 2) continue;
      
      paint.color = stroke.color;
      paint.strokeWidth = stroke.strokeWidth * scale;
      
      final path = Path();
      final firstPoint = stroke.points.first;
      path.moveTo(
        offsetX + firstPoint.dx * scale,
        offsetY + firstPoint.dy * scale,
      );
      
      for (int i = 1; i < stroke.points.length; i++) {
        final point = stroke.points[i];
        path.lineTo(
          offsetX + point.dx * scale,
          offsetY + point.dy * scale,
        );
      }
      
      canvas.drawPath(path, paint);
    }

    final picture = recorder.endRecording();
    final image = await picture.toImage(
      thumbnailSize.width.toInt(),
      thumbnailSize.height.toInt(),
    );
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    
    if (byteData != null) {
      await file.writeAsBytes(byteData.buffer.asUint8List());
    }

    return thumbnailPath;
  }

  String generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }
}
