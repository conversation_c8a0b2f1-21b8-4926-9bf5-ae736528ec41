import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

/// Metadata for a cached PDF file
class PdfCacheMetadata {
  final String sourcePath;
  final String sourceFileName;
  final int sourceSize;
  final DateTime sourceModified;
  final String pdfPath;
  final DateTime cacheCreated;
  final String sourceHash;

  const PdfCacheMetadata({
    required this.sourcePath,
    required this.sourceFileName,
    required this.sourceSize,
    required this.sourceModified,
    required this.pdfPath,
    required this.cacheCreated,
    required this.sourceHash,
  });

  Map<String, dynamic> toJson() => {
    'sourcePath': sourcePath,
    'sourceFileName': sourceFileName,
    'sourceSize': sourceSize,
    'sourceModified': sourceModified.millisecondsSinceEpoch,
    'pdfPath': pdfPath,
    'cacheCreated': cacheCreated.millisecondsSinceEpoch,
    'sourceHash': sourceHash,
  };

  factory PdfCacheMetadata.fromJson(Map<String, dynamic> json) => PdfCacheMetadata(
    sourcePath: json['sourcePath'] as String,
    sourceFileName: json['sourceFileName'] as String,
    sourceSize: json['sourceSize'] as int,
    sourceModified: DateTime.fromMillisecondsSinceEpoch(json['sourceModified'] as int),
    pdfPath: json['pdfPath'] as String,
    cacheCreated: DateTime.fromMillisecondsSinceEpoch(json['cacheCreated'] as int),
    sourceHash: json['sourceHash'] as String,
  );
}

/// Service to manage PDF caching for converted Office documents
class GeneratedPdfsCacheService {
  static const String _cacheDirectoryName = 'Generated PDFs';
  
  /// Get the Generated PDFs directory path
  static Future<Directory> getCacheDirectory() async {
    final appDocDir = await getApplicationDocumentsDirectory();
    final cacheDir = Directory(path.join(appDocDir.path, _cacheDirectoryName));
    
    // Create directory if it doesn't exist
    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
      if (kDebugMode) {
        print('[GeneratedPdfsCacheService] Created cache directory: ${cacheDir.path}');
      }
    }
    
    return cacheDir;
  }

  /// Generate a simple hash for file content verification
  static String _generateFileHash(String filePath, int fileSize, DateTime modified) {
    // Simple hash based on path, size and modification time
    final combined = '$filePath-$fileSize-${modified.millisecondsSinceEpoch}';
    return combined.hashCode.abs().toRadixString(16);
  }

  /// Check if a cached PDF exists and is still valid for the given source file
  static Future<String?> getCachedPdfPath({
    required String sourceFilePath,
    required String sourceFileName,
  }) async {
    try {
      if (kDebugMode) {
        print('[GeneratedPdfsCacheService] Checking cache for source file: $sourceFilePath');
      }

      final cacheDir = await getCacheDirectory();
      if (!await cacheDir.exists()) {
        return null;
      }

      final sourceFile = File(sourceFilePath);
      if (!await sourceFile.exists()) {
        if (kDebugMode) {
          print('[GeneratedPdfsCacheService] Source file does not exist: $sourceFilePath');
        }
        return null;
      }
      
      final sourceStat = await sourceFile.stat();
      final currentHash = _generateFileHash(sourceFilePath, sourceStat.size, sourceStat.modified);

      // Iterate over all files in the cache directory to find a matching meta file
      await for (final entity in cacheDir.list()) {
        if (entity is File && entity.path.endsWith('.meta')) {
          try {
            final metaFile = entity;
            final metaContent = await metaFile.readAsString();
            final metaJson = jsonDecode(metaContent) as Map<String, dynamic>;
            final metadata = PdfCacheMetadata.fromJson(metaJson);

            // Check if the metadata's source path matches the current file path
            if (metadata.sourcePath == sourceFilePath) {
              if (kDebugMode) {
                print('[GeneratedPdfsCacheService] Found matching metadata file: ${metaFile.path}');
              }

              // Verify that the cached PDF file still exists
              final pdfFile = File(metadata.pdfPath);
              if (!await pdfFile.exists()) {
                if (kDebugMode) {
                  print('[GeneratedPdfsCacheService] Cached PDF file missing: ${metadata.pdfPath}');
                }
                // Clean up orphaned metadata file
                await metaFile.delete();
                continue; // Continue to next meta file
              }

              // Check if the source file has changed using the hash and stats
              if (metadata.sourceHash == currentHash &&
                  metadata.sourceSize == sourceStat.size &&
                  metadata.sourceModified == sourceStat.modified) {
                
                if (kDebugMode) {
                  print('[GeneratedPdfsCacheService] Valid cached PDF found: ${metadata.pdfPath}');
                }
                return metadata.pdfPath; // Valid cache found
              } else {
                if (kDebugMode) {
                  print('[GeneratedPdfsCacheService] Source file has changed, cache invalid for ${metaFile.path}');
                  print('[GeneratedPdfsCacheService]   Cached hash: ${metadata.sourceHash}, current: $currentHash');
                  print('[GeneratedPdfsCacheService]   Cached size: ${metadata.sourceSize}, current: ${sourceStat.size}');
                  print('[GeneratedPdfsCacheService]   Cached modified: ${metadata.sourceModified}, current: ${sourceStat.modified}');
                }
                // If the source has changed, this cache is invalid, but another meta file might be valid
                // (e.g., if the same file was opened from different paths).
                // We continue checking other meta files.
              }
            }
          } catch (e) {
            if (kDebugMode) {
              print('[GeneratedPdfsCacheService] Error processing metadata file ${entity.path}: $e');
            }
            // Ignore corrupted or invalid meta files and continue
          }
        }
      }

      if (kDebugMode) {
        print('[GeneratedPdfsCacheService] No valid cached PDF found for: $sourceFilePath');
      }
      
      return null; // No valid cache found
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('[GeneratedPdfsCacheService] Error checking cache: $e');
        print('[GeneratedPdfsCacheService] Stack trace: $stackTrace');
      }
      return null;
    }
  }

  /// Generate a unique PDF filename, handling duplicates with _1, _2, etc.
  static Future<String> generateUniquePdfPath({
    required String sourceFileName,
  }) async {
    final cacheDir = await getCacheDirectory();
    final baseFileName = path.basenameWithoutExtension(sourceFileName);
    
    String candidateName = '$baseFileName.pdf';
    String candidatePath = path.join(cacheDir.path, candidateName);
    
    int counter = 1;
    while (await File(candidatePath).exists()) {
      candidateName = '${baseFileName}_$counter.pdf';
      candidatePath = path.join(cacheDir.path, candidateName);
      counter++;
    }
    
    if (kDebugMode) {
      print('[GeneratedPdfsCacheService] Generated unique PDF path: $candidatePath');
    }
    
    return candidatePath;
  }

  /// Cache a generated PDF with metadata
  static Future<String?> cachePdf({
    required String sourceFilePath,
    required String sourceFileName,
    required List<int> pdfBytes,
  }) async {
    try {
      if (kDebugMode) {
        print('[GeneratedPdfsCacheService] Caching PDF for: $sourceFileName');
      }

      // Get source file info
      final sourceFile = File(sourceFilePath);
      if (!await sourceFile.exists()) {
        if (kDebugMode) {
          print('[GeneratedPdfsCacheService] Source file does not exist: $sourceFilePath');
        }
        return null;
      }
      
      final sourceStat = await sourceFile.stat();
      
      // Generate unique PDF path
      final pdfPath = await generateUniquePdfPath(sourceFileName: sourceFileName);
      
      // Write PDF file
      final pdfFile = File(pdfPath);
      await pdfFile.writeAsBytes(pdfBytes);
      
      // Create metadata
      final metadata = PdfCacheMetadata(
        sourcePath: sourceFilePath,
        sourceFileName: sourceFileName,
        sourceSize: sourceStat.size,
        sourceModified: sourceStat.modified,
        pdfPath: pdfPath,
        cacheCreated: DateTime.now(),
        sourceHash: _generateFileHash(sourceFilePath, sourceStat.size, sourceStat.modified),
      );
      
      // Write metadata file
      final metaPath = '$pdfPath.meta';
      final metaFile = File(metaPath);
      await metaFile.writeAsString(jsonEncode(metadata.toJson()));
      
      if (kDebugMode) {
        print('[GeneratedPdfsCacheService] PDF cached successfully: $pdfPath');
        print('[GeneratedPdfsCacheService] Metadata saved: $metaPath');
      }
      
      return pdfPath;
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('[GeneratedPdfsCacheService] Error caching PDF: $e');
        print('[GeneratedPdfsCacheService] Stack trace: $stackTrace');
      }
      return null;
    }
  }

  /// Clean up old cached PDFs (older than specified duration)
  static Future<void> cleanupOldCache({Duration maxAge = const Duration(days: 30)}) async {
    try {
      final cacheDir = await getCacheDirectory();
      if (!await cacheDir.exists()) {
        return;
      }

      final cutoffTime = DateTime.now().subtract(maxAge);
      int deletedCount = 0;

      if (kDebugMode) {
        print('[GeneratedPdfsCacheService] Starting cache cleanup, removing files older than $cutoffTime');
      }

      await for (final entity in cacheDir.list()) {
        if (entity is File && entity.path.endsWith('.meta')) {
          try {
            final metaContent = await entity.readAsString();
            final metaJson = jsonDecode(metaContent) as Map<String, dynamic>;
            final metadata = PdfCacheMetadata.fromJson(metaJson);
            
            if (metadata.cacheCreated.isBefore(cutoffTime)) {
              // Delete PDF file
              final pdfFile = File(metadata.pdfPath);
              if (await pdfFile.exists()) {
                await pdfFile.delete();
              }
              
              // Delete metadata file
              await entity.delete();
              deletedCount++;
              
              if (kDebugMode) {
                print('[GeneratedPdfsCacheService] Deleted old cache: ${metadata.pdfPath}');
              }
            }
          } catch (e) {
            if (kDebugMode) {
              print('[GeneratedPdfsCacheService] Error processing metadata file ${entity.path}: $e');
            }
          }
        }
      }

      if (kDebugMode) {
        print('[GeneratedPdfsCacheService] Cache cleanup completed. Deleted $deletedCount files');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[GeneratedPdfsCacheService] Error during cache cleanup: $e');
      }
    }
  }

  /// Get total size of cached PDFs
  static Future<int> getCacheTotalSize() async {
    try {
      final cacheDir = await getCacheDirectory();
      if (!await cacheDir.exists()) {
        return 0;
      }

      int totalSize = 0;
      await for (final entity in cacheDir.list()) {
        if (entity is File) {
          try {
            final stat = await entity.stat();
            totalSize += stat.size;
          } catch (e) {
            // Ignore errors for individual files
          }
        }
      }

      return totalSize;
    } catch (e) {
      if (kDebugMode) {
        print('[GeneratedPdfsCacheService] Error calculating cache size: $e');
      }
      return 0;
    }
  }

  /// Clear all cached PDFs
  static Future<void> clearAllCache() async {
    try {
      final cacheDir = await getCacheDirectory();
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
        if (kDebugMode) {
          print('[GeneratedPdfsCacheService] All cache cleared');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[GeneratedPdfsCacheService] Error clearing cache: $e');
      }
    }
  }
}