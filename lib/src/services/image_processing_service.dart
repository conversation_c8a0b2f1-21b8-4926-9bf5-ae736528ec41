import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:mime/mime.dart';
import 'package:http/io_client.dart';

import '../config/image_processing_config.dart';
import 'network_permission_service.dart';

/// 图像分辨率检查工具
class ImageResolutionChecker {
  
  /// 检查图像分辨率是否符合处理要求
  /// 基于服务端逻辑：宽高都 < 200px OR 任一边 < 100px 为太低
  static bool isResolutionTooLow(int width, int height) {
    return (width < 200 && height < 200) || width < 100 || height < 100;
  }
  
  /// 检查分辨率是否超出处理范围
  static bool isResolutionOutOfRange(int width, int height) {
    return width < 20 || height < 20 || width > 10000 || height > 10000;
  }
  
  /// 检查是否需要自动缩放（任一边 > 8192px）
  static bool needsAutoScaling(int width, int height) {
    return width > 8192 || height > 8192;
  }
  
  /// 获取图像分辨率
  static Future<Map<String, int>?> getImageDimensions(File imageFile) async {
    try {
      // 这里简化实现，实际项目中可能需要用到image包来获取真实尺寸
      // 目前先返回一个估算值，实际实现需要依赖image解码库
      final bytes = await imageFile.readAsBytes();
      if (bytes.length < 1000) {
        // 文件太小，可能分辨率很低
        return {'width': 50, 'height': 50};
      } else if (bytes.length > 10 * 1024 * 1024) {
        // 文件很大，可能分辨率很高
        return {'width': 4000, 'height': 3000};
      } else {
        // 中等大小文件，假设合理分辨率
        return {'width': 1920, 'height': 1080};
      }
    } catch (e) {
      if (kDebugMode) {
        print('[ImageResolutionChecker] Failed to get dimensions: $e');
      }
      return null;
    }
  }
}


class ProcessingSession {
  final String sessionId;
  final String ticket;
  final int ttl;
  final DateTime createdAt;

  ProcessingSession({
    required this.sessionId,
    required this.ticket,
    required this.ttl,
    required this.createdAt,
  });

  bool get isExpired {
    final expiryTime = createdAt.add(Duration(seconds: ttl));
    return DateTime.now().isAfter(expiryTime);
  }
}

class ProcessedImage {
  final String id;
  final String originalPath;
  final String processedUrl;
  final Map<String, dynamic> metadata;

  ProcessedImage({
    required this.id,
    required this.originalPath,
    required this.processedUrl,
    required this.metadata,
  });
}

class ImageProcessingService {
  final ImageProcessingConfig config;
  final http.Client _httpClient;
  ProcessingSession? _currentSession;

  ImageProcessingService({
    ImageProcessingConfig? config,
    http.Client? httpClient,
  }) : config = config ?? ImageProcessingConfig.instance,
        _httpClient = httpClient ?? _createHttpClient();

  static http.Client _createHttpClient() {
    if (kDebugMode) {
      // 在调试模式下忽略SSL证书错误
      final httpClient = HttpClient();
      httpClient.badCertificateCallback = (cert, host, port) {
        if (kDebugMode) {
          print('[ImageProcessingService] Ignoring SSL certificate error for $host:$port in debug mode');
        }
        return true; // 忽略证书错误
      };
      return IOClient(httpClient);
    }
    return http.Client();
  }

  Future<bool> initialize() async {
    try {
      // 首先检查网络权限
      final hasNetworkPermission = await NetworkPermissionService.instance.initialize();
      if (!hasNetworkPermission) {
        if (kDebugMode) {
          print('[ImageProcessingService] Network permission not available');
        }
        return false;
      }
      
      await _ensureValidSession();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('[ImageProcessingService] Failed to initialize: $e');
      }
      return false;
    }
  }

  Future<void> _ensureValidSession() async {
    if (_currentSession == null || _currentSession!.isExpired) {
      await _createNewSession();
    }
  }

  Future<void> _createNewSession() async {
    if (kDebugMode) {
      print('[ImageProcessingService] Creating new session...');
      print('[ImageProcessingService] Server URL: ${config.baseUrl}/api/native/session');
      print('[ImageProcessingService] Client secret: "${config.clientSecret}"');
    }

    final url = Uri.parse('${config.baseUrl}/api/native/session');
    final requestBody = json.encode({'credential': config.clientSecret});
    
    if (kDebugMode) {
      print('[ImageProcessingService] Request body: $requestBody');
    }
    
    final response = await _httpClient.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: requestBody,
    ).timeout(config.timeout);

    if (kDebugMode) {
      print('[ImageProcessingService] Response status: ${response.statusCode}');
      print('[ImageProcessingService] Response headers: ${response.headers}');
      print('[ImageProcessingService] Response body length: ${response.body.length}');
      print('[ImageProcessingService] Response body: ${response.body}');
    }

    if (response.statusCode != 200) {
      if (kDebugMode) {
        print('[ImageProcessingService] Session creation failed with status ${response.statusCode}');
        print('[ImageProcessingService] Response body: ${response.body}');
      }
      throw Exception('Session creation failed: ${response.statusCode}');
    }

    final responseData = json.decode(response.body);
    
    if (kDebugMode) {
      print('[ImageProcessingService] Response JSON keys: ${responseData.keys.toList()}');
      print('[ImageProcessingService] Response success: ${responseData['success']}');
      if (responseData.containsKey('data')) {
        print('[ImageProcessingService] Response data field present: ${responseData['data'] != null}');
        print('[ImageProcessingService] Response data type: ${responseData['data'].runtimeType}');
        if (responseData['data'] is String) {
          print('[ImageProcessingService] Response data length: ${(responseData['data'] as String).length}');
        }
      } else {
        print('[ImageProcessingService] Response data field missing');
      }
    }
    
    if (responseData['success'] != true) {
      if (kDebugMode) {
        print('[ImageProcessingService] Server returned success=false: ${responseData['message']}');
      }
      throw Exception('Session creation failed: ${responseData['message'] ?? 'Unknown error'}');
    }

    final encryptedData = responseData['data'] as String;
    
    if (kDebugMode) {
      print('[ImageProcessingService] Encrypted session data received, length: ${encryptedData.length}');
      print('[ImageProcessingService] Starting decryption...');
    }
    
    final decryptedPayload = _decryptSessionData(encryptedData);
    
    final sessionId = decryptedPayload['sid'] as String;
    final ticket = decryptedPayload['ticket'] as String;
    final ttl = decryptedPayload['ttl'] as int? ?? config.defaultTtl;

    _currentSession = ProcessingSession(
      sessionId: sessionId,
      ticket: ticket,
      ttl: ttl,
      createdAt: DateTime.now(),
    );

    if (kDebugMode) {
      print('[ImageProcessingService] Session created: ${sessionId.substring(0, 8)}...');
    }
  }

  Map<String, dynamic> _decryptSessionData(String encryptedData) {
    try {
      if (kDebugMode) {
        print('[ImageProcessingService] [Crypto] Starting decryption...');
        print('[ImageProcessingService] [Crypto] Encrypted data length: ${encryptedData.length}');
        print('[ImageProcessingService] [Crypto] Encrypted data (base64): ${encryptedData.length > 100 ? encryptedData.substring(0, 100) + '...' : encryptedData}');
      }
      
      final encryptedBytes = base64.decode(encryptedData);
      
      if (kDebugMode) {
        print('[ImageProcessingService] [Crypto] Decoded bytes length: ${encryptedBytes.length}');
        print('[ImageProcessingService] [Crypto] Decoded bytes (hex): ${encryptedBytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join('')}');
      }
      
      final iv = encryptedBytes.sublist(0, 12);
      final ciphertext = encryptedBytes.sublist(12, encryptedBytes.length - 16);
      final authTag = encryptedBytes.sublist(encryptedBytes.length - 16);
      
      if (kDebugMode) {
        print('[ImageProcessingService] [Crypto] IV (hex): ${iv.map((b) => b.toRadixString(16).padLeft(2, '0')).join('')}');
        print('[ImageProcessingService] [Crypto] Ciphertext length: ${ciphertext.length}');
        print('[ImageProcessingService] [Crypto] Ciphertext (hex): ${ciphertext.map((b) => b.toRadixString(16).padLeft(2, '0')).join('')}');
        print('[ImageProcessingService] [Crypto] Auth Tag (hex): ${authTag.map((b) => b.toRadixString(16).padLeft(2, '0')).join('')}');
      }
      
      // 使用SHA-256派生32字节密钥，与服务端保持一致
      final keyBytes = sha256.convert(utf8.encode(config.clientSecret)).bytes;
      
      if (kDebugMode) {
        print('[ImageProcessingService] [Crypto] Client secret: "${config.clientSecret}"');
        print('[ImageProcessingService] [Crypto] SHA-256 key length: ${keyBytes.length}');
        print('[ImageProcessingService] [Crypto] SHA-256 key (hex): ${keyBytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join('')}');
      }
      
      final key = encrypt.Key(Uint8List.fromList(keyBytes));
      final encrypter = encrypt.Encrypter(encrypt.AES(key, mode: encrypt.AESMode.gcm));
      
      if (kDebugMode) {
        print('[ImageProcessingService] [Crypto] Created AES-GCM encrypter');
        print('[ImageProcessingService] [Crypto] Attempting decryption...');
      }
      
      final encrypted = encrypt.Encrypted(Uint8List.fromList(ciphertext + authTag));
      final decrypted = encrypter.decrypt(encrypted, iv: encrypt.IV(iv));
      
      if (kDebugMode) {
        print('[ImageProcessingService] [Crypto] Decryption successful!');
        print('[ImageProcessingService] [Crypto] Decrypted data: $decrypted');
      }
      
      final result = json.decode(decrypted);
      
      if (kDebugMode) {
        print('[ImageProcessingService] [Crypto] JSON parsing successful');
        print('[ImageProcessingService] [Crypto] Session data keys: ${result.keys.toList()}');
      }
      
      return result;
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('[ImageProcessingService] [Crypto] Decryption failed!');
        print('[ImageProcessingService] [Crypto] Error type: ${e.runtimeType}');
        print('[ImageProcessingService] [Crypto] Error details: $e');
        print('[ImageProcessingService] [Crypto] Stack trace: $stackTrace');
      }
      throw Exception('Failed to decrypt session data: $e');
    }
  }

  Future<List<ProcessedImage>> processImages(
    List<File> imageFiles, {
    bool autoProcess = true,
    Map<String, dynamic>? options,
  }) async {
    if (kDebugMode) {
      print('[ImageProcessingService] Processing ${imageFiles.length} images...');
    }

    final processedImages = <ProcessedImage>[];
    final validImages = <File>[];
    
    // 先进行本地分辨率预检查
    for (int i = 0; i < imageFiles.length; i++) {
      final file = imageFiles[i];
      final dimensions = await ImageResolutionChecker.getImageDimensions(file);
      
      if (dimensions == null) {
        // 无法获取尺寸，返回错误状态
        processedImages.add(ProcessedImage(
          id: 'error_${i}_${DateTime.now().millisecondsSinceEpoch}',
          originalPath: file.path,
          processedUrl: '',
          metadata: {'error': 'Unable to read image dimensions', 'isError': true},
        ));
        continue;
      }
      
      final width = dimensions['width']!;
      final height = dimensions['height']!;
      
      if (ImageResolutionChecker.isResolutionTooLow(width, height)) {
        // 分辨率太低，跳过服务端处理
        if (kDebugMode) {
          print('[ImageProcessingService] Image ${file.path} resolution too low: ${width}x$height');
        }
        
        processedImages.add(ProcessedImage(
          id: 'lowres_${i}_${DateTime.now().millisecondsSinceEpoch}',
          originalPath: file.path,
          processedUrl: '',
          metadata: {
            'isLowResolution': true,
            'width': width,
            'height': height,
            'reason': 'Resolution too low for processing',
          },
        ));
        continue;
      }
      
      if (ImageResolutionChecker.isResolutionOutOfRange(width, height)) {
        // 分辨率超出范围
        processedImages.add(ProcessedImage(
          id: 'outofrange_${i}_${DateTime.now().millisecondsSinceEpoch}',
          originalPath: file.path,
          processedUrl: '',
          metadata: {
            'error': 'Image resolution out of supported range: ${width}x${height}px',
            'isError': true,
          },
        ));
        continue;
      }
      
      // 分辨率合适，添加到待处理列表
      validImages.add(file);
    }
    
    // 如果没有有效图片，直接返回
    if (validImages.isEmpty) {
      if (kDebugMode) {
        print('[ImageProcessingService] No valid images to process');
      }
      return processedImages;
    }
    
    // 处理有效的图片
    try {
      await _ensureValidSession();
      
      final url = Uri.parse('${config.baseUrl}/api/images/upload');
      final request = http.MultipartRequest('POST', url);
      
      final authParams = {
        'autoProcess': autoProcess,
        'options': options ?? {},
      };
      
      if (kDebugMode) {
        print('[ImageProcessingService] Upload URL: $url');
        print('[ImageProcessingService] Auth params: $authParams');
        print('[ImageProcessingService] Session ID: ${_currentSession!.sessionId}');
        print('[ImageProcessingService] Session ticket length: ${_currentSession!.ticket.length}');
      }
      
      _addAuthHeaders(request.headers, 'POST', '/api/images/upload', authParams, isMultipart: true);

      request.headers['X-Client-Type'] = 'mobile';
      request.headers['X-Client-ID'] = _currentSession!.sessionId;
      
      if (kDebugMode) {
        print('[ImageProcessingService] Request headers before adding files:');
        request.headers.forEach((key, value) {
          print('[ImageProcessingService]   $key: $value');
        });
      }

      for (int i = 0; i < validImages.length; i++) {
        final file = validImages[i];
        final mimeType = _lookupMimeType(file.path);
        final multipartFile = await http.MultipartFile.fromPath(
          'files',
          file.path,
          filename: 'image_$i.jpg',
          contentType: MediaType.parse(mimeType),
        );
        request.files.add(multipartFile);
        
        if (kDebugMode) {
          print('[ImageProcessingService] Added file $i: ${file.path} (size: ${await file.length()} bytes)');
        }
      }
      
      if (kDebugMode) {
        print('[ImageProcessingService] Total files to upload: ${request.files.length}');
        print('[ImageProcessingService] Request final headers:');
        request.headers.forEach((key, value) {
          print('[ImageProcessingService]   $key: $value');
        });
        print('[ImageProcessingService] Sending request...');
      }

      final response = await _httpClient.send(request).timeout(config.timeout);
      final responseBody = await response.stream.bytesToString();
      
      if (kDebugMode) {
        print('[ImageProcessingService] Upload response status: ${response.statusCode}');
        print('[ImageProcessingService] Upload response headers: ${response.headers}');
        print('[ImageProcessingService] Upload response body: $responseBody');
      }

      if (response.statusCode != 200) {
        throw Exception('Image processing failed: ${response.statusCode} - $responseBody');
      }

      final responseData = json.decode(responseBody);
      if (responseData['success'] != true) {
        throw Exception('Image processing failed: ${responseData['message'] ?? 'Unknown error'}');
      }

      // Server returns: { success: true, data: { images: [...] } }
      final data = responseData['data'];
      final results = (data != null && data['images'] != null)
          ? (data['images'] as List<dynamic>)
          : <dynamic>[];

      for (int i = 0; i < results.length; i++) {
        final result = results[i] as Map<String, dynamic>;
        final id = (result['id'] ?? 'unknown_${i}_${DateTime.now().millisecondsSinceEpoch}').toString();
        final cropped = result['cropped'] as String?;
        final original = result['original'] as String?;
        // Prefer cropped; fallback to original
        final base64Image = cropped ?? original;
        String processedUrl = '';
        if (base64Image != null && base64Image.isNotEmpty) {
          // Default to JPEG; server currently returns base64 strings without data URL prefix
          processedUrl = 'data:image/jpeg;base64,$base64Image';
        }

        final meta = <String, dynamic>{
          'originalName': result['originalName'],
          'isLowResolution': result['isLowResolution'] == true,
          'enhancement': result['enhancement'],
          'confidence': result['confidence'],
          'corners': result['corners'],
          'timestamp': result['timestamp'],
        };

        processedImages.add(ProcessedImage(
          id: id,
          originalPath: validImages[i].path,
          processedUrl: processedUrl,
          metadata: meta,
        ));
      }

      if (kDebugMode) {
        print('[ImageProcessingService] Successfully processed ${results.length} valid images');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('[ImageProcessingService] Server processing failed: $e');
      }
      
      // 服务端处理失败，为有效图片添加失败状态
      for (final file in validImages) {
        processedImages.add(ProcessedImage(
          id: 'failed_${DateTime.now().millisecondsSinceEpoch}',
          originalPath: file.path,
          processedUrl: '',
          metadata: {'error': e.toString(), 'isError': true},
        ));
      }
    }

    return processedImages;
  }

  @Deprecated('PDF generation is now handled locally. Use _generatePdfLocally in EditorScreen instead.')
  Future<File> generatePdf(List<ProcessedImage> processedImages, String outputPath) async {
    await _ensureValidSession();

    if (kDebugMode) {
      print('[ImageProcessingService] Generating PDF for ${processedImages.length} images...');
    }

    final url = Uri.parse('${config.baseUrl}/api/pdf/generate');
    final requestBody = {
      'images': processedImages.map((img) => {
        'id': img.id,
        'url': img.processedUrl,
        'metadata': img.metadata,
      }).toList(),
      'options': {
        'quality': 'high',
        'pageFormat': 'auto',
      },
    };

    final response = await _httpClient.post(
      url,
      headers: _buildAuthHeaders('POST', '/api/pdf/generate', requestBody),
      body: json.encode(requestBody),
    ).timeout(config.timeout);

    if (response.statusCode != 200) {
      throw Exception('PDF generation failed: ${response.statusCode}');
    }

    final outputFile = File(outputPath);
    await outputFile.writeAsBytes(response.bodyBytes);

    if (kDebugMode) {
      print('[ImageProcessingService] PDF generated successfully: $outputPath');
    }

    return outputFile;
  }

  Map<String, String> _buildAuthHeaders(String method, String path, Map<String, dynamic> params) {
    final headers = <String, String>{};
    _addAuthHeaders(headers, method, path, params, isMultipart: false);
    headers['Content-Type'] = 'application/json';
    headers['X-Client-Type'] = 'mobile';
    headers['X-Client-ID'] = _currentSession!.sessionId;
    return headers;
  }

  void _addAuthHeaders(Map<String, String> headers, String method, String path, Map<String, dynamic> params, {bool isMultipart = false}) {
    final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final nonce = _generateNonce();
    
    final signatureParams = <String, String>{
      '_method': method.toUpperCase(),
      '_timestamp': timestamp.toString(),
      '_path': path,
      '_nonce': nonce,
    };
    
    // Handle multipart form data differently to match server expectations
    if (isMultipart) {
      // Server-side HMAC for multipart signs: _nonce + options (JSON) + files([])
      // It conditionally includes autoProcess only if present in body; since we
      // don't send it as a field here, omit it from the signature to match server.

      // options: always JSON-encoded; server defaults to {} if absent
      if (params.containsKey('options')) {
        signatureParams['options'] = json.encode(params['options']);
      } else {
        signatureParams['options'] = json.encode({});
      }

      // files: sign as empty array to mirror server logic
      signatureParams['files'] = json.encode([]);
    } else {
      // For regular JSON requests, JSON-encode all parameters
      for (final entry in params.entries) {
        signatureParams[entry.key] = json.encode(entry.value);
      }
    }

    final sortedKeys = signatureParams.keys.toList()..sort();
    final signatureString = sortedKeys
        .map((key) => '$key=${signatureParams[key]}')
        .join('&');

    final hmacSha256 = Hmac(sha256, utf8.encode(_currentSession!.ticket));
    final signature = hmacSha256.convert(utf8.encode(signatureString));

    if (kDebugMode) {
      print('[ImageProcessingService] [HMAC] Generating signature...');
      print('[ImageProcessingService] [HMAC] Method: $method');
      print('[ImageProcessingService] [HMAC] Path: $path');
      print('[ImageProcessingService] [HMAC] Timestamp: $timestamp');
      print('[ImageProcessingService] [HMAC] Nonce: $nonce');
      print('[ImageProcessingService] [HMAC] Params: $params');
      print('[ImageProcessingService] [HMAC] Signature params: $signatureParams');
      print('[ImageProcessingService] [HMAC] Sorted keys: $sortedKeys');
      print('[ImageProcessingService] [HMAC] Signature string: $signatureString');
      print('[ImageProcessingService] [HMAC] Ticket (first 10 chars): ${_currentSession!.ticket.substring(0, 10)}...');
      print('[ImageProcessingService] [HMAC] Ticket length: ${_currentSession!.ticket.length}');
      print('[ImageProcessingService] [HMAC] Generated signature: $signature');
    }

    headers['X-Timestamp'] = timestamp.toString();
    headers['X-Trace-ID'] = nonce;
    headers['X-Auth-Token'] = signature.toString();
  }

  String _generateNonce() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(16, (_) => chars.codeUnitAt(random.nextInt(chars.length)))
    );
  }

  String _lookupMimeType(String path) {
    try {
      final mt = lookupMimeType(path);
      if (mt != null) return mt;
    } catch (_) {}
    final lower = path.toLowerCase();
    if (lower.endsWith('.jpg') || lower.endsWith('.jpeg')) return 'image/jpeg';
    if (lower.endsWith('.png')) return 'image/png';
    if (lower.endsWith('.bmp')) return 'image/bmp';
    if (lower.endsWith('.webp')) return 'image/webp';
    if (lower.endsWith('.tif') || lower.endsWith('.tiff')) return 'image/tiff';
    if (lower.endsWith('.gif')) return 'image/gif';
    return 'application/octet-stream';
  }

  Future<bool> isServiceAvailable() async {
    try {
      // 先检查网络权限
      if (!NetworkPermissionService.instance.hasNetworkPermission) {
        final hasPermission = await NetworkPermissionService.instance.requestNetworkPermission();
        if (!hasPermission) {
          if (kDebugMode) {
            print('[ImageProcessingService] Network permission denied');
          }
          return false;
        }
      }
      
      final url = Uri.parse('${config.baseUrl}/api/health');
      final response = await _httpClient.get(url).timeout(
        const Duration(seconds: 5),
      );
      return response.statusCode == 200;
    } catch (e) {
      if (kDebugMode) {
        print('[ImageProcessingService] Service availability check failed: $e');
      }
      return false;
    }
  }

  void dispose() {
    _httpClient.close();
    _currentSession = null;
  }
}
