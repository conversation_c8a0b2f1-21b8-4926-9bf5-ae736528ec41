import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class DocumentFile {
  final String path;
  final String displayName;
  final int sizeBytes;
  final DateTime modified;
  final bool isExternal; // True if file is from external app/directory
  final List<int>? externalBytes; // Store file bytes for external files

  const DocumentFile({
    required this.path,
    required this.displayName,
    required this.sizeBytes,
    required this.modified,
    this.isExternal = false,
    this.externalBytes,
  });
}

class DocumentScanner {
  bool _initialized = false;

  List<DocumentFile> pdfs = [];
  List<DocumentFile> words = [];
  List<DocumentFile> excels = [];
  List<DocumentFile> ppts = [];

  Future<void> initialize() async {
    if (_initialized) return;
    // Request storage permissions on Android; on iOS we will use Files picker on demand
    if (Platform.isAndroid) {
      if (kDebugMode) {
        print('[DocumentScanner] Requesting Android storage permissions...');
      }
      
      // For Android 11+ (API 30+), we need MANAGE_EXTERNAL_STORAGE for full storage access
      final manageExternalStorageStatus = await Permission.manageExternalStorage.status;
      if (kDebugMode) {
        print('[DocumentScanner] MANAGE_EXTERNAL_STORAGE status: $manageExternalStorageStatus');
      }
      
      if (!manageExternalStorageStatus.isGranted) {
        final manageResult = await Permission.manageExternalStorage.request();
        if (kDebugMode) {
          print('[DocumentScanner] MANAGE_EXTERNAL_STORAGE request result: $manageResult');
        }
        
        // If manage external storage is denied, fall back to basic storage permission
        if (!manageResult.isGranted) {
          final basicStorageStatus = await Permission.storage.request();
          if (kDebugMode) {
            print('[DocumentScanner] Basic storage permission result: $basicStorageStatus');
          }
        }
      }
    }
    _initialized = true;
  }

  Future<void> scan() async {
    if (!Platform.isAndroid) {
      // On iOS, files are added manually by the user through the file picker.
      // We clear the lists to ensure no stale data from other platforms/sessions.
      pdfs = [];
      words = [];
      excels = [];
      ppts = [];
      return;
    }

    // Android-specific scanning logic remains the same.
    if (kDebugMode) {
      print('[DocumentScanner] Starting Android scan...');
    }
    final List<DocumentFile> foundPdfs = [];
    final List<DocumentFile> foundWords = [];
    final List<DocumentFile> foundExcels = [];
    final List<DocumentFile> foundPpts = [];

    // Common public folders to scan
    final List<String> roots = <String>[
      '/storage/emulated/0/Download',
      '/storage/emulated/0/Documents',
      '/storage/emulated/0/DCIM',
      '/storage/emulated/0/Pictures',
      '/storage/emulated/0/WhatsApp/Media/WhatsApp Documents',
      '/storage/emulated/0/Android/media',
    ];

    final Set<String> visited = <String>{};
    int scannedFiles = 0;
    const int maxFiles = 20000; // safety cap

    for (final root in roots) {
      await _scanPath(Directory(root), onFile: (entity) async {
        if (scannedFiles++ > maxFiles) return false;
        final path = entity.path;
        final lower = path.toLowerCase();
        final stat = await entity.stat();
        final doc = DocumentFile(
          path: path,
          displayName: p.basename(path),
          sizeBytes: stat.size,
          modified: stat.modified,
          isExternal: false, // Android scanned files are not external
        );
        if (lower.endsWith('.pdf')) {
          foundPdfs.add(doc);
        } else if (lower.endsWith('.doc') || lower.endsWith('.docx')) {
          foundWords.add(doc);
        } else if (lower.endsWith('.xls') || lower.endsWith('.xlsx')) {
          foundExcels.add(doc);
        } else if (lower.endsWith('.ppt') || lower.endsWith('.pptx')) {
          foundPpts.add(doc);
        }
        return true;
      }, visited: visited);
    }

    // Also scan app's own documents directory
    await _scanAppDirectory(foundPdfs, foundWords, foundExcels, foundPpts);

    pdfs = foundPdfs;
    words = foundWords;
    excels = foundExcels;
    ppts = foundPpts;
    if (kDebugMode) {
      print('[DocumentScanner] Android scan complete. Found ${pdfs.length} PDFs, ${words.length} Words, ${excels.length} Excels, ${ppts.length} PPTs.');
    }
  }

  void clearAll() {
    final oldCounts = 'PDFs: ${pdfs.length}, Words: ${words.length}, Excels: ${excels.length}, PPTs: ${ppts.length}';
    pdfs = [];
    words = [];
    excels = [];
    ppts = [];
    if (kDebugMode) {
      print('[DocumentScanner] Cleared all document lists. Previous counts - $oldCounts');
    }
  }

  // On iOS, we add files directly rather than scanning directories.
  void addFiles(List<DocumentFile> files, {bool replace = false}) {
    if (kDebugMode) {
      print('[DocumentScanner] addFiles called with ${files.length} files, replace: $replace');
    }
    
    if (replace) {
      clearAll();
    }
    
    final oldCounts = 'PDFs: ${pdfs.length}, Words: ${words.length}, Excels: ${excels.length}, PPTs: ${ppts.length}';
    
    for (final file in files) {
      if (kDebugMode) {
        print('[DocumentScanner] Processing file: ${file.path}');
        print('[DocumentScanner] Display name: ${file.displayName}');
        print('[DocumentScanner] Size: ${file.sizeBytes} bytes');
        print('[DocumentScanner] Modified: ${file.modified}');
      }
      
      final lower = file.path.toLowerCase();
      if (lower.endsWith('.pdf')) {
        // Avoid duplicates
        if (!pdfs.any((e) => e.path == file.path)) {
          pdfs.add(file);
          if (kDebugMode) {
            print('[DocumentScanner] Added PDF: ${file.displayName} at ${file.path}');
          }
        } else if (kDebugMode) {
          print('[DocumentScanner] PDF already exists: ${file.displayName}');
        }
      } else if (lower.endsWith('.doc') || lower.endsWith('.docx')) {
        if (!words.any((e) => e.path == file.path)) {
          words.add(file);
          if (kDebugMode) {
            print('[DocumentScanner] Added Word: ${file.displayName} at ${file.path}');
          }
        } else if (kDebugMode) {
          print('[DocumentScanner] Word doc already exists: ${file.displayName}');
        }
      } else if (lower.endsWith('.xls') || lower.endsWith('.xlsx')) {
        if (!excels.any((e) => e.path == file.path)) {
          excels.add(file);
          if (kDebugMode) {
            print('[DocumentScanner] Added Excel: ${file.displayName} at ${file.path}');
          }
        } else if (kDebugMode) {
          print('[DocumentScanner] Excel already exists: ${file.displayName}');
        }
      } else if (lower.endsWith('.ppt') || lower.endsWith('.pptx')) {
        if (!ppts.any((e) => e.path == file.path)) {
          ppts.add(file);
          if (kDebugMode) {
            print('[DocumentScanner] Added PPT: ${file.displayName} at ${file.path}');
          }
        } else if (kDebugMode) {
          print('[DocumentScanner] PPT already exists: ${file.displayName}');
        }
      } else if (kDebugMode) {
        print('[DocumentScanner] Unknown file type ignored: ${file.path}');
      }
    }
    if (kDebugMode) {
      print('[DocumentScanner] Before: $oldCounts');
      print('[DocumentScanner] After adding ${files.length} files: PDFs: ${pdfs.length}, Words: ${words.length}, Excels: ${excels.length}, PPTs: ${ppts.length}');
    }
  }

  // Private helper for Android scanning.
  Future<void> _scanPath(
    Directory dir, {
    required Future<bool> Function(File entity) onFile,
    required Set<String> visited,
  }) async {
    final path = dir.path;
    if (visited.contains(path)) return;
    visited.add(path);
    
    try {
      if (!await dir.exists()) {
        if (kDebugMode) {
          print('[DocumentScanner] Directory does not exist: $path');
        }
        return;
      }
      
      if (kDebugMode) {
        print('[DocumentScanner] Scanning directory: $path');
      }
      
      await for (final entity in dir.list(recursive: true, followLinks: false)) {
        if (entity is File) {
          final ok = await onFile(entity);
          if (!ok) return;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        // Check if it's a permission error and provide helpful logging
        if (e.toString().contains('Permission denied') || 
            e.toString().contains('PathAccessException')) {
          print('[DocumentScanner] Permission denied for directory: $path');
          print('[DocumentScanner] This directory will be skipped. Consider granting storage permissions.');
        } else {
          print('[DocumentScanner] Scan error at $path: $e');
        }
      }
    }
  }

  // Scan app's own documents directory (both internal and external storage)
  Future<void> _scanAppDirectory(
    List<DocumentFile> foundPdfs,
    List<DocumentFile> foundWords,
    List<DocumentFile> foundExcels,
    List<DocumentFile> foundPpts,
  ) async {
    // Scan internal app directory
    await _scanSingleDirectory(
      await getApplicationDocumentsDirectory(),
      'internal app directory',
      foundPdfs,
      foundWords,
      foundExcels,
      foundPpts,
    );

    // Scan external app directory (where LocalImageStorage saves files)
    if (Platform.isAndroid) {
      try {
        final dirs = await getExternalStorageDirectories(type: StorageDirectory.documents);
        if (dirs != null && dirs.isNotEmpty) {
          const String appName = "All PDF Editor";
          final appDir = Directory(p.join(dirs.first.path, appName));
          await _scanSingleDirectory(
            appDir,
            'external app directory',
            foundPdfs,
            foundWords,
            foundExcels,
            foundPpts,
          );
        }
      } catch (e) {
        if (kDebugMode) {
          print('[DocumentScanner] Error scanning external app directory: $e');
        }
      }
    }
  }

  // Helper method to scan a single directory
  Future<void> _scanSingleDirectory(
    Directory dir,
    String dirDescription,
    List<DocumentFile> foundPdfs,
    List<DocumentFile> foundWords,
    List<DocumentFile> foundExcels,
    List<DocumentFile> foundPpts,
  ) async {
    try {
      if (kDebugMode) {
        print('[DocumentScanner] Scanning $dirDescription: ${dir.path}');
      }

      if (!await dir.exists()) {
        if (kDebugMode) {
          print('[DocumentScanner] $dirDescription does not exist');
        }
        return;
      }

      await for (final entity in dir.list(recursive: true, followLinks: false)) {
        if (entity is File) {
          final path = entity.path;
          final lower = path.toLowerCase();
          
          if (lower.endsWith('.pdf') || lower.endsWith('.doc') || lower.endsWith('.docx') ||
              lower.endsWith('.xls') || lower.endsWith('.xlsx') || 
              lower.endsWith('.ppt') || lower.endsWith('.pptx')) {
            
            final stat = await entity.stat();
            final doc = DocumentFile(
              path: path,
              displayName: p.basename(path),
              sizeBytes: stat.size,
              modified: stat.modified,
              isExternal: false,
            );
            
            if (lower.endsWith('.pdf')) {
              foundPdfs.add(doc);
              if (kDebugMode) {
                print('[DocumentScanner] Found PDF in $dirDescription: ${doc.displayName}');
              }
            } else if (lower.endsWith('.doc') || lower.endsWith('.docx')) {
              foundWords.add(doc);
              if (kDebugMode) {
                print('[DocumentScanner] Found Word doc in $dirDescription: ${doc.displayName}');
              }
            } else if (lower.endsWith('.xls') || lower.endsWith('.xlsx')) {
              foundExcels.add(doc);
              if (kDebugMode) {
                print('[DocumentScanner] Found Excel doc in $dirDescription: ${doc.displayName}');
              }
            } else if (lower.endsWith('.ppt') || lower.endsWith('.pptx')) {
              foundPpts.add(doc);
              if (kDebugMode) {
                print('[DocumentScanner] Found PPT doc in $dirDescription: ${doc.displayName}');
              }
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[DocumentScanner] Error scanning $dirDescription: $e');
      }
    }
  }

  
}
