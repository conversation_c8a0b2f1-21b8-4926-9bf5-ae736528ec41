import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import '../../l10n/app_localizations.dart';

/// 网络状态检测服务
class NetworkStatusService {
  static NetworkStatusService? _instance;
  static NetworkStatusService get instance => _instance ??= NetworkStatusService._();
  
  NetworkStatusService._();
  
  final StreamController<bool> _networkStatusController = StreamController<bool>.broadcast();
  Stream<bool> get networkStatusStream => _networkStatusController.stream;
  
  bool _isConnected = false;
  bool get isConnected => _isConnected;
  
  Timer? _checkTimer;
  
  /// 开始网络状态监听
  void startMonitoring() {
    if (_checkTimer != null) {
      return; // 已在监听中
    }
    
    // 立即检查一次
    _checkNetworkStatus();
    
    // 每30秒检查一次网络状态
    _checkTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _checkNetworkStatus();
    });
    
    if (kDebugMode) {
      print('[NetworkStatusService] Started monitoring network status');
    }
  }
  
  /// 停止网络状态监听
  void stopMonitoring() {
    _checkTimer?.cancel();
    _checkTimer = null;
    
    if (kDebugMode) {
      print('[NetworkStatusService] Stopped monitoring network status');
    }
  }
  
  /// 检查网络连接状态
  Future<bool> checkNetworkStatus() async {
    await _checkNetworkStatus();
    return _isConnected;
  }
  
  /// 内部网络状态检查
  Future<void> _checkNetworkStatus() async {
    try {
      final result = await _testNetworkConnection();
      
      if (_isConnected != result) {
        _isConnected = result;
        _networkStatusController.add(_isConnected);
        
        if (kDebugMode) {
          print('[NetworkStatusService] Network status changed: $_isConnected');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[NetworkStatusService] Network status check failed: $e');
      }
      
      if (_isConnected) {
        _isConnected = false;
        _networkStatusController.add(_isConnected);
      }
    }
  }
  
  /// 测试网络连接
  Future<bool> _testNetworkConnection() async {
    try {
      // 使用多个测试目标提高检测准确性
      final testUrls = [
        'google.com',
        'apple.com',
        'cloudflare.com',
      ];
      
      for (final url in testUrls) {
        try {
          final result = await InternetAddress.lookup(url);
          if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
            return true;
          }
        } catch (e) {
          if (kDebugMode) {
            print('[NetworkStatusService] DNS lookup failed for $url: $e');
          }
          continue; // 尝试下一个URL
        }
      }
      
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('[NetworkStatusService] Network connection test failed: $e');
      }
      return false;
    }
  }
  
  /// 获取网络状态描述
  String getNetworkStatusDescription(AppLocalizations l10n) {
    if (_isConnected) {
      return l10n.networkConnected;
    } else {
      return l10n.networkDisconnected;
    }
  }
  
  /// 获取网络故障排除建议
  List<String> getNetworkTroubleshootingTips(AppLocalizations l10n) {
    return l10n.networkTroubleshootingTips.split('\n');
  }
  
  /// 释放资源
  void dispose() {
    stopMonitoring();
    _networkStatusController.close();
  }
}
