import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';

/// 网络权限管理服务

/// 网络权限管理服务
/// 负责检查和申请iOS 18+的网络访问权限
class NetworkPermissionService {
  static NetworkPermissionService? _instance;
  static NetworkPermissionService get instance => _instance ??= NetworkPermissionService._();
  
  NetworkPermissionService._();
  
  bool _networkPermissionChecked = false;
  bool _hasNetworkPermission = false;
  
  /// 检查是否已有网络权限
  bool get hasNetworkPermission => _hasNetworkPermission;
  
  /// 初始化网络权限检查
  Future<bool> initialize() async {
    if (_networkPermissionChecked) {
      return _hasNetworkPermission;
    }
    
    try {
      if (Platform.isIOS) {
        // iOS 18+ 需要检查网络权限
        await _checkIOSNetworkPermission();
      } else {
        // Android 默认有网络权限
        _hasNetworkPermission = true;
      }
      
      _networkPermissionChecked = true;
      
      if (kDebugMode) {
        print('[NetworkPermissionService] Network permission status: $_hasNetworkPermission');
      }
      
      return _hasNetworkPermission;
    } catch (e) {
      if (kDebugMode) {
        print('[NetworkPermissionService] Failed to check network permission: $e');
      }
      return false;
    }
  }
  
  /// 申请网络权限
  Future<bool> requestNetworkPermission() async {
    try {
      if (Platform.isIOS) {
        return await _requestIOSNetworkPermission();
      } else {
        // Android 默认有网络权限
        return true;
      }
    } catch (e) {
      if (kDebugMode) {
        print('[NetworkPermissionService] Failed to request network permission: $e');
      }
      return false;
    }
  }
  
  /// 检查iOS网络权限
  Future<void> _checkIOSNetworkPermission() async {
    try {
      // 在iOS 18+上，我们通过尝试一个简单的网络请求来检查权限
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 5);
      
      try {
        final request = await client.getUrl(Uri.parse('https://www.apple.com'));
        final response = await request.close();
        await response.drain();
        _hasNetworkPermission = true;
        
        if (kDebugMode) {
          print('[NetworkPermissionService] Network test successful');
        }
      } on SocketException catch (e) {
        if (kDebugMode) {
          print('[NetworkPermissionService] Network test failed: $e');
        }
        // 网络连接失败可能是权限问题
        _hasNetworkPermission = false;
      } finally {
        client.close();
      }
    } catch (e) {
      if (kDebugMode) {
        print('[NetworkPermissionService] iOS network permission check failed: $e');
      }
      _hasNetworkPermission = false;
    }
  }
  
  /// 申请iOS网络权限
  Future<bool> _requestIOSNetworkPermission() async {
    try {
      // iOS没有直接的网络权限API，权限在首次网络请求时触发
      // 我们需要确保Info.plist中包含NSNetworkUsageDescription
      // 然后进行网络请求来触发权限申请
      
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 10);
      
      try {
        if (kDebugMode) {
          print('[NetworkPermissionService] Attempting network request to trigger permission...');
        }
        
        final request = await client.getUrl(Uri.parse('https://www.apple.com'));
        final response = await request.close();
        await response.drain();
        
        _hasNetworkPermission = true;
        _networkPermissionChecked = true;
        
        if (kDebugMode) {
          print('[NetworkPermissionService] Network permission granted');
        }
        
        return true;
      } on SocketException catch (e) {
        if (kDebugMode) {
          print('[NetworkPermissionService] Network permission denied or no connection: $e');
        }
        _hasNetworkPermission = false;
        return false;
      } finally {
        client.close();
      }
    } catch (e) {
      if (kDebugMode) {
        print('[NetworkPermissionService] iOS network permission request failed: $e');
      }
      return false;
    }
  }
  
  /// 显示网络权限设置指引
  String getNetworkPermissionGuide(AppLocalizations l10n) {
    if (Platform.isIOS) {
      return l10n.iosNetworkPermissionGuide(appName);
    } else {
      return l10n.checkNetworkConnection;
    }
  }
  
  static const String appName = 'All PDF Editor';
  
  /// 重置权限状态（用于测试）
  void reset() {
    _networkPermissionChecked = false;
    _hasNetworkPermission = false;
  }
}