import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// Service to handle external files that need to be copied to accessible locations
class ExternalFileService {
  static const String _tempDirName = 'external_files';
  
  /// Copy external file bytes to temporary directory and return accessible path
  static Future<String> copyExternalFileToTemp({
    required List<int> fileBytes,
    required String originalFileName,
  }) async {
    try {
      if (kDebugMode) {
        print('[ExternalFileService] Copying external file: $originalFileName');
        print('[ExternalFileService] File size: ${fileBytes.length} bytes');
      }

      // Get temporary directory
      final tempDir = await getTemporaryDirectory();
      final externalFilesDir = Directory(path.join(tempDir.path, _tempDirName));
      
      // Create external files directory if it doesn't exist
      if (!await externalFilesDir.exists()) {
        await externalFilesDir.create(recursive: true);
        if (kDebugMode) {
          print('[ExternalFileService] Created external files directory: ${externalFilesDir.path}');
        }
      }

      // Generate unique filename to avoid conflicts
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(originalFileName);
      final baseName = path.basenameWithoutExtension(originalFileName);
      final uniqueFileName = '${baseName}_$timestamp$extension';
      
      final tempFilePath = path.join(externalFilesDir.path, uniqueFileName);
      final tempFile = File(tempFilePath);

      // Write bytes to temporary file
      await tempFile.writeAsBytes(fileBytes);
      
      if (kDebugMode) {
        print('[ExternalFileService] File copied to: $tempFilePath');
        // Verify the file was written correctly
        final stat = await tempFile.stat();
        print('[ExternalFileService] Temp file size: ${stat.size} bytes');
      }

      return tempFilePath;
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('[ExternalFileService] Error copying external file: $e');
        print('[ExternalFileService] Stack trace: $stackTrace');
      }
      rethrow;
    }
  }

  /// Clean up old temporary files (older than specified duration)
  static Future<void> cleanupOldTempFiles({Duration maxAge = const Duration(hours: 24)}) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final externalFilesDir = Directory(path.join(tempDir.path, _tempDirName));
      
      if (!await externalFilesDir.exists()) {
        return; // Nothing to clean up
      }

      final cutoffTime = DateTime.now().subtract(maxAge);
      int deletedCount = 0;

      if (kDebugMode) {
        print('[ExternalFileService] Starting cleanup of files older than $cutoffTime');
      }

      await for (final entity in externalFilesDir.list()) {
        if (entity is File) {
          try {
            final stat = await entity.stat();
            if (stat.modified.isBefore(cutoffTime)) {
              await entity.delete();
              deletedCount++;
              if (kDebugMode) {
                print('[ExternalFileService] Deleted old temp file: ${entity.path}');
              }
            }
          } catch (e) {
            if (kDebugMode) {
              print('[ExternalFileService] Error deleting file ${entity.path}: $e');
            }
          }
        }
      }

      if (kDebugMode) {
        print('[ExternalFileService] Cleanup completed. Deleted $deletedCount files');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[ExternalFileService] Error during cleanup: $e');
      }
    }
  }

  /// Get the size of all temporary external files
  static Future<int> getTempFilesTotalSize() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final externalFilesDir = Directory(path.join(tempDir.path, _tempDirName));
      
      if (!await externalFilesDir.exists()) {
        return 0;
      }

      int totalSize = 0;
      await for (final entity in externalFilesDir.list()) {
        if (entity is File) {
          try {
            final stat = await entity.stat();
            totalSize += stat.size;
          } catch (e) {
            // Ignore errors for individual files
          }
        }
      }

      return totalSize;
    } catch (e) {
      if (kDebugMode) {
        print('[ExternalFileService] Error calculating temp files size: $e');
      }
      return 0;
    }
  }

  /// Delete specific temporary file
  static Future<void> deleteTempFile(String tempFilePath) async {
    try {
      final file = File(tempFilePath);
      if (await file.exists()) {
        await file.delete();
        if (kDebugMode) {
          print('[ExternalFileService] Deleted temp file: $tempFilePath');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[ExternalFileService] Error deleting temp file $tempFilePath: $e');
      }
    }
  }
}