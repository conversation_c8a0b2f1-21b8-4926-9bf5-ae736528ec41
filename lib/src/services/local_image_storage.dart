import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../l10n/app_localizations.dart';
import '../models/image_item.dart';

class LocalImageStorage {
  static final LocalImageStorage _instance = LocalImageStorage._internal();
  factory LocalImageStorage() => _instance;
  LocalImageStorage._internal();

  Directory? _originalPhotosDir;
  Directory? _aiProcessedDir;
  Directory? _userCroppedDir;
  Directory? _generatedPdfsDir;

  Directory? get generatedPdfsDir => _generatedPdfsDir;

  Future<Directory> _getBaseStorageDirectory() async {
    if (Platform.isAndroid) {
      // Get the app-specific external storage directory for documents.
      // This is the modern, compliant way to store user-visible documents.
      final dirs = await getExternalStorageDirectories(type: StorageDirectory.documents);
      if (dirs != null && dirs.isNotEmpty) {
        // We use the first directory, which is usually the primary external storage.
        const String appName = "All PDF Editor";
        final appDir = Directory(p.join(dirs.first.path, appName));
        if (!await appDir.exists()) {
          await appDir.create(recursive: true);
        }
        return appDir;
      }
    }
    // For iOS or if Android external storage is not available.
    return await getApplicationDocumentsDirectory();
  }

  Future<void> initialize(AppLocalizations localizations) async {
    final baseDir = await _getBaseStorageDirectory();
    
    // Top-level directories
    _originalPhotosDir = Directory(p.join(baseDir.path, 'Original Photos'));
    _generatedPdfsDir = Directory(p.join(baseDir.path, 'Generated PDFs'));
    final processingParentDir = Directory(p.join(baseDir.path, 'Processed Photos'));

    // Nested directories for processing stages
    _aiProcessedDir = Directory(p.join(processingParentDir.path, 'AI Processed'));
    _userCroppedDir = Directory(p.join(processingParentDir.path, 'User Cropped'));

    // Ensure all directories exist
    await _originalPhotosDir!.create(recursive: true);
    await _generatedPdfsDir!.create(recursive: true);
    await _aiProcessedDir!.create(recursive: true); // This will also create the parent
    await _userCroppedDir!.create(recursive: true); // This will also create the parent

    if (kDebugMode) {
      print('[LocalImageStorage] Initialized directories in: ${baseDir.path}');
      print('  Original: ${_originalPhotosDir!.path}');
      print('  AI Processed: ${_aiProcessedDir!.path}');
      print('  User Cropped: ${_userCroppedDir!.path}');
      print('  Generated PDFs: ${_generatedPdfsDir!.path}');
    }
  }

  /// Saves the original photo to the "Original Photos" directory
  Future<File> saveOriginalPhoto(File sourceFile, String imageId) async {
    if (_originalPhotosDir == null) {
      throw StateError('LocalImageStorage not initialized');
    }

    final extension = p.extension(sourceFile.path);
    final fileName = '${imageId}_original$extension';
    final targetPath = p.join(_originalPhotosDir!.path, fileName);
    
    final savedFile = await sourceFile.copy(targetPath);
    
    if (kDebugMode) {
      print('[LocalImageStorage] Saved original photo: $targetPath');
    }
    
    return savedFile;
  }

  /// Downloads a processed image from a URL or data URL and saves it to the AI Processed directory
  Future<File> downloadProcessedImage(String imageUrl, String imageId) async {
    if (_aiProcessedDir == null) {
      throw StateError('LocalImageStorage not initialized');
    }

    try {
      Uint8List bytes;

      if (imageUrl.startsWith('data:')) {
        final commaIndex = imageUrl.indexOf(',');
        if (commaIndex <= 0) throw Exception('Invalid data URL');
        final base64Part = imageUrl.substring(commaIndex + 1);
        bytes = Uint8List.fromList(base64.decode(base64Part));
      } else {
        final response = await http.get(Uri.parse(imageUrl));
        if (response.statusCode != 200) {
          throw Exception('HTTP ${response.statusCode}: Failed to download image');
        }
        bytes = response.bodyBytes;
      }

      final fileName = '${imageId}_processed.jpg';
      final targetPath = p.join(_aiProcessedDir!.path, fileName);
      final file = File(targetPath);
      
      await file.writeAsBytes(bytes);
      
      if (kDebugMode) {
        print('[LocalImageStorage] Downloaded AI processed image: $targetPath');
      }
      
      return file;
    } catch (e) {
      if (kDebugMode) {
        print('[LocalImageStorage] Failed to download image: $e');
      }
      rethrow;
    }
  }

  /// Saves a user-cropped image to the User Cropped directory
  Future<File> saveCroppedImage(Uint8List imageData, String imageId) async {
    if (_userCroppedDir == null) {
      throw StateError('LocalImageStorage not initialized');
    }

    final fileName = '${imageId}_cropped.jpg';
    final targetPath = p.join(_userCroppedDir!.path, fileName);
    final file = File(targetPath);
    
    await file.writeAsBytes(imageData);
    
    if (kDebugMode) {
      print('[LocalImageStorage] Saved user-cropped image: $targetPath');
    }
    
    return file;
  }

  /// Cleans up all files related to a specific image ID
  Future<void> cleanupImageFiles(String imageId) async {
    try {
      final dirs = [_originalPhotosDir, _aiProcessedDir, _userCroppedDir];
      for (final dir in dirs) {
        if (dir != null) {
          final files = await dir.list()
              .where((entity) => entity is File && p.basename(entity.path).startsWith(imageId))
              .cast<File>()
              .toList();
          for (final file in files) {
            if (await file.exists()) {
              await file.delete();
              if (kDebugMode) {
                print('[LocalImageStorage] Deleted file: ${file.path}');
              }
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[LocalImageStorage] Error cleaning up files for $imageId: $e');
      }
    }
  }

  /// Gets information about storage directories
  Map<String, String> getDirectoryInfo() {
    return {
      'original': _originalPhotosDir?.path ?? 'Not initialized',
      'aiProcessed': _aiProcessedDir?.path ?? 'Not initialized',
      'userCropped': _userCroppedDir?.path ?? 'Not initialized',
      'generatedPdfs': _generatedPdfsDir?.path ?? 'Not initialized',
    };
  }

  /// Checks if a file exists
  Future<bool> fileExists(String filePath) async {
    return await File(filePath).exists();
  }

  /// Gets the size of a file in bytes
  Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      }
    } catch (e) {
      if (kDebugMode) {
        print('[LocalImageStorage] Error getting file size: $e');
      }
    }
    return 0;
  }

  /// Cleans up old temporary files
  Future<void> cleanupOldFiles({int maxAgeHours = 24}) async {
    try {
      final now = DateTime.now();
      final maxAge = Duration(hours: maxAgeHours);
      
      for (final dir in [_originalPhotosDir, _aiProcessedDir, _userCroppedDir]) {
        if (dir != null && await dir.exists()) {
          final files = await dir.list().toList();
          for (final entity in files) {
            if (entity is File) {
              final stat = await entity.stat();
              if (now.difference(stat.modified) > maxAge) {
                await entity.delete();
                if (kDebugMode) {
                  print('[LocalImageStorage] Cleaned up old file: ${entity.path}');
                }
              }
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[LocalImageStorage] Error during cleanup: $e');
      }
    }
  }
}
