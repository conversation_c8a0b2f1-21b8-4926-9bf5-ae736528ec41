import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class DocumentSearchManager extends ChangeNotifier {
  InAppWebViewController? _controller;
  
  // Search state
  List<Map<String, dynamic>> _searchResults = [];
  int _currentSearchIndex = -1;
  bool _isSearching = false;
  bool _caseSensitive = false;
  String _lastSearchQuery = '';
  Timer? _searchDebounceTimer;
  
  // Getters
  List<Map<String, dynamic>> get searchResults => _searchResults;
  int get currentSearchIndex => _currentSearchIndex;
  bool get isSearching => _isSearching;
  bool get caseSensitive => _caseSensitive;
  String get lastSearchQuery => _lastSearchQuery;
  bool get hasResults => _searchResults.isNotEmpty;
  int get totalResults => _searchResults.length;
  
  // Setters
  void setController(InAppWebViewController? controller) {
    _controller = controller;
  }
  
  void setCaseSensitive(bool value) {
    if (_caseSensitive != value) {
      _caseSensitive = value;
      notifyListeners();
    }
  }
  
  // Main search functionality
  Future<void> performSearch(String query) async {
    if (query.isEmpty || _controller == null) {
      await clearSearch();
      return;
    }
    
    _isSearching = true;
    _lastSearchQuery = query; // Set this early to show proper UI
    notifyListeners();
    
    try {
      await _performAdvancedSearch(query);
    } catch (e) {
      if (kDebugMode) {
        print('[DocumentSearchManager] Error performing search: $e');
      }
      // Fallback to basic search
      await _controller!.evaluateJavascript(source: '''
        if (window.find) {
          window.find('$query', false, false, true, false, true, false);
        }
      ''');
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }
  
  Future<void> performDebouncedSearch(String query, {Duration delay = const Duration(milliseconds: 500)}) async {
    // Cancel previous timer
    _searchDebounceTimer?.cancel();
    
    // Set up new debounced search
    _searchDebounceTimer = Timer(delay, () async {
      if (query.isEmpty) {
        await clearSearch();
      } else {
        await performSearch(query);
      }
    });
  }
  
  Future<void> _performAdvancedSearch(String query) async {
    if (_controller == null) return;
    
    final escapedQuery = query.replaceAll("'", "\\'").replaceAll('"', '\\"');
    
    // Clear previous search highlights
    await clearSearchHighlights();
    
    // Perform advanced search with highlighting
    final result = await _controller!.evaluateJavascript(source: '''
      (function() {
        try {
          // Advanced search and highlight function
          window.officeSearchEngine = window.officeSearchEngine || {
            searchResults: [],
            currentIndex: -1,
            highlightClassName: 'office-search-highlight',
            currentHighlightClassName: 'office-search-current'
          };
          
          const engine = window.officeSearchEngine;
          const query = "$escapedQuery";
          const caseSensitive = ${_caseSensitive.toString()};
          
          // Clear previous results
          engine.searchResults = [];
          engine.currentIndex = -1;
          
          // Remove existing highlights
          const existingHighlights = document.querySelectorAll('.' + engine.highlightClassName);
          existingHighlights.forEach(highlight => {
            const parent = highlight.parentNode;
            parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
            parent.normalize();
          });
          
          if (query.trim() === '') {
            return JSON.stringify({ matches: 0, results: [] });
          }
          
          // Create styles for highlighting if not exists
          let styleElement = document.getElementById('office-search-styles');
          if (!styleElement) {
            styleElement = document.createElement('style');
            styleElement.id = 'office-search-styles';
            styleElement.textContent = `
              .office-search-highlight {
                background-color: yellow !important;
                color: black !important;
                padding: 1px 0;
              }
              .office-search-current {
                background-color: orange !important;
                color: black !important;
                padding: 1px 0;
                box-shadow: 0 0 3px rgba(255, 165, 0, 0.8);
              }
            `;
            document.head.appendChild(styleElement);
          }
          
          // Search and highlight function
          function searchAndHighlight(node, query, caseSensitive) {
            const results = [];
            const searchText = caseSensitive ? query : query.toLowerCase();
            
            function walkNode(node) {
              if (node.nodeType === Node.TEXT_NODE) {
                const textContent = node.textContent;
                const searchInText = caseSensitive ? textContent : textContent.toLowerCase();
                let startPos = 0;
                let index = -1;
                
                while ((index = searchInText.indexOf(searchText, startPos)) !== -1) {
                  // Create range for this match
                  const range = document.createRange();
                  range.setStart(node, index);
                  range.setEnd(node, index + query.length);
                  
                  // Get position info for scrolling
                  const rect = range.getBoundingClientRect();
                  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                  const absoluteTop = rect.top + scrollTop;
                  
                  results.push({
                    range: range,
                    text: textContent.substring(index, index + query.length),
                    absoluteTop: absoluteTop,
                    element: node.parentElement
                  });
                  
                  startPos = index + 1;
                }
              } else if (node.nodeType === Node.ELEMENT_NODE) {
                // Skip script and style elements
                if (node.tagName && (node.tagName.toLowerCase() === 'script' || node.tagName.toLowerCase() === 'style')) {
                  return;
                }
                for (let child of node.childNodes) {
                  walkNode(child);
                }
              }
            }
            
            walkNode(node);
            return results;
          }
          
          // Perform the search
          const matches = searchAndHighlight(document.body, query, caseSensitive);
          
          // Create highlights for all matches
          matches.forEach((match, index) => {
            try {
              const span = document.createElement('span');
              span.className = engine.highlightClassName;
              span.textContent = match.text;
              span.setAttribute('data-search-index', index.toString());
              
              // Replace the range with highlighted span
              match.range.deleteContents();
              match.range.insertNode(span);
            } catch (e) {
              console.warn('Failed to highlight match:', e);
            }
          });
          
          engine.searchResults = matches;
          
          return JSON.stringify({
            matches: matches.length,
            results: matches.map((match, index) => ({
              index: index,
              text: match.text,
              top: match.absoluteTop
            }))
          });
          
        } catch (error) {
          console.error('Search error:', error);
          return JSON.stringify({ error: error.message, matches: 0, results: [] });
        }
      })();
    ''');
    
    if (kDebugMode) {
      print('[DocumentSearchManager] Advanced search result: $result');
    }
    
    // Parse the result
    if (result != null) {
      try {
        // Simple JSON parsing since we control the format
        final resultStr = result.toString();
        if (resultStr.contains('"matches":')) {
          // Extract results array
          final resultsRegex = RegExp(r'"results":\[(.*?)\]');
          final resultsMatch = resultsRegex.firstMatch(resultStr);
          final resultsData = resultsMatch?.group(1) ?? '';
          
          // Parse individual results
          final List<Map<String, dynamic>> searchResults = [];
          if (resultsData.isNotEmpty) {
            // Split by objects and parse each one
            final objectRegex = RegExp(r'\{[^}]+\}');
            final objectMatches = objectRegex.allMatches(resultsData);
            
            for (final objectMatch in objectMatches) {
              final objectStr = objectMatch.group(0)!;
              final indexMatch = RegExp(r'"index":(\d+)').firstMatch(objectStr);
              final topMatch = RegExp(r'"top":([\d.]+)').firstMatch(objectStr);
              
              if (indexMatch != null && topMatch != null) {
                searchResults.add({
                  'index': int.parse(indexMatch.group(1)!),
                  'top': double.parse(topMatch.group(1)!),
                });
              }
            }
          }
          
          _searchResults = searchResults;
          _currentSearchIndex = searchResults.isNotEmpty ? 0 : -1;
          notifyListeners();
          
          // Navigate to first match if found
          if (searchResults.isNotEmpty) {
            await navigateToSearchMatch(0);
          }
          
          if (kDebugMode) {
            print('[DocumentSearchManager] Found ${_searchResults.length} matches, navigated to first');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('[DocumentSearchManager] Error parsing search results: $e');
        }
      }
    }
  }
  
  // Navigation functions
  Future<void> navigateToNext() async {
    if (_searchResults.isEmpty) return;
    
    final nextIndex = (_currentSearchIndex + 1) % _searchResults.length;
    await navigateToSearchMatch(nextIndex);
  }
  
  Future<void> navigateToPrevious() async {
    if (_searchResults.isEmpty) return;
    
    final prevIndex = _currentSearchIndex <= 0 ? _searchResults.length - 1 : _currentSearchIndex - 1;
    await navigateToSearchMatch(prevIndex);
  }
  
  Future<void> navigateToSearchMatch(int index) async {
    if (_controller == null || _searchResults.isEmpty || index < 0 || index >= _searchResults.length) {
      return;
    }
    
    _currentSearchIndex = index;
    notifyListeners();
    
    await _controller!.evaluateJavascript(source: '''
      (function() {
        try {
          const engine = window.officeSearchEngine;
          if (!engine || !engine.searchResults || engine.searchResults.length === 0) {
            return;
          }
          
          // Remove current highlight from all matches
          const allHighlights = document.querySelectorAll('.' + engine.highlightClassName);
          allHighlights.forEach(highlight => {
            highlight.classList.remove(engine.currentHighlightClassName);
          });
          
          // Add current highlight to the specific match
          const targetHighlight = document.querySelector('[data-search-index="$index"]');
          if (targetHighlight) {
            targetHighlight.classList.add(engine.currentHighlightClassName);
            
            // Scroll to the element smoothly
            targetHighlight.scrollIntoView({ 
              behavior: 'smooth', 
              block: 'center',
              inline: 'nearest'
            });
          }
          
          engine.currentIndex = $index;
        } catch (error) {
          console.error('Navigation error:', error);
        }
      })();
    ''');
    
    if (kDebugMode) {
      print('[DocumentSearchManager] Navigated to search match $index of ${_searchResults.length}');
    }
  }
  
  // Clear functions
  Future<void> clearSearchHighlights() async {
    if (_controller == null) return;
    
    await _controller!.evaluateJavascript(source: '''
      (function() {
        try {
          // Remove all search highlights
          const highlights = document.querySelectorAll('.office-search-highlight, .office-search-current');
          highlights.forEach(highlight => {
            const parent = highlight.parentNode;
            if (parent) {
              parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
              parent.normalize();
            }
          });
          
          // Clear engine state
          if (window.officeSearchEngine) {
            window.officeSearchEngine.searchResults = [];
            window.officeSearchEngine.currentIndex = -1;
          }
        } catch (error) {
          console.error('Clear highlights error:', error);
        }
      })();
    ''');
    
    _searchResults.clear();
    _currentSearchIndex = -1;
    // Don't clear _lastSearchQuery here to preserve "not found" message
    notifyListeners();
  }

  Future<void> clearSearch() async {
    await clearSearchHighlights();
    
    // Also clear legacy selection for backward compatibility
    if (_controller != null) {
      await _controller!.evaluateJavascript(source: '''
        if (window.getSelection) {
          window.getSelection().removeAllRanges();
        }
      ''');
    }
  }
  
  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    super.dispose();
  }
}