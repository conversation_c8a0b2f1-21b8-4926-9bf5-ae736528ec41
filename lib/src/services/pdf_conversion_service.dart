import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'dart:math' as math;

import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

import 'generated_pdfs_cache_service.dart';

/// Service to handle converting Office documents to PDF using WebView
class PdfConversionService {
  
  /// Convert the current WebView content to PDF with caching support
  static Future<String?> convertToPdf({
    required InAppWebViewController controller,
    required String originalFileName,
    required String documentType, // 'word', 'excel', 'ppt'
    String? sourceFilePath, // Path to the original source file for caching
  }) async {
    try {
      if (kDebugMode) {
        print('[PdfConversionService] Starting PDF conversion for: $originalFileName');
        print('[PdfConversionService] Document type: $documentType');
        print('[PdfConversionService] Source file path: $sourceFilePath');
      }

      // Check cache first if source file path is provided
      if (sourceFilePath != null) {
        final cachedPdfPath = await GeneratedPdfsCacheService.getCachedPdfPath(
          sourceFilePath: sourceFilePath,
          sourceFileName: originalFileName,
        );
        
        if (cachedPdfPath != null) {
          if (kDebugMode) {
            print('[PdfConversionService] Using cached PDF: $cachedPdfPath');
          }
          return cachedPdfPath;
        }
        
        if (kDebugMode) {
          print('[PdfConversionService] No valid cache found, generating new PDF...');
        }
      }
      

      // Apply CSS for different document types before conversion
      await _applyPrintStyles(controller, documentType);
      
      if (kDebugMode) {
        print('[PdfConversionService] Print styles applied, waiting for styles to take effect...');
      }
      
      // Wait a moment for styles to apply
      await Future.delayed(const Duration(milliseconds: 500));
      
      if (kDebugMode) {
        print('[PdfConversionService] Generating PDF from WebView content using hybrid approach...');
      }
      
      // Try createPdf first with timeout protection, fallback to screenshot if it fails
      final pdfBytes = await _generatePdfWithFallback(controller, documentType);
      
      if (pdfBytes == null) {
        if (kDebugMode) {
          print('[PdfConversionService] All PDF generation methods failed');
        }
        return null;
      }
      
      if (kDebugMode) {
        print('[PdfConversionService] PDF generated successfully: ${pdfBytes.length} bytes');
      }
      
      // Cache the PDF if source file path is provided
      if (sourceFilePath != null) {
        final cachedPdfPath = await GeneratedPdfsCacheService.cachePdf(
          sourceFilePath: sourceFilePath,
          sourceFileName: originalFileName,
          pdfBytes: pdfBytes,
        );
        
        if (cachedPdfPath != null) {
          if (kDebugMode) {
            print('[PdfConversionService] PDF cached successfully: $cachedPdfPath');
          }
          return cachedPdfPath;
        } else {
          if (kDebugMode) {
            print('[PdfConversionService] Failed to cache PDF, falling back to temporary save');
          }
        }
      }
      
      // Fallback: save to app documents directory (legacy behavior)
      final appDocDir = await getApplicationDocumentsDirectory();
      final baseName = path.basenameWithoutExtension(originalFileName);
      final pdfFileName = '$baseName.pdf';
      final pdfFilePath = path.join(appDocDir.path, pdfFileName);
      
      final pdfFile = File(pdfFilePath);
      await pdfFile.writeAsBytes(pdfBytes);
      
      if (kDebugMode) {
        print('[PdfConversionService] PDF saved to fallback location: $pdfFilePath');
      }

      return pdfFilePath;
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('[PdfConversionService] Error converting to PDF: $e');
        print('[PdfConversionService] Stack trace: $stackTrace');
      }
      return null;
    }
  }

  /// Apply CSS print styles based on document type
  static Future<void> _applyPrintStyles(InAppWebViewController controller, String documentType) async {
    String cssStyles;
    
    switch (documentType.toLowerCase()) {
      case 'word':
      case 'excel':
        // A4 paper size for Word/Excel: 210mm × 297mm
        cssStyles = '''
          @media print {
            @page {
              size: A4;
              margin: 10mm;
            }
            body {
              font-size: 12pt;
              line-height: 1.2;
              margin: 0;
              padding: 0;
              width: 100%;
              height: 100%;
            }
            * {
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
          }
        ''';
        break;
      case 'ppt':
        // For PowerPoint, use custom dimensions and fit slides to page
        cssStyles = '''
          @media print {
            @page {
              size: 297mm 210mm; /* A4 landscape */
              margin: 0;
            }
            body {
              width: 100%;
              height: 100%;
              margin: 0 !important;
              padding: 0 !important;
              overflow: hidden;
              transform-origin: top left;
            }
            html, body {
              background: white !important;
            }
            * {
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            /* Ensure slides fill the page */
            .slide, [class*="slide"], [id*="slide"] {
              width: 100% !important;
              height: 100% !important;
              margin: 0 !important;
              padding: 0 !important;
            }
          }
        ''';
        break;
      default:
        cssStyles = '''
          @media print {
            @page {
              size: A4;
              margin: 10mm;
            }
            body {
              margin: 0;
              padding: 0;
            }
            * {
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
          }
        ''';
    }

    if (kDebugMode) {
      print('[PdfConversionService] Applying CSS styles for document type: $documentType');
    }

    // Inject CSS styles
    await controller.evaluateJavascript(source: '''
      // Remove any existing print styles first
      const existingStyles = document.querySelectorAll('style[data-pdf-conversion]');
      existingStyles.forEach(style => style.remove());
      
      // Add new print styles
      const style = document.createElement('style');
      style.type = 'text/css';
      style.setAttribute('data-pdf-conversion', 'true');
      style.innerHTML = `$cssStyles`;
      document.head.appendChild(style);
      
      console.log('PDF conversion styles applied');
    ''');

    if (kDebugMode) {
      print('[PdfConversionService] Applied print styles for $documentType');
    }
  }

  /// Generate PDF with fallback: try createPdf first, then takeScreenshot if it fails
  static Future<Uint8List?> _generatePdfWithFallback(InAppWebViewController controller, String documentType) async {
    try {
      if (kDebugMode) {
        print('[PdfConversionService] Starting hybrid PDF generation for document type: $documentType');
      }

      // On iOS, calling createPdf can deadlock the Flutter engine in some cases
      // (especially with file:// URLs). To avoid app freezes, skip createPdf on iOS
      // and use the screenshot fallback directly.
      if (!Platform.isIOS) {
        // Method 1: Try createPdf with strict timeout (3 seconds)
        if (kDebugMode) {
          print('[PdfConversionService] Method 1: Attempting createPdf with timeout...');
        }
        final createPdfResult = await _tryCreatePdfWithTimeout(controller);
        if (createPdfResult != null) {
          if (kDebugMode) {
            print('[PdfConversionService] Method 1 SUCCESS: createPdf returned ${createPdfResult.length} bytes');
          }
          return createPdfResult;
        }
        if (kDebugMode) {
          print('[PdfConversionService] Method 1 FAILED: createPdf timed out or failed');
          print('[PdfConversionService] Method 2: Attempting scroll-and-stitch fallback...');
        }
      } else {
        if (kDebugMode) {
          print('[PdfConversionService] iOS detected: skipping createPdf to avoid deadlock; using screenshot fallback');
        }
      }
      // Method 2: Try scroll-and-stitch multipage approach to capture full content
      final stitchedResult = await _generatePdfByScrolling(controller, documentType);
      if (stitchedResult != null) {
        if (kDebugMode) {
          print('[PdfConversionService] Method 2 SUCCESS: scroll-and-stitch produced ${stitchedResult.length} bytes');
        }
        return stitchedResult;
      }

      if (kDebugMode) {
        print('[PdfConversionService] Method 2 FAILED: scroll-and-stitch approach failed');
        print('[PdfConversionService] Method 3: Attempting single screenshot fallback...');
      }

      // Method 3: Fallback to single-screenshot approach (at least current viewport)
      final screenshotResult = await _generatePdfFromScreenshot(controller, documentType);
      if (screenshotResult != null) {
        if (kDebugMode) {
          print('[PdfConversionService] Method 3 SUCCESS: screenshot converted to PDF, ${screenshotResult.length} bytes');
        }
        return screenshotResult;
      }

      if (kDebugMode) {
        print('[PdfConversionService] Method 3 FAILED: screenshot approach also failed');
        print('[PdfConversionService] All methods exhausted - PDF generation failed');
      }

      return null;
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('[PdfConversionService] === HYBRID PDF GENERATION ERROR ===');
        print('[PdfConversionService] Error in _generatePdfWithFallback: $e');
        print('[PdfConversionService] Stack trace: $stackTrace');
        print('[PdfConversionService] === END HYBRID ERROR ===');
      }
      return null;
    }
  }

  /// Generate a multipage PDF by scrolling and capturing the WebView viewport repeatedly
  static Future<Uint8List?> _generatePdfByScrolling(
    InAppWebViewController controller,
    String documentType,
  ) async {
    try {
      if (kDebugMode) {
        print('[PdfConversionService] Starting scroll-and-stitch capture...');
      }

      // Get scroll metrics via JS
      final jsResult = await controller.evaluateJavascript(source: '''
        (function() {
          const d = document.documentElement;
          const b = document.body || {};
          const scrollHeight = Math.max(d.scrollHeight || 0, b.scrollHeight || 0, 0);
          const clientHeight = window.innerHeight || d.clientHeight || 0;
          const clientWidth = window.innerWidth || d.clientWidth || 0;
          const scrollY = window.scrollY || window.pageYOffset || 0;
          const deviceScale = window.devicePixelRatio || 1;
          return JSON.stringify({ scrollHeight, clientHeight, clientWidth, scrollY, deviceScale });
        })();
      ''');

      if (jsResult == null) {
        if (kDebugMode) {
          print('[PdfConversionService] Failed to get scroll metrics (null JS result)');
        }
        return null;
      }

      late Map<String, dynamic> metrics;
      try {
        metrics = jsonDecode(jsResult as String) as Map<String, dynamic>;
      } catch (e) {
        if (kDebugMode) {
          print('[PdfConversionService] Failed to decode scroll metrics: $e');
        }
        return null;
      }

      final totalHeight = (metrics['scrollHeight'] as num?)?.toDouble() ?? 0.0;
      final viewportHeight = (metrics['clientHeight'] as num?)?.toDouble() ?? 0.0;
      final originalScrollY = (metrics['scrollY'] as num?)?.toDouble() ?? 0.0;
      final clientWidth = (metrics['clientWidth'] as num?)?.toDouble() ?? 0.0;

      if (totalHeight <= 0 || viewportHeight <= 0) {
        if (kDebugMode) {
          print('[PdfConversionService] Invalid scroll metrics: totalHeight=$totalHeight, viewportHeight=$viewportHeight');
        }
        return null;
      }

      // Determine how many pages to capture
      final rawPageCount = (totalHeight / viewportHeight).ceil();
      final pageCount = math.max(1, math.min(rawPageCount, 200)); // safety upper bound

      if (kDebugMode) {
        print('[PdfConversionService] totalHeight=$totalHeight, viewportHeight=$viewportHeight, width=$clientWidth');
        print('[PdfConversionService] Estimated pages to capture: $pageCount');
      }

      // Prepare capture Y positions
      final positions = <double>[];
      for (var i = 0; i < pageCount; i++) {
        double y = i * viewportHeight;
        // Ensure last position aligns bottom exactly
        if (i == pageCount - 1) {
          y = math.max(0.0, totalHeight - viewportHeight);
        }
        if (positions.isEmpty || (y - positions.last).abs() > 1.0) {
          positions.add(y);
        }
      }

      if (kDebugMode) {
        print('[PdfConversionService] Capture positions count: ${positions.length}');
      }

      // Capture screenshots for each position
      final images = <Uint8List>[];
      for (var idx = 0; idx < positions.length; idx++) {
        final y = positions[idx];
        await controller.evaluateJavascript(source: 'window.scrollTo(0, ${y.toStringAsFixed(0)});');
        // small delay to let rendering settle
        await Future.delayed(const Duration(milliseconds: 180));
        final shot = await controller.takeScreenshot();
        if (shot == null) {
          if (kDebugMode) {
            print('[PdfConversionService] Screenshot at index $idx returned null');
          }
          continue;
        }
        images.add(shot);
        if (kDebugMode) {
          print('[PdfConversionService] Captured page ${idx + 1}/${positions.length}, bytes=${shot.length}');
        }
      }

      // Restore original scroll position
      await controller.evaluateJavascript(source: 'window.scrollTo(0, ${originalScrollY.toStringAsFixed(0)});');

      if (images.isEmpty) {
        if (kDebugMode) {
          print('[PdfConversionService] No screenshots captured during scroll');
        }
        return null;
      }

      // Build multipage PDF
      final pdf = pw.Document();

      if (documentType.toLowerCase() == 'ppt') {
        // For PPT: one slide per page, page size matches slide aspect ratio, fill page without borders
        // Use viewport aspect ratio as slide ratio proxy
        final slideAspect = (viewportHeight > 0 && clientWidth > 0)
            ? (clientWidth / viewportHeight)
            : (16 / 9);
        // Choose a reasonable PDF width in points, compute height by aspect
        const baseWidthPts = 842.0; // similar to A4 width for good quality
        final baseHeightPts = baseWidthPts / slideAspect;
        final slidePageFormat = PdfPageFormat(baseWidthPts, baseHeightPts);

        for (final img in images) {
          pdf.addPage(
            pw.Page(
              pageFormat: slidePageFormat,
              margin: pw.EdgeInsets.zero,
              build: (context) => pw.Image(
                pw.MemoryImage(img),
                fit: pw.BoxFit.contain, // page ratio matches image; no borders, no distortion
                width: slidePageFormat.width,
                height: slidePageFormat.height,
              ),
            ),
          );
        }
      } else {
        // Word/Excel: standard A4 (portrait), contain within page
        final pageFormat = PdfPageFormat.a4;
        for (final img in images) {
          pdf.addPage(
            pw.Page(
              pageFormat: pageFormat,
              margin: pw.EdgeInsets.zero,
              build: (context) => pw.Center(
                child: pw.Image(
                  pw.MemoryImage(img),
                  fit: pw.BoxFit.contain,
                  width: pageFormat.width,
                  height: pageFormat.height,
                ),
              ),
            ),
          );
        }
      }

      final bytes = await pdf.save();
      if (kDebugMode) {
        print('[PdfConversionService] Scroll-and-stitch PDF bytes: ${bytes.length}');
      }
      return bytes;
    } catch (e, st) {
      if (kDebugMode) {
        print('[PdfConversionService] Error in scroll-and-stitch: $e');
        print('[PdfConversionService] Stack trace: $st');
      }
      return null;
    }
  }

  /// Try createPdf with strict timeout to avoid deadlocks
  static Future<Uint8List?> _tryCreatePdfWithTimeout(InAppWebViewController controller) async {
    try {
      final completer = Completer<Uint8List?>();
      bool isCompleted = false;

      // Start createPdf call
      controller.createPdf().then((pdfBytes) {
        if (!isCompleted && !completer.isCompleted) {
          completer.complete(pdfBytes);
        }
      }).catchError((error) {
        if (kDebugMode) {
          print('[PdfConversionService] createPdf error: $error');
        }
        if (!isCompleted && !completer.isCompleted) {
          completer.complete(null);
        }
      });

      // Strict 3-second timeout
      Timer(const Duration(seconds: 3), () {
        if (!isCompleted) {
          if (kDebugMode) {
            print('[PdfConversionService] createPdf timeout after 3 seconds');
          }
          if (!completer.isCompleted) {
            completer.complete(null);
          }
        }
      });

      final result = await completer.future;
      isCompleted = true;
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('[PdfConversionService] createPdf attempt failed: $e');
      }
      return null;
    }
  }

  /// Generate PDF from screenshot as fallback method
  static Future<Uint8List?> _generatePdfFromScreenshot(InAppWebViewController controller, String documentType) async {
    try {
      if (kDebugMode) {
        print('[PdfConversionService] Taking screenshot of WebView...');
      }

      // Take screenshot
      final screenshot = await controller.takeScreenshot();
      if (screenshot == null) {
        if (kDebugMode) {
          print('[PdfConversionService] takeScreenshot returned null');
        }
        return null;
      }

      if (kDebugMode) {
        print('[PdfConversionService] Screenshot taken: ${screenshot.length} bytes');
        print('[PdfConversionService] Converting screenshot to PDF...');
      }

      // Convert screenshot to PDF using pdf package
      return await _convertScreenshotToPdf(screenshot, documentType);
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('[PdfConversionService] Error in screenshot fallback: $e');
        print('[PdfConversionService] Stack trace: $stackTrace');
      }
      return null;
    }
  }

  /// Convert screenshot image to PDF
  static Future<Uint8List?> _convertScreenshotToPdf(Uint8List imageBytes, String documentType) async {
    try {
      if (kDebugMode) {
        print('[PdfConversionService] Creating PDF from screenshot...');
      }

      // Create PDF document
      final pdf = pw.Document();

      // Determine page format based on document type
      PdfPageFormat pageFormat;
      switch (documentType.toLowerCase()) {
        case 'ppt':
          // PPT uses landscape A4
          pageFormat = PdfPageFormat.a4.landscape;
          break;
        case 'word':
        case 'excel':
        default:
          // Word/Excel use portrait A4
          pageFormat = PdfPageFormat.a4;
          break;
      }

      if (kDebugMode) {
        print('[PdfConversionService] Using page format: ${pageFormat.width} x ${pageFormat.height}');
      }

      // Create PDF page with the screenshot
      pdf.addPage(
        pw.Page(
          pageFormat: pageFormat,
          margin: pw.EdgeInsets.zero, // No margins for screenshot
          build: (pw.Context context) {
            return pw.Center(
              child: pw.Image(
                pw.MemoryImage(imageBytes),
                fit: pw.BoxFit.contain, // Fit the image within the page
                width: pageFormat.width,
                height: pageFormat.height,
              ),
            );
          },
        ),
      );

      // Generate PDF bytes
      final pdfBytes = await pdf.save();

      if (kDebugMode) {
        print('[PdfConversionService] Screenshot converted to PDF: ${pdfBytes.length} bytes');
      }

      return pdfBytes;
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('[PdfConversionService] Error converting screenshot to PDF: $e');
        print('[PdfConversionService] Stack trace: $stackTrace');
      }
      return null;
    }
  }


  /// Get the appropriate document type based on file extension
  static String getDocumentType(String fileName) {
    final extension = path.extension(fileName).toLowerCase();
    
    if (extension == '.doc' || extension == '.docx') {
      return 'word';
    } else if (extension == '.xls' || extension == '.xlsx') {
      return 'excel';
    } else if (extension == '.ppt' || extension == '.pptx') {
      return 'ppt';
    }
    
    return 'unknown';
  }
}
