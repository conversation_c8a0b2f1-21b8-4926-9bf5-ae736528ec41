import 'dart:async';
import 'dart:math' as math;
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';

import '../models/image_item.dart';

/// 透视变换工具类
/// 实现四点透视矫正，将任意四边形区域矫正为矩形
class PerspectiveTransform {
  
  /// 计算透视变换矩阵
  /// 将源四边形变换为目标矩形
  static List<double> calculatePerspectiveMatrix(
    List<Point> srcPoints,
    double dstWidth,
    double dstHeight,
  ) {
    // 目标矩形的四个角点（左上、右上、右下、左下）
    final dst = [
      const Point(0, 0),
      Point(dstWidth, 0),
      Point(dstWidth, dstHeight),
      Point(0, dstHeight)
    ];

    // 计算透视变换矩阵 (3x3)
    // 使用齐次坐标系和线性方程组求解
    final List<List<double>> A = [];
    final List<double> B = [];

    for (int i = 0; i < 4; i++) {
      final sx = srcPoints[i].x;
      final sy = srcPoints[i].y;
      final dx = dst[i].x;
      final dy = dst[i].y;

      // 对于每个点，我们有两个方程：
      // dx = (a*sx + b*sy + c) / (g*sx + h*sy + 1)
      // dy = (d*sx + e*sy + f) / (g*sx + h*sy + 1)
      
      // 重新排列得到线性方程
      A.add([sx, sy, 1, 0, 0, 0, -dx * sx, -dx * sy]);
      B.add(dx);
      A.add([0, 0, 0, sx, sy, 1, -dy * sx, -dy * sy]);
      B.add(dy);
    }

    // 使用高斯消元法求解线性方程组
    final solution = _solveLinearSystem(A, B);
    
    // 返回3x3变换矩阵的扁平化数组
    // [a, b, c, d, e, f, g, h, 1]
    return [...solution, 1];
  }

  /// 应用透视变换到图像
  static Future<Uint8List> applyPerspectiveTransform(
    Uint8List sourceImageBytes,
    List<Point> srcPoints,
    int outputWidth,
    int outputHeight,
  ) async {
    // 解码源图像
    final ui.Codec codec = await ui.instantiateImageCodec(sourceImageBytes);
    final ui.FrameInfo frameInfo = await codec.getNextFrame();
    final ui.Image sourceImage = frameInfo.image;

    // 创建画布进行变换
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final ui.Canvas canvas = ui.Canvas(recorder);
    
    // 设置输出尺寸
    canvas.clipRect(ui.Rect.fromLTWH(0, 0, outputWidth.toDouble(), outputHeight.toDouble()));

    // 使用双线性插值进行透视变换
    final transformedData = await _transformWithInterpolation(
      sourceImage,
      srcPoints,
      outputWidth,
      outputHeight,
    );

    // 结束记录并获取图像
    final ui.Picture picture = recorder.endRecording();
    final ui.Image outputImage = await picture.toImage(outputWidth, outputHeight);
    
    // 使用变换后的数据创建图像
    final ui.ImmutableBuffer buffer = await ui.ImmutableBuffer.fromUint8List(transformedData);
    final ui.ImageDescriptor descriptor = ui.ImageDescriptor.raw(
      buffer,
      width: outputWidth,
      height: outputHeight,
      pixelFormat: ui.PixelFormat.rgba8888,
    );
    final ui.Codec outputCodec = await descriptor.instantiateCodec();
    final ui.FrameInfo outputFrame = await outputCodec.getNextFrame();
    final ui.Image finalImage = outputFrame.image;

    // 转换为PNG字节数据
    final ByteData? byteData = await finalImage.toByteData(format: ui.ImageByteFormat.png);
    
    // 清理资源
    sourceImage.dispose();
    outputImage.dispose();
    finalImage.dispose();
    
    return byteData!.buffer.asUint8List();
  }

  /// 简化的透视变换实现
  /// 使用双线性映射近似透视变换
  static Point _transformPoint(double x, double y, List<Point> srcPoints, double dstWidth, double dstHeight) {
    // 将输出坐标归一化到[0,1]范围
    final u = x / dstWidth;
    final v = y / dstHeight;

    // 使用双线性插值计算源坐标
    // 四个角点的双线性插值
    final topLeft = srcPoints[0];     // 左上
    final topRight = srcPoints[1];    // 右上
    final bottomRight = srcPoints[2]; // 右下
    final bottomLeft = srcPoints[3];  // 左下

    // 先在水平方向插值
    final top = Point(
      topLeft.x * (1 - u) + topRight.x * u,
      topLeft.y * (1 - u) + topRight.y * u,
    );
    
    final bottom = Point(
      bottomLeft.x * (1 - u) + bottomRight.x * u,
      bottomLeft.y * (1 - u) + bottomRight.y * u,
    );

    // 再在垂直方向插值
    return Point(
      top.x * (1 - v) + bottom.x * v,
      top.y * (1 - v) + bottom.y * v,
    );
  }

  /// 使用双线性插值进行图像变换
  static Future<Uint8List> _transformWithInterpolation(
    ui.Image sourceImage,
    List<Point> srcPoints,
    int outputWidth,
    int outputHeight,
  ) async {
    // 获取源图像像素数据
    final ByteData? sourceData = await sourceImage.toByteData(format: ui.ImageByteFormat.rawRgba);
    if (sourceData == null) {
      throw Exception('Failed to get source image data');
    }

    final sourceWidth = sourceImage.width;
    final sourceHeight = sourceImage.height;
    final sourceBytes = sourceData.buffer.asUint8List();
    
    // 创建输出图像数据
    final outputData = Uint8List(outputWidth * outputHeight * 4);

    // 对每个输出像素进行变换
    for (int y = 0; y < outputHeight; y++) {
      for (int x = 0; x < outputWidth; x++) {
        // 将输出坐标映射到源图像坐标
        final srcCoord = _transformPoint(x.toDouble(), y.toDouble(), srcPoints, outputWidth.toDouble(), outputHeight.toDouble());
        
        if (srcCoord.x >= 0 && srcCoord.x < sourceWidth && 
            srcCoord.y >= 0 && srcCoord.y < sourceHeight) {
          
          // 使用双线性插值获取像素值
          final color = _bilinearInterpolation(sourceBytes, srcCoord.x, srcCoord.y, sourceWidth, sourceHeight);
          
          final outputIndex = (y * outputWidth + x) * 4;
          outputData[outputIndex] = color[0];     // R
          outputData[outputIndex + 1] = color[1]; // G
          outputData[outputIndex + 2] = color[2]; // B
          outputData[outputIndex + 3] = color[3]; // A
        } else {
          // 超出边界的像素设为白色
          final outputIndex = (y * outputWidth + x) * 4;
          outputData[outputIndex] = 255;     // R
          outputData[outputIndex + 1] = 255; // G
          outputData[outputIndex + 2] = 255; // B
          outputData[outputIndex + 3] = 255; // A
        }
      }
    }

    return outputData;
  }

  /// 双线性插值获取像素颜色
  static List<int> _bilinearInterpolation(
    Uint8List imageData, 
    double x, 
    double y, 
    int width, 
    int height
  ) {
    final x1 = x.floor();
    final y1 = y.floor();
    final x2 = math.min(x1 + 1, width - 1);
    final y2 = math.min(y1 + 1, height - 1);

    final dx = x - x1;
    final dy = y - y1;

    // 获取四个邻近像素
    List<int> getPixel(int px, int py) {
      final index = (py * width + px) * 4;
      return [
        imageData[index],     // R
        imageData[index + 1], // G
        imageData[index + 2], // B
        imageData[index + 3], // A
      ];
    }

    final c1 = getPixel(x1, y1); // 左上
    final c2 = getPixel(x2, y1); // 右上
    final c3 = getPixel(x1, y2); // 左下
    final c4 = getPixel(x2, y2); // 右下

    // 双线性插值
    double interpolate(double a, double b, double c, double d) {
      return a * (1 - dx) * (1 - dy) + 
             b * dx * (1 - dy) + 
             c * (1 - dx) * dy + 
             d * dx * dy;
    }

    return [
      interpolate(c1[0].toDouble(), c2[0].toDouble(), c3[0].toDouble(), c4[0].toDouble()).round(),
      interpolate(c1[1].toDouble(), c2[1].toDouble(), c3[1].toDouble(), c4[1].toDouble()).round(),
      interpolate(c1[2].toDouble(), c2[2].toDouble(), c3[2].toDouble(), c4[2].toDouble()).round(),
      interpolate(c1[3].toDouble(), c2[3].toDouble(), c3[3].toDouble(), c4[3].toDouble()).round(),
    ];
  }

  /// 求解线性方程组 (简化版高斯消元法)
  static List<double> _solveLinearSystem(List<List<double>> A, List<double> B) {
    final n = A.length;
    final augmented = <List<double>>[];
    for (int i = 0; i < n; i++) {
      augmented.add([...A[i], B[i]]);
    }

    // 前向消元
    for (int i = 0; i < n; i++) {
      // 找到主元
      int maxRow = i;
      for (int k = i + 1; k < n; k++) {
        if (augmented[k][i].abs() > augmented[maxRow][i].abs()) {
          maxRow = k;
        }
      }
      
      // 交换行
      final temp = augmented[i];
      augmented[i] = augmented[maxRow];
      augmented[maxRow] = temp;

      // 消元
      for (int k = i + 1; k < n; k++) {
        final factor = augmented[k][i] / augmented[i][i];
        for (int j = i; j <= n; j++) {
          augmented[k][j] -= factor * augmented[i][j];
        }
      }
    }

    // 回代求解
    final solution = List<double>.filled(n, 0);
    for (int i = n - 1; i >= 0; i--) {
      solution[i] = augmented[i][n];
      for (int j = i + 1; j < n; j++) {
        solution[i] -= augmented[i][j] * solution[j];
      }
      solution[i] /= augmented[i][i];
    }

    return solution;
  }

  /// 计算四边形的面积（用于确定输出尺寸）
  static double calculateQuadrilateralArea(List<Point> points) {
    // 使用鞋带公式计算多边形面积
    double area = 0;
    for (int i = 0; i < points.length; i++) {
      final j = (i + 1) % points.length;
      area += points[i].x * points[j].y - points[j].x * points[i].y;
    }
    return area.abs() / 2;
  }

  /// 计算输出图像的合适尺寸
  static ImageSize calculateOutputDimensions(List<Point> points, {double maxWidth = 800}) {
    // 计算边长
    final topWidth = math.sqrt(
      math.pow(points[1].x - points[0].x, 2) + math.pow(points[1].y - points[0].y, 2)
    );
    final bottomWidth = math.sqrt(
      math.pow(points[2].x - points[3].x, 2) + math.pow(points[2].y - points[3].y, 2)
    );
    final leftHeight = math.sqrt(
      math.pow(points[3].x - points[0].x, 2) + math.pow(points[3].y - points[0].y, 2)
    );
    final rightHeight = math.sqrt(
      math.pow(points[2].x - points[1].x, 2) + math.pow(points[2].y - points[1].y, 2)
    );

    // 取平均值
    final avgWidth = (topWidth + bottomWidth) / 2;
    final avgHeight = (leftHeight + rightHeight) / 2;
    
    // 保持宽高比，限制最大宽度
    final aspectRatio = avgHeight / avgWidth;
    final outputWidth = math.min(maxWidth, avgWidth);
    final outputHeight = outputWidth * aspectRatio;

    return ImageSize(outputWidth, outputHeight);
  }

  /// 创建默认的裁剪区域（图片中心80%）
  static List<Point> createDefaultCropArea(double imageWidth, double imageHeight) {
    final inset = 0.1; // 10% inset from each edge
    return [
      Point(imageWidth * inset, imageHeight * inset),             // 左上
      Point(imageWidth * (1 - inset), imageHeight * inset),       // 右上  
      Point(imageWidth * (1 - inset), imageHeight * (1 - inset)), // 右下
      Point(imageWidth * inset, imageHeight * (1 - inset)),       // 左下
    ];
  }
}

/// 表示尺寸的简单类（避免与Flutter的Size冲突）
class ImageSize {
  final double width;
  final double height;

  const ImageSize(this.width, this.height);

  @override
  String toString() => 'ImageSize($width, $height)';
}