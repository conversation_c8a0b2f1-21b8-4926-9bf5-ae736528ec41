import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';

class DeviceUtils {
  static Future<String> getDeviceType() async {
    if (!Platform.isIOS) {
      return 'iPhone'; // Default fallback for non-iOS platforms
    }

    try {
      final deviceInfo = DeviceInfoPlugin();
      final iosInfo = await deviceInfo.iosInfo;
      
      // Check if it's an iPad based on the model
      if (iosInfo.model.toLowerCase().contains('ipad')) {
        return 'iPad';
      } else {
        return 'iPhone';
      }
    } catch (e) {
      // Fallback to iPhone if we can't determine the device type
      return 'iPhone';
    }
  }

  static Map<String, dynamic> getScreenInfo(BuildContext context) {
    final MediaQueryData mediaQuery = MediaQuery.of(context);
    final Size screenSize = mediaQuery.size;
    final double devicePixelRatio = mediaQuery.devicePixelRatio;
    final double textScaleFactor = mediaQuery.textScaler.scale(1.0);
    
    return {
      'screenWidth': screenSize.width,
      'screenHeight': screenSize.height,
      'devicePixelRatio': devicePixelRatio,
      'textScaleFactor': textScaleFactor,
      'physicalWidth': screenSize.width * devicePixelRatio,
      'physicalHeight': screenSize.height * devicePixelRatio,
      'padding': mediaQuery.padding,
      'viewInsets': mediaQuery.viewInsets,
    };
  }

  static Future<Map<String, dynamic>> getDetailedDeviceInfo() async {
    final Map<String, dynamic> info = {};
    
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        info.addAll({
          'platform': 'iOS',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'systemVersion': iosInfo.systemVersion,
          'utsname': iosInfo.utsname.machine,
        });
      } else if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        info.addAll({
          'platform': 'Android',
          'model': androidInfo.model,
          'brand': androidInfo.brand,
          'device': androidInfo.device,
          'androidId': androidInfo.id,
          'version': androidInfo.version.release,
        });
      }
    } catch (e) {
      info['error'] = e.toString();
    }
    
    return info;
  }
}