import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../services/network_status_service.dart';
import '../widgets/network_permission_dialog.dart';

/// 网络功能帮助工具类
class NetworkHelper {
  /// 执行需要网络的操作前进行权限和连接检查
  /// 
  /// [context] 构建上下文
  /// [operation] 需要网络的操作函数
  /// [showErrorDialog] 是否显示错误对话框（默认true）
  /// 
  /// 返回操作结果，如果网络不可用返回null
  static Future<T?> performNetworkOperation<T>(
    BuildContext context,
    Future<T> Function() operation, {
    bool showErrorDialog = true,
  }) async {
    final l10n = AppLocalizations.of(context)!;
    try {
      // 检查并请求网络权限
      final hasPermission = await NetworkPermissionHelper.checkAndRequestPermission(context);
      if (!hasPermission) {
        if (showErrorDialog && context.mounted) {
          NetworkPermissionHelper.showNetworkError(
            context,
            l10n.networkAccessDenied
          );
        }
        return null;
      }

      // 执行网络操作
      return await operation();
    } catch (e) {
      if (showErrorDialog && context.mounted) {
        NetworkPermissionHelper.showNetworkError(
          context,
          l10n.networkOperationFailed(e.toString())
        );
      }
      return null;
    }
  }

  /// 检查网络状态并显示相应提示
  static Future<bool> checkNetworkStatus(BuildContext context, {bool showErrorDialog = true}) async {
    final isConnected = await NetworkStatusService.instance.checkNetworkStatus();
    
    if (!isConnected && showErrorDialog && context.mounted) {
      final retry = await showDialog<bool>(
        context: context,
        builder: (context) => const NetworkStatusDialog(),
      );
      return retry ?? false;
    }
    
    return isConnected;
  }

  /// 在需要网络的Widget中使用的StreamBuilder帮助方法
  static Widget buildNetworkStatusWidget({
    required Widget Function(bool isConnected) builder,
    Widget Function()? loadingBuilder,
  }) {
    return StreamBuilder<bool>(
      stream: NetworkStatusService.instance.networkStatusStream,
      initialData: NetworkStatusService.instance.isConnected,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return loadingBuilder != null ? loadingBuilder() : const CircularProgressIndicator();
        }
        return builder(snapshot.data!);
      },
    );
  }

  /// 显示网络状态指示器
  static Widget buildNetworkIndicator(BuildContext context) {
    return buildNetworkStatusWidget(
      builder: (isConnected) {
        final l10n = AppLocalizations.of(context)!;
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: isConnected ? Colors.green : Colors.red,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isConnected ? Icons.wifi : Icons.wifi_off,
                color: Colors.white,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                isConnected ? l10n.connected : l10n.noNetwork,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}