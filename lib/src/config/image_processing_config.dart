/// 图像处理服务配置类
class ImageProcessingConfig {
  // 默认配置常量 - 统一定义处，避免多处重复
  static const String _defaultBaseUrl = 'https://all-pdf-editor.apusai.com';
  static const String _defaultClientSecret = 'test-key';
  static const int _defaultTtl = 3600;
  static const int _defaultTimeoutSeconds = 30;

  final String baseUrl;
  final String clientSecret;
  final int defaultTtl;
  final Duration timeout;

  const ImageProcessingConfig({
    this.baseUrl = _defaultBaseUrl,
    this.clientSecret = _defaultClientSecret,
    this.defaultTtl = _defaultTtl,
    this.timeout = const Duration(seconds: _defaultTimeoutSeconds),
  });

  // 预留Firebase远程配置接口
  static ImageProcessingConfig? _remoteConfig;
  
  static ImageProcessingConfig get instance {
    return _remoteConfig ?? const ImageProcessingConfig();
  }
  
  static void updateFromRemoteConfig(Map<String, dynamic> config) {
    _remoteConfig = ImageProcessingConfig(
      baseUrl: config['baseUrl'] ?? _defaultBaseUrl,
      clientSecret: config['clientSecret'] ?? _defaultClientSecret,
      defaultTtl: config['defaultTtl'] ?? _defaultTtl,
      timeout: Duration(seconds: config['timeoutSeconds'] ?? _defaultTimeoutSeconds),
    );
  }
}