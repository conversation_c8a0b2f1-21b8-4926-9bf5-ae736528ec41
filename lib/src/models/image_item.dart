import 'dart:io';

enum ImageProcessingStatus {
  original,      // 原始图像
  processing,    // AI处理中
  processed,     // AI处理完成
  failed,        // AI处理失败
  lowResolution, // 分辨率太低，无法处理
  cropped,       // 用户手工裁剪完成
}

class CropData {
  final List<Point> corners;
  final double originalWidth;
  final double originalHeight;

  CropData({
    required this.corners,
    required this.originalWidth,
    required this.originalHeight,
  });

  Map<String, dynamic> toJson() {
    return {
      'corners': corners.map((p) => [p.x, p.y]).toList(),
      'originalWidth': originalWidth,
      'originalHeight': originalHeight,
    };
  }

  factory CropData.fromJson(Map<String, dynamic> json) {
    final cornersJson = json['corners'] as List<dynamic>;
    final corners = cornersJson
        .map((c) => Point(c[0] as double, c[1] as double))
        .toList();
    
    return CropData(
      corners: corners,
      originalWidth: json['originalWidth'] as double,
      originalHeight: json['originalHeight'] as double,
    );
  }
}

class Point {
  final double x;
  final double y;

  const Point(this.x, this.y);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Point && 
      runtimeType == other.runtimeType &&
      x == other.x &&
      y == other.y;

  @override
  int get hashCode => x.hashCode ^ y.hashCode;

  @override
  String toString() => 'Point($x, $y)';
}

class ImageItem {
  final String id;
  final File originalFile;
  final DateTime createdAt;
  
  ImageProcessingStatus status;
  File? processedFile;
  CropData? aiCropData;
  CropData? userCropData;
  String? errorMessage;
  Map<String, dynamic>? metadata;

  ImageItem({
    required this.id,
    required this.originalFile,
    required this.createdAt,
    this.status = ImageProcessingStatus.original,
    this.processedFile,
    this.aiCropData,
    this.userCropData,
    this.errorMessage,
    this.metadata,
  });

  // 获取当前应该显示的图像文件
  File get currentFile {
    // 优先显示用户裁剪的图像
    if (status == ImageProcessingStatus.cropped && processedFile != null) {
      return processedFile!;
    }
    
    // 其次显示AI处理的图像
    if (status == ImageProcessingStatus.processed && processedFile != null) {
      return processedFile!;
    }
    
    // 最后显示原图
    return originalFile;
  }

  // 获取当前的裁剪数据
  CropData? get currentCropData {
    return userCropData ?? aiCropData;
  }

  // 是否应该显示crop按钮
  bool get shouldShowCropButton {
    return status == ImageProcessingStatus.processed || 
           status == ImageProcessingStatus.failed ||
           status == ImageProcessingStatus.cropped;
  }

  // 是否是分辨率太低
  bool get isLowResolution {
    return status == ImageProcessingStatus.lowResolution;
  }

  // 是否正在处理中
  bool get isProcessing {
    return status == ImageProcessingStatus.processing;
  }

  // 复制并更新状态
  ImageItem copyWith({
    ImageProcessingStatus? status,
    File? processedFile,
    CropData? aiCropData,
    CropData? userCropData,
    String? errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    return ImageItem(
      id: id,
      originalFile: originalFile,
      createdAt: createdAt,
      status: status ?? this.status,
      processedFile: processedFile ?? this.processedFile,
      aiCropData: aiCropData ?? this.aiCropData,
      userCropData: userCropData ?? this.userCropData,
      errorMessage: errorMessage ?? this.errorMessage,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'originalFile': originalFile.path,
      'createdAt': createdAt.toIso8601String(),
      'status': status.index,
      'processedFile': processedFile?.path,
      'aiCropData': aiCropData?.toJson(),
      'userCropData': userCropData?.toJson(),
      'errorMessage': errorMessage,
      'metadata': metadata,
    };
  }

  factory ImageItem.fromJson(Map<String, dynamic> json) {
    return ImageItem(
      id: json['id'] as String,
      originalFile: File(json['originalFile'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      status: ImageProcessingStatus.values[json['status'] as int],
      processedFile: json['processedFile'] != null 
          ? File(json['processedFile'] as String) 
          : null,
      aiCropData: json['aiCropData'] != null 
          ? CropData.fromJson(json['aiCropData'] as Map<String, dynamic>)
          : null,
      userCropData: json['userCropData'] != null 
          ? CropData.fromJson(json['userCropData'] as Map<String, dynamic>)
          : null,
      errorMessage: json['errorMessage'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  @override
  String toString() {
    return 'ImageItem(id: $id, status: $status, originalFile: ${originalFile.path})';
  }
}