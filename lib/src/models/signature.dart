import 'dart:ui';

class SignatureStroke {
  final List<Offset> points;
  final Color color;
  final double strokeWidth;

  SignatureStroke({
    required this.points,
    required this.color,
    required this.strokeWidth,
  });

  Map<String, dynamic> toJson() {
    return {
      'points': points.map((p) => {'x': p.dx, 'y': p.dy}).toList(),
      'color': color.value,
      'strokeWidth': strokeWidth,
    };
  }

  factory SignatureStroke.fromJson(Map<String, dynamic> json) {
    final pointsList = (json['points'] as List).cast<Map<String, dynamic>>();
    final points = pointsList.map((p) => Offset(p['x'], p['y'])).toList();
    
    return SignatureStroke(
      points: points,
      color: Color(json['color']),
      strokeWidth: json['strokeWidth'],
    );
  }
}

class Signature {
  final String id;
  final String name;
  final List<SignatureStroke> strokes;
  final DateTime createdAt;
  final Size canvasSize;

  Signature({
    required this.id,
    required this.name,
    required this.strokes,
    required this.createdAt,
    required this.canvasSize,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'strokes': strokes.map((s) => s.toJson()).toList(),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'canvasSize': {'width': canvasSize.width, 'height': canvasSize.height},
    };
  }

  factory Signature.fromJson(Map<String, dynamic> json) {
    final strokesList = (json['strokes'] as List).cast<Map<String, dynamic>>();
    final strokes = strokesList.map((s) => SignatureStroke.fromJson(s)).toList();
    final canvasSizeJson = json['canvasSize'] as Map<String, dynamic>;
    
    return Signature(
      id: json['id'],
      name: json['name'],
      strokes: strokes,
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      canvasSize: Size(canvasSizeJson['width'], canvasSizeJson['height']),
    );
  }

  bool get isEmpty => strokes.isEmpty;
}