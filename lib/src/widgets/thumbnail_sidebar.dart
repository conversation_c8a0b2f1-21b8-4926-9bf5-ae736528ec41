import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'thumbnail_manager.dart';
import '../../l10n/app_localizations.dart';

class ThumbnailSidebar extends StatelessWidget {
  final ThumbnailManager manager;
  final VoidCallback onClose;
  final Function(double) onThumbnailTap;
  final bool mainWebViewLoaded;

  const ThumbnailSidebar({
    super.key,
    required this.manager,
    required this.onClose,
    required this.onThumbnailTap,
    required this.mainWebViewLoaded,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: () {
        final w = MediaQuery.of(context).size.width;
        final computed = w * 0.35;
        if (computed < 200) return 200.0;
        if (computed > 250) return 250.0;
        return computed;
      }(),
      color: Colors.white,
      child: Column(
        children: [
          _buildHeader(context),
          Expanded(
            child: _buildThumbnailView(context),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              AppLocalizations.of(context)!.thumbnails,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: onClose,
          ),
        ],
      ),
    );
  }

  Widget _buildThumbnailView(BuildContext context) {
    if (!Platform.isIOS) {
      return Center(child: Text(AppLocalizations.of(context)!.thumbnailsOnlyIos));
    }
    
    if (!mainWebViewLoaded) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(AppLocalizations.of(context)!.waitDocumentComplete),
          ],
        ),
      );
    }

    return AnimatedBuilder(
      animation: manager,
      builder: (context, child) {
        if (manager.thumbnailData == null && !manager.isGenerating) {
          return _buildGenerateThumbnailPrompt(context);
        }

        if (manager.isGenerating) {
          return _buildGeneratingView(context);
        }

        if (manager.thumbnailData != null) {
          return Container(
            padding: const EdgeInsets.all(16),
            child: _buildScreenshotThumbnail(context),
          );
        }

        return _buildFallbackView(context);
      },
    );
  }

  Widget _buildGenerateThumbnailPrompt(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_outlined,
              size: 48,
              color: Colors.grey.shade600,
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context)!.thumbnails,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              AppLocalizations.of(context)!.thumbnailDescription,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneratingView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(AppLocalizations.of(context)!.generatingThumbnail),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context)!.analyzingDocument,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScreenshotThumbnail(BuildContext context) {
    if (kDebugMode) {
      print('[ThumbnailSidebar] Building screenshot-based thumbnail');
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300, width: 2),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: Stack(
          children: [
            Positioned.fill(
              child: Image.memory(
                manager.thumbnailData!,
                fit: BoxFit.contain,
              ),
            ),
            Positioned.fill(
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTapDown: (details) async {
                    final box = context.findRenderObject() as RenderBox?;
                    final size = box?.size;
                    if (size == null) return;

                    final dy = details.localPosition.dy.clamp(0.0, size.height);
                    final ratio = size.height == 0 ? 0.0 : (dy / size.height);
                    if (kDebugMode) {
                      print('[ThumbnailSidebar] Thumbnail overlay tap — dy: $dy, height: ${size.height}, ratio: $ratio');
                    }
                    onThumbnailTap(ratio);
                  },
                ),
              ),
            ),
            Positioned(
              top: 8,
              right: 8,
              child: Material(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(16),
                child: InkWell(
                  borderRadius: BorderRadius.circular(16),
                  onTap: () {
                    manager.refreshThumbnail();
                  },
                  child: const Padding(
                    padding: EdgeInsets.all(6),
                    child: Icon(
                      Icons.refresh,
                      size: 16,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFallbackView(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300, width: 2),
          borderRadius: BorderRadius.circular(8),
          color: Colors.grey.shade50,
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.article_outlined,
                size: 48,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context)!.thumbnailUnavailable,
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                AppLocalizations.of(context)!.clickRefreshRetry,
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}