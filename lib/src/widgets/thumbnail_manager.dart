import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class ThumbnailManager extends ChangeNotifier {
  Uint8List? _thumbnailData;
  bool _isGenerating = false;
  bool _isVisible = false;
  HeadlessInAppWebView? _headlessWebView;

  Uint8List? get thumbnailData => _thumbnailData;
  bool get isGenerating => _isGenerating;
  bool get isVisible => _isVisible;

  void toggleVisibility() {
    if (kDebugMode) {
      print('[ThumbnailManager] Toggling thumbnails display: $_isVisible -> ${!_isVisible}');
    }
    
    _isVisible = !_isVisible;
    if (_isVisible) {
      if (kDebugMode) {
        print('[ThumbnailManager] Thumbnail sidebar opening');
      }
    } else {
      if (kDebugMode) {
        print('[ThumbnailManager] Thumbnail sidebar closing');
      }
    }
    notifyListeners();
  }

  Future<void> generateThumbnail(String filePath) async {
    if (_isGenerating) return;
    
    _isGenerating = true;
    notifyListeners();
    
    try {
      if (kDebugMode) {
        print('[ThumbnailManager] Generating full document thumbnail using HeadlessWebView');
      }
      
      await _disposeHeadlessWebView();
      
      final fileUrl = 'file://$filePath';
      
      if (kDebugMode) {
        print('[ThumbnailManager] Creating HeadlessWebView for: $fileUrl');
      }
      
      _headlessWebView = HeadlessInAppWebView(
        initialUrlRequest: URLRequest(url: WebUri(fileUrl)),
        initialSize: const Size(800, 1200),
        initialSettings: InAppWebViewSettings(
          javaScriptEnabled: true,
          allowsInlineMediaPlayback: true,
          mediaPlaybackRequiresUserGesture: false,
          allowFileAccessFromFileURLs: true,
          allowUniversalAccessFromFileURLs: true,
          allowFileAccess: true,
          supportZoom: false,
          builtInZoomControls: false,
          displayZoomControls: false,
        ),
        onWebViewCreated: (controller) {
          if (kDebugMode) {
            print('[ThumbnailManager] HeadlessWebView created for thumbnail');
          }
        },
        onLoadStop: (controller, url) async {
          await _handleHeadlessLoadComplete(controller);
        },
        onReceivedError: (controller, request, error) {
          if (kDebugMode) {
            print('[ThumbnailManager] HeadlessWebView error: ${error.description}');
          }
          _handleThumbnailError();
        },
        onReceivedHttpError: (controller, request, errorResponse) {
          if (kDebugMode) {
            print('[ThumbnailManager] HeadlessWebView HTTP error: ${errorResponse.statusCode}');
          }
          _handleThumbnailError();
        },
      );
      
      await _headlessWebView!.run();
      
      if (kDebugMode) {
        print('[ThumbnailManager] HeadlessWebView started');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('[ThumbnailManager] Error creating HeadlessWebView: $e');
      }
      _handleThumbnailError();
    }
  }

  Future<void> _handleHeadlessLoadComplete(InAppWebViewController controller) async {
    try {
      if (kDebugMode) {
        print('[ThumbnailManager] HeadlessWebView loaded, detecting document size');
      }
      
      await Future.delayed(const Duration(milliseconds: 1000));
      
      final sizeResult = await controller.evaluateJavascript(source: '''
        (function() {
          try {
            const docHeight = Math.max(
              document.body.scrollHeight,
              document.body.offsetHeight,
              document.documentElement.clientHeight,
              document.documentElement.scrollHeight,
              document.documentElement.offsetHeight
            );
            const docWidth = Math.max(
              document.body.scrollWidth,
              document.body.offsetWidth,
              document.documentElement.clientWidth,
              document.documentElement.scrollWidth,
              document.documentElement.offsetWidth
            );
            return JSON.stringify({ width: docWidth, height: docHeight });
          } catch (e) {
            return JSON.stringify({ error: String(e) });
          }
        })();
      ''');
      
      if (kDebugMode) {
        print('[ThumbnailManager] Document size result: $sizeResult');
      }
      
      if (sizeResult != null) {
        try {
          final sizeDataStr = sizeResult.toString();
          if (sizeDataStr.startsWith('{')) {
            final sizeMap = <String, dynamic>{};
            try {
              final cleanJson = sizeDataStr.replaceAll(RegExp(r'[{}""]'), '').split(',');
              for (final item in cleanJson) {
                final parts = item.split(':');
                if (parts.length == 2) {
                  final key = parts[0].trim();
                  final value = double.tryParse(parts[1].trim());
                  if (value != null) {
                    sizeMap[key] = value;
                  }
                }
              }
            } catch (jsonError) {
              if (kDebugMode) {
                print('[ThumbnailManager] JSON parsing error: $jsonError');
              }
            }
            
            double targetWidth = (sizeMap['width'] as double?) ?? 1024;
            double targetHeight = (sizeMap['height'] as double?) ?? 1536;
            
            if (targetWidth < 800) targetWidth = 800;
            if (targetHeight < 600) targetHeight = 600;
            if (targetWidth > 1200) targetWidth = 1200;
            
            final targetSize = Size(targetWidth, targetHeight);
            
            await _headlessWebView!.setSize(targetSize);
            
            if (kDebugMode) {
              print('[ThumbnailManager] Set HeadlessWebView size to: $targetSize to capture full document (original: ${sizeMap['width']}x${sizeMap['height']})');
            }
          }
          
          await Future.delayed(const Duration(milliseconds: 1000));
          
        } catch (parseError) {
          if (kDebugMode) {
            print('[ThumbnailManager] Error parsing size data: $parseError, using default size');
          }
          await _headlessWebView!.setSize(const Size(1024, 1536));
        }
      } else {
        await _headlessWebView!.setSize(const Size(1024, 1536));
      }
      
      await controller.evaluateJavascript(source: '''
        window.scrollTo({ top: 0, behavior: 'auto' });
        document.body.style.height = 'auto';
        document.documentElement.style.height = 'auto';
      ''');
      
      await Future.delayed(const Duration(milliseconds: 1500));
      
      final screenshot = await controller.takeScreenshot();
      
      if (screenshot != null) {
        _thumbnailData = screenshot;
        _isGenerating = false;
        notifyListeners();
        
        if (kDebugMode) {
          print('[ThumbnailManager] Full document thumbnail generated successfully');
        }
      } else {
        _handleThumbnailError();
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('[ThumbnailManager] Error in HeadlessWebView load completion: $e');
      }
      _handleThumbnailError();
    } finally {
      await _disposeHeadlessWebView();
    }
  }

  void _handleThumbnailError() {
    _isGenerating = false;
    notifyListeners();
  }

  Future<void> _disposeHeadlessWebView() async {
    if (_headlessWebView != null) {
      try {
        if (_headlessWebView!.isRunning()) {
          await _headlessWebView!.dispose();
        }
        _headlessWebView = null;
        if (kDebugMode) {
          print('[ThumbnailManager] HeadlessWebView disposed');
        }
      } catch (e) {
        if (kDebugMode) {
          print('[ThumbnailManager] Error disposing HeadlessWebView: $e');
        }
      }
    }
  }

  Future<void> scrollToRatio(double ratio, InAppWebViewController controller) async {
    final clamped = ratio.clamp(0.0, 1.0);
    final js = '''
      (function(){
        try {
          const total = Math.max(document.documentElement.scrollHeight || 0, document.body.scrollHeight || 0);
          const view = window.innerHeight || 0;
          const maxScroll = Math.max(0, total - view);
          const target = Math.max(0, Math.min(maxScroll, $clamped * maxScroll));
          window.scrollTo({ top: target, behavior: 'smooth' });
          return JSON.stringify({ total, view, maxScroll, target });
        } catch (e) {
          return JSON.stringify({ error: String(e) });
        }
      })();
    ''';
    try {
      final res = await controller.evaluateJavascript(source: js);
      if (kDebugMode) {
        print('[ThumbnailManager] Main scroll result: $res');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[ThumbnailManager] Error scrolling main view: $e');
      }
    }
  }

  void refreshThumbnail() {
    _thumbnailData = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _disposeHeadlessWebView();
    _thumbnailData = null;
    super.dispose();
  }
}