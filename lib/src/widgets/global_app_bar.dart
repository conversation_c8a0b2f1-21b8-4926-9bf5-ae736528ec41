import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../state/app_state.dart';
import '../screens/search_screen.dart';
import '../screens/signature_management_screen.dart';
import '../../l10n/app_localizations.dart';

class GlobalAppBar extends StatelessWidget implements PreferredSizeWidget {
  const GlobalAppBar({super.key});

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AppState>();
    final brand = state.activeBrandTheme;

    return AppBar(
      backgroundColor: brand.brandColor,
      foregroundColor: Theme.of(context).colorScheme.onPrimary,
      title: Text(AppLocalizations.of(context)!.appName),
      actions: [
        IconButton(
          tooltip: 'Sort',
          icon: const Icon(Icons.sort),
          onPressed: () async {
            await _showSortSheet(context);
          },
        ),
        IconButton(
          tooltip: 'Search',
          icon: const Icon(Icons.search),
          onPressed: () {
            Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const SearchScreen()),
            );
          },
        ),
        IconButton(
          tooltip: AppLocalizations.of(context)!.signatures,
          icon: const Icon(Icons.draw),
          onPressed: () {
            Navigator.of(context).push(
              MaterialPageRoute(builder: (_) => const SignatureManagementScreen()),
            );
          },
        ),
        const SizedBox(width: 8),
      ],
    );
  }
}

Future<void> _showSortSheet(BuildContext context) async {
  final state = context.read<AppState>();
  await showModalBottomSheet(
    context: context,
    showDragHandle: true,
    shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(24))),
    builder: (context) {
      var by = state.sortBy;
      var order = state.sortOrder;
      final textTheme = Theme.of(context).textTheme;
      final colorScheme = Theme.of(context).colorScheme;
      final titleStyle = textTheme.titleLarge?.copyWith(color: colorScheme.onSurface);
      final itemStyle = textTheme.titleMedium?.copyWith(color: colorScheme.onSurface);

      return StatefulBuilder(builder: (context, setState) {
        return SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(AppLocalizations.of(context)!.sort, style: titleStyle),
                  const SizedBox(height: 8),
                  RadioListTile<SortBy>(
                    value: SortBy.name,
                    groupValue: by,
                    title: Text(AppLocalizations.of(context)!.nameSort, style: itemStyle),
                    onChanged: (v) => setState(() => by = v!),
                  ),
                  RadioListTile<SortBy>(
                    value: SortBy.modified,
                    groupValue: by,
                    title: Text(AppLocalizations.of(context)!.lastModified, style: itemStyle),
                    onChanged: (v) => setState(() => by = v!),
                  ),
                  RadioListTile<SortBy>(
                    value: SortBy.size,
                    groupValue: by,
                    title: Text(AppLocalizations.of(context)!.sizeSort, style: itemStyle),
                    onChanged: (v) => setState(() => by = v!),
                  ),
                  const Divider(),
                  SwitchListTile(
                    value: order == SortOrder.desc,
                    onChanged: (v) => setState(() => order = v ? SortOrder.desc : SortOrder.asc),
                    title: Text(AppLocalizations.of(context)!.descending, style: itemStyle),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text(AppLocalizations.of(context)!.cancel),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: FilledButton(
                          onPressed: () {
                            context.read<AppState>().setSorting(by, order);
                            Navigator.pop(context);
                          },
                          child: Text(AppLocalizations.of(context)!.apply),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),
        );
      });
    },
  );
}