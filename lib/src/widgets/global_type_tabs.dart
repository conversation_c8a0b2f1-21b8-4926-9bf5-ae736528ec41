import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../state/app_state.dart';
import '../utils/device_utils.dart';

class GlobalTypeTabs extends StatefulWidget {
  const GlobalTypeTabs({super.key});

  @override
  State<GlobalTypeTabs> createState() => _GlobalTypeTabsState();
}

class _GlobalTypeTabsState extends State<GlobalTypeTabs> {
  bool _debugInfoLogged = false;

  double _measureTextWidth(String text, TextStyle style, double textScaleFactor) {
    final TextPainter textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      maxLines: 1,
      textDirection: TextDirection.ltr,
      textScaler: TextScaler.linear(textScaleFactor),
    );
    textPainter.layout();
    return textPainter.width;
  }

  void _logDebugInfo(BuildContext context, BoxConstraints constraints) async {
    if (_debugInfoLogged) return;
    _debugInfoLogged = true;

    if (kDebugMode) {
      final screenInfo = DeviceUtils.getScreenInfo(context);
      final deviceInfo = await DeviceUtils.getDetailedDeviceInfo();
      
      final availableWidth = constraints.maxWidth;
      final horizontalPadding = 0.0; // No horizontal padding - true edge-to-edge
      final effectiveWidth = availableWidth - horizontalPadding;
      final buttonWidth = effectiveWidth / 4; // 4 buttons
      
      // Get theme and text styles
      final theme = Theme.of(context);
      final normalTextStyle = theme.textTheme.labelLarge ?? const TextStyle(fontSize: 14);
      final compactTextStyle = const TextStyle(fontSize: 14); // Increased to match normal text
      final textScaleFactor = screenInfo['textScaleFactor'];
      
      // Measure actual text widths
      final labels = ['PDF', 'Word', 'Excel', 'PPT'];
      final normalWidths = <String, double>{};
      final compactWidths = <String, double>{};
      
      for (final label in labels) {
        normalWidths[label] = _measureTextWidth(label, normalTextStyle, textScaleFactor);
        compactWidths[label] = _measureTextWidth(label, compactTextStyle, textScaleFactor);
      }
      
      // SegmentedButton spacing analysis (updated for actual measurements)
      final iconWidth = 24.0;
      final iconTextSpacing = 8.0; // Space between icon and text
      final horizontalButtonPadding = 16.0; // Actual internal horizontal padding (higher than expected)
      
      print('[GlobalTypeTabs] Screen Debug Info:');
      print('[GlobalTypeTabs]   Screen Size: ${screenInfo['screenWidth'].toStringAsFixed(1)} x ${screenInfo['screenHeight'].toStringAsFixed(1)} dp');
      print('[GlobalTypeTabs]   Physical Size: ${screenInfo['physicalWidth'].toStringAsFixed(0)} x ${screenInfo['physicalHeight'].toStringAsFixed(0)} px');
      print('[GlobalTypeTabs]   Device Pixel Ratio: ${screenInfo['devicePixelRatio'].toStringAsFixed(2)}');
      print('[GlobalTypeTabs]   Text Scale Factor: ${screenInfo['textScaleFactor'].toStringAsFixed(2)}');
      print('[GlobalTypeTabs]   Available Width: ${availableWidth.toStringAsFixed(1)} dp');
      print('[GlobalTypeTabs]   Effective Width: ${effectiveWidth.toStringAsFixed(1)} dp');
      print('[GlobalTypeTabs]   Button Width: ${buttonWidth.toStringAsFixed(1)} dp');
      print('[GlobalTypeTabs]   Safe Area Padding: ${screenInfo['padding']}');
      
      print('[GlobalTypeTabs] Device Info:');
      deviceInfo.forEach((key, value) {
        print('[GlobalTypeTabs]   $key: $value');
      });
      
      print('[GlobalTypeTabs] Text Width Measurements (Normal):');
      normalWidths.forEach((label, width) {
        print('[GlobalTypeTabs]   "$label": ${width.toStringAsFixed(1)} dp');
      });
      
      print('[GlobalTypeTabs] Text Width Measurements (Compact):');
      compactWidths.forEach((label, width) {
        print('[GlobalTypeTabs]   "$label": ${width.toStringAsFixed(1)} dp');
      });
      
      // Calculate required space for each mode
      final maxNormalTextWidth = normalWidths.values.reduce((a, b) => a > b ? a : b);
      final maxCompactTextWidth = compactWidths.values.reduce((a, b) => a > b ? a : b);
      
      final fullModeMinWidth = iconWidth + iconTextSpacing + maxNormalTextWidth + horizontalButtonPadding;
      final compactModeMinWidth = iconWidth + iconTextSpacing + maxCompactTextWidth + horizontalButtonPadding;
      final iconOnlyMinWidth = iconWidth + horizontalButtonPadding;
      
      print('[GlobalTypeTabs] Space Requirements:');
      print('[GlobalTypeTabs]   Icon width: ${iconWidth.toStringAsFixed(1)} dp');
      print('[GlobalTypeTabs]   Icon-text spacing: ${iconTextSpacing.toStringAsFixed(1)} dp');
      print('[GlobalTypeTabs]   Button padding: ${horizontalButtonPadding.toStringAsFixed(1)} dp');
      print('[GlobalTypeTabs]   Max normal text width: ${maxNormalTextWidth.toStringAsFixed(1)} dp');
      print('[GlobalTypeTabs]   Max compact text width: ${maxCompactTextWidth.toStringAsFixed(1)} dp');
      print('[GlobalTypeTabs]   Full mode min width: ${fullModeMinWidth.toStringAsFixed(1)} dp');
      print('[GlobalTypeTabs]   Compact mode min width: ${compactModeMinWidth.toStringAsFixed(1)} dp');
      print('[GlobalTypeTabs]   Icon-only min width: ${iconOnlyMinWidth.toStringAsFixed(1)} dp');
      
      // Determine optimal layout mode
      String recommendedMode;
      if (buttonWidth >= fullModeMinWidth) {
        recommendedMode = 'FULL (show all text)';
      } else if (buttonWidth >= compactModeMinWidth) {
        recommendedMode = 'COMPACT (smaller text)';
      } else if (buttonWidth >= iconOnlyMinWidth) {
        recommendedMode = 'ICON_ONLY (no text)';
      } else {
        recommendedMode = 'TOO_SMALL (need scrolling)';
      }
      
      print('[GlobalTypeTabs] Layout Analysis:');
      print('[GlobalTypeTabs]   Current button width: ${buttonWidth.toStringAsFixed(1)} dp');
      print('[GlobalTypeTabs]   Recommended mode: $recommendedMode');
      print('[GlobalTypeTabs]   Wrap risk analysis:');
      for (final label in labels) {
        final normalRequired = iconWidth + iconTextSpacing + normalWidths[label]! + horizontalButtonPadding;
        final compactRequired = iconWidth + iconTextSpacing + compactWidths[label]! + horizontalButtonPadding;
        print('[GlobalTypeTabs]     "$label" normal: ${normalRequired.toStringAsFixed(1)} dp (${buttonWidth >= normalRequired ? 'OK' : 'WRAP'})');
        print('[GlobalTypeTabs]     "$label" compact: ${compactRequired.toStringAsFixed(1)} dp (${buttonWidth >= compactRequired ? 'OK' : 'WRAP'})');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AppState>();
    final brand = state.activeBrandTheme;

    return LayoutBuilder(
      builder: (context, constraints) {
        // Log debug info once
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _logDebugInfo(context, constraints);
        });

        final availableWidth = constraints.maxWidth;
        final horizontalPadding = 0.0; // No horizontal padding - true edge-to-edge
        final effectiveWidth = availableWidth - horizontalPadding;
        final buttonWidth = effectiveWidth / 4;
        
        // Get theme and calculate space requirements precisely
        final theme = Theme.of(context);
        final normalTextStyle = theme.textTheme.labelLarge ?? const TextStyle(fontSize: 14);
        final compactTextStyle = const TextStyle(fontSize: 14); // Increased to match normal text
        final screenInfo = DeviceUtils.getScreenInfo(context);
        final textScaleFactor = screenInfo['textScaleFactor'];
        
        // Measure text widths for all labels
        final labels = ['PDF', 'Word', 'Excel', 'PPT'];
        final normalWidths = <String, double>{};
        final compactWidths = <String, double>{};
        
        for (final label in labels) {
          normalWidths[label] = _measureTextWidth(label, normalTextStyle, textScaleFactor);
          compactWidths[label] = _measureTextWidth(label, compactTextStyle, textScaleFactor);
        }
        
        // SegmentedButton internal spacing (updated)
        final iconWidth = 24.0;
        final iconTextSpacing = 8.0;
        final horizontalButtonPadding = 16.0; // Increased based on actual measurements
        
        // Calculate minimum required widths
        final maxNormalTextWidth = normalWidths.values.reduce((a, b) => a > b ? a : b);
        final maxCompactTextWidth = compactWidths.values.reduce((a, b) => a > b ? a : b);
        
        final fullModeMinWidth = iconWidth + iconTextSpacing + maxNormalTextWidth + horizontalButtonPadding;
        final compactModeMinWidth = iconWidth + iconTextSpacing + maxCompactTextWidth + horizontalButtonPadding;
        final iconOnlyMinWidth = iconWidth + horizontalButtonPadding;

        List<ButtonSegment<DocType>> segments;
        
        // Determine layout mode based on precise measurements  
        if (buttonWidth >= fullModeMinWidth + 4) { // Reduced safety margin for larger buttons
          // Full text mode - sufficient space for normal text
          segments = const [
            ButtonSegment(value: DocType.pdf, icon: Icon(Icons.picture_as_pdf), label: Text('PDF')),
            ButtonSegment(value: DocType.word, icon: Icon(Icons.text_snippet), label: Text('Word')),
            ButtonSegment(value: DocType.excel, icon: Icon(Icons.grid_on), label: Text('Excel')),
            ButtonSegment(value: DocType.ppt, icon: Icon(Icons.slideshow), label: Text('PPT')),
          ];
        } else if (buttonWidth >= compactModeMinWidth + 4) { // Slightly reduced safety margin
          // Compact text mode - now same font size as normal
          segments = const [
            ButtonSegment(
              value: DocType.pdf, 
              icon: Icon(Icons.picture_as_pdf), 
              label: Text('PDF', style: TextStyle(fontSize: 14))
            ),
            ButtonSegment(
              value: DocType.word, 
              icon: Icon(Icons.text_snippet), 
              label: Text('Word', style: TextStyle(fontSize: 14))
            ),
            ButtonSegment(
              value: DocType.excel, 
              icon: Icon(Icons.grid_on), 
              label: Text('Excel', style: TextStyle(fontSize: 14))
            ),
            ButtonSegment(
              value: DocType.ppt, 
              icon: Icon(Icons.slideshow), 
              label: Text('PPT', style: TextStyle(fontSize: 14))
            ),
          ];
        } else if (buttonWidth >= iconOnlyMinWidth) {
          // Icon-only mode - no text labels
          segments = const [
            ButtonSegment(value: DocType.pdf, icon: Icon(Icons.picture_as_pdf)),
            ButtonSegment(value: DocType.word, icon: Icon(Icons.text_snippet)),
            ButtonSegment(value: DocType.excel, icon: Icon(Icons.grid_on)),
            ButtonSegment(value: DocType.ppt, icon: Icon(Icons.slideshow)),
          ];
        } else {
          // Fallback: Try ultra-compact with very short labels and smaller icons
          segments = const [
            ButtonSegment(
              value: DocType.pdf, 
              icon: Icon(Icons.picture_as_pdf, size: 18), 
              label: Text('PDF', style: TextStyle(fontSize: 11))
            ),
            ButtonSegment(
              value: DocType.word, 
              icon: Icon(Icons.text_snippet, size: 18), 
              label: Text('Doc', style: TextStyle(fontSize: 11))
            ),
            ButtonSegment(
              value: DocType.excel, 
              icon: Icon(Icons.grid_on, size: 18), 
              label: Text('XLS', style: TextStyle(fontSize: 11))
            ),
            ButtonSegment(
              value: DocType.ppt, 
              icon: Icon(Icons.slideshow, size: 18), 
              label: Text('PPT', style: TextStyle(fontSize: 11))
            ),
          ];
        }

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 12),
          child: SegmentedButton<DocType>(
            segments: segments,
            selected: <DocType>{state.activeDocType},
            onSelectionChanged: (set) {
              final type = set.first;
              context.read<AppState>().setDocType(type, isUserTriggered: true);
            },
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.resolveWith((states) {
                return states.contains(WidgetState.selected)
                    ? brand.brandColor.withValues(alpha: 0.12)
                    : Theme.of(context).colorScheme.surface;
              }),
              foregroundColor: WidgetStateProperty.resolveWith((states) {
                return states.contains(WidgetState.selected)
                    ? brand.brandColor
                    : Theme.of(context).colorScheme.onSurfaceVariant;
              }),
              padding: WidgetStateProperty.all(
                const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
              ),
              minimumSize: WidgetStateProperty.all(const Size(0, 36)),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
        );
      },
    );
  }
}