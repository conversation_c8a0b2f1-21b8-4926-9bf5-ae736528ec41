import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../services/network_permission_service.dart';
import '../services/network_status_service.dart';

/// 网络权限提示对话框
class NetworkPermissionDialog extends StatefulWidget {
  const NetworkPermissionDialog({super.key});

  @override
  State<NetworkPermissionDialog> createState() => _NetworkPermissionDialogState();
}

class _NetworkPermissionDialogState extends State<NetworkPermissionDialog> {
  bool _isRequesting = false;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return AlertDialog(
      title: Row(
        children: [
          const Icon(Icons.wifi_off, color: Colors.orange),
          const SizedBox(width: 8),
          Text(l10n.networkPermission),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.networkPermissionRequest,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          Text(
            l10n.featuresInclude,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text('• ${l10n.imageClarityEnhancement}'),
          Text('• ${l10n.documentEdgeDetection}'),
          Text('• ${l10n.imageOptimization}'),
          const SizedBox(height: 16),
          Text(
            NetworkPermissionService.instance.getNetworkPermissionGuide(l10n),
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isRequesting ? null : () {
            Navigator.of(context).pop(false);
          },
          child: Text(l10n.cancel),
        ),
        ElevatedButton(
          onPressed: _isRequesting ? null : _requestPermission,
          child: _isRequesting
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(l10n.requestPermission),
        ),
      ],
    );
  }

  Future<void> _requestPermission() async {
    setState(() {
      _isRequesting = true;
    });

    try {
      final granted = await NetworkPermissionService.instance.requestNetworkPermission();
      
      if (mounted) {
        Navigator.of(context).pop(granted);
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(false);
      }
    }
  }
}

/// 网络状态提示对话框
class NetworkStatusDialog extends StatelessWidget {
  const NetworkStatusDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return AlertDialog(
      title: Row(
        children: [
          const Icon(Icons.signal_wifi_off, color: Colors.red),
          const SizedBox(width: 8),
          Text(l10n.networkConnectionProblem),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(NetworkStatusService.instance.getNetworkStatusDescription(l10n)),
          const SizedBox(height: 16),
          Text(
            l10n.troubleshootingSuggestions,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          ...NetworkStatusService.instance.getNetworkTroubleshootingTips(l10n)
              .map((tip) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text('• $tip'),
                  )),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text(l10n.cancel),
        ),
        ElevatedButton(
          onPressed: () async {
            final isConnected = await NetworkStatusService.instance.checkNetworkStatus();
            if (context.mounted) {
              Navigator.of(context).pop(isConnected);
            }
          },
          child: Text(l10n.retry),
        ),
      ],
    );
  }
}

/// 网络权限工具类
class NetworkPermissionHelper {
  /// 检查并请求网络权限
  static Future<bool> checkAndRequestPermission(BuildContext context) async {
    // 先检查网络连接状态
    final isConnected = await NetworkStatusService.instance.checkNetworkStatus();
    if (!isConnected) {
      if (context.mounted) {
        final retry = await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => const NetworkStatusDialog(),
        );
        return retry ?? false;
      }
      return false;
    }

    // 检查网络权限
    if (!NetworkPermissionService.instance.hasNetworkPermission) {
      if (context.mounted) {
        final granted = await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => const NetworkPermissionDialog(),
        );
        return granted ?? false;
      }
      return false;
    }

    return true;
  }

  /// 显示网络错误提示
  static void showNetworkError(BuildContext context, String message) {
    final l10n = AppLocalizations.of(context)!;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        action: SnackBarAction(
          label: l10n.settings,
          textColor: Colors.white,
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => const NetworkPermissionDialog(),
            );
          },
        ),
      ),
    );
  }
}