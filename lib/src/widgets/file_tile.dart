import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path/path.dart' as p;
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

import '../services/document_scanner.dart';
import '../state/app_state.dart';
import '../screens/pdf_viewer_screen.dart';
import '../screens/office_document_viewer_screen.dart';

class FileTile extends StatelessWidget {
  final DocumentFile file;
  const FileTile({super.key, required this.file});

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(2)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
  }

  bool _isSupportedOfficeDocument(String filePath) {
    final extension = filePath.toLowerCase();
    return extension.endsWith('.docx') ||
           extension.endsWith('.xlsx') ||
           extension.endsWith('.pptx') ||
           extension.endsWith('.doc') ||
           extension.endsWith('.xls') ||
           extension.endsWith('.ppt') ||
           extension.endsWith('.txt') ||
           extension.endsWith('.rtf') ||
           extension.endsWith('.jpg') ||
           extension.endsWith('.jpeg') ||
           extension.endsWith('.png') ||
           extension.endsWith('.gif') ||
           extension.endsWith('.bmp') ||
           extension.endsWith('.tiff') ||
           extension.endsWith('.webp');
  }

  @override
  Widget build(BuildContext context) {
    final color = Theme.of(context).colorScheme;
    final isFav =
        context.select<AppState, bool>((s) => s.favorites.isFavorite(file.path));

    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.primary.withValues(alpha: 0.12),
          child: Icon(Icons.insert_drive_file, color: color.primary),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                file.displayName,
                maxLines: 2, // Allow for slightly longer names
                overflow: TextOverflow.ellipsis,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ),
            const SizedBox(width: 8),
            SizedBox(
              width: 72, // Adjusted width for the size column
              child: Text(
                _formatBytes(file.sizeBytes),
                textAlign: TextAlign.end,
                style: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.copyWith(color: color.onSurfaceVariant),
              ),
            ),
            IconButton(
              tooltip: isFav ? '取消收藏' : '收藏',
              icon: Icon(isFav ? Icons.star : Icons.star_border),
              onPressed: () async {
                final appState = context.read<AppState>();
                await appState.favorites.toggle(file.path);
                // Trigger UI refresh after toggling favorite
                appState.notifyListeners();
              },
            ),
            PopupMenuButton<String>(
              onSelected: (value) => _onMenu(context, value, file),
              itemBuilder: (context) => const [
                PopupMenuItem(value: 'open', child: Text('打开')),
                PopupMenuItem(value: 'rename', child: Text('改名')),
                PopupMenuItem(value: 'share', child: Text('分享')),
                PopupMenuItem(value: 'delete', child: Text('删除')),
              ],
            ),
          ],
        ),
        onTap: () => _onMenu(context, 'open', file),
      ),
    );
  }

  Future<void> _onMenu(BuildContext context, String action, DocumentFile f) async {
    switch (action) {
      case 'open':
        // Check if it's a PDF file and open in PDF viewer
        if (f.path.toLowerCase().endsWith('.pdf')) {
          if (context.mounted) {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => PdfViewerScreen(
                  filePath: f.path,
                  fileName: f.displayName,
                ),
              ),
            );
            context.read<AppState>().recents.add(f.path);
          }
        } else if (Platform.isIOS && _isSupportedOfficeDocument(f.path)) {
          // On iOS, use WKWebView to display Office documents and other supported files
          if (kDebugMode) {
            print('[FileTile] Opening Office document on iOS: ${f.path}');
            print('[FileTile] File name: ${f.displayName}');
            print('[FileTile] File type supported: ${_isSupportedOfficeDocument(f.path)}');
          }

          // Verify file exists before navigation. If not, only proceed if we have external bytes.
          bool exists = false;
          try {
            exists = await File(f.path).exists();
            if (kDebugMode) {
              print('[FileTile] File exists check: $exists');
            }
            if (exists) {
              try {
                final stat = await File(f.path).stat();
                if (kDebugMode) {
                  print('[FileTile] File size: ${stat.size} bytes');
                }
              } catch (e) {
                if (kDebugMode) {
                  print('[FileTile] Error getting file stats: $e');
                }
              }
            }
          } catch (e) {
            if (kDebugMode) {
              print('[FileTile] Error checking file existence: $e');
            }
          }

          if (!exists && (f.externalBytes == null || f.externalBytes!.isEmpty)) {
            // No local access and no external bytes to fall back to
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('无法访问该文件。请在“已授权目录”中重新授权或重新选择文件。')),
              );
            }
            return;
          }

          if (context.mounted) {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (_) => OfficeDocumentViewerScreen(
                  documentFile: f,
                ),
              ),
            );
            // Important: Do NOT add to recents here. OfficeDocumentViewerScreen
            // will add the actual path it loaded (temp copy for external files).
          }
        } else {
          try {
            // Open other file types with external apps (Android and other platforms)
            final result = await OpenFilex.open(f.path);
            if (result.type != ResultType.done) {
              if (context.mounted) {
                _showInstallOfficeDialog(context);
              }
            } else {
              if (context.mounted) {
                context.read<AppState>().recents.add(f.path);
              }
            }
          } catch (e) {
            if (context.mounted) {
              _showInstallOfficeDialog(context);
            }
          }
        }
        break;
      case 'rename':
        await _rename(context, f);
        break;
      case 'share':
        await Share.shareXFiles([XFile(f.path)], text: f.displayName);
        break;
      case 'delete':
        await _delete(context, f);
        break;
    }
  }

  Future<void> _showInstallOfficeDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('需要安装App来打开Office文件'),
          content: const Text('需要安装一个应用来打开这个文件。是否从应用商店安装？'),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            FilledButton(
              child: const Text('安装'),
              onPressed: () {
                _launchStore();
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> _launchStore() async {
    final wpsPlayStoreUrl = Uri.parse('https://play.google.com/store/apps/details?id=cn.wps.moffice_eng');
    final wpsMarketUrl = Uri.parse('market://details?id=cn.wps.moffice_eng');
    final wpsTencentAppStoreUrl = Uri.parse('https://sj.qq.com/appdetail/cn.wps.moffice_eng');

    if (await canLaunchUrl(wpsPlayStoreUrl)) {
      await launchUrl(wpsPlayStoreUrl);
    } else if (await canLaunchUrl(wpsMarketUrl)) {
      await launchUrl(wpsMarketUrl);
    } else {
      await launchUrl(wpsTencentAppStoreUrl, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _rename(BuildContext context, DocumentFile f) async {
    final controller = TextEditingController(text: f.displayName);
    final newName = await showDialog<String>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('改名'),
          content: TextField(controller: controller, autofocus: true),
          actions: [
            TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消')),
            FilledButton(
                onPressed: () => Navigator.pop(context, controller.text),
                child: const Text('确定')),
          ],
        );
      },
    );
    if (newName == null || newName.trim().isEmpty) return;
    final dir = p.dirname(f.path);
    final newPath = p.join(dir, newName);
    final fileObj = File(f.path);
    try {
      await fileObj.rename(newPath);
      if (context.mounted) {
        ScaffoldMessenger.of(context)
            .showSnackBar(const SnackBar(content: Text('已改名')));
        await context.read<AppState>().refreshScanIncludingAppDirectory();
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('改名失败: $e')));
      }
    }
  }

  Future<void> _delete(BuildContext context, DocumentFile f) async {
    final ok = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除'),
        content: Text('确认删除文件：\n${f.displayName}?'),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('取消')),
          FilledButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('删除')),
        ],
      ),
    );
    if (ok != true) return;
    try {
      final fileObj = File(f.path);
      if (await fileObj.exists()) {
        await fileObj.delete();
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context)
            .showSnackBar(const SnackBar(content: Text('已删除')));
        await context.read<AppState>().refreshScanIncludingAppDirectory();
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('删除失败: $e')));
      }
    }
  }
}
