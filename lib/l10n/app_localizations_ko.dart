// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Korean (`ko`).
class AppLocalizationsKo extends AppLocalizations {
  AppLocalizationsKo([String locale = 'ko']) : super(locale);

  @override
  String get appName => '모든 PDF 편집기';

  @override
  String get documents => '문서';

  @override
  String get tools => 'Tools';

  @override
  String get settings => '설정';

  @override
  String get home => 'Home';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get files => 'Files';

  @override
  String get photoToPdf => '사진을 PDF로';

  @override
  String get imageToPdf => '이미지를 PDF로';

  @override
  String get addImages => 'Add Images';

  @override
  String addImagesFromGallery(int count) {
    return '갤러리에서 $count개의 이미지 추가';
  }

  @override
  String addImagesFromFiles(int count) {
    return 'Add $count images from files';
  }

  @override
  String get convert => '변환';

  @override
  String get undo => '실행 취소';

  @override
  String get discardChangesTitle => '변경사항을 취소하시겠습니까?';

  @override
  String get discardChangesContent =>
      '저장되지 않은 변경사항이 있습니다. 지금 종료하면 변경사항이 손실됩니다.';

  @override
  String get keepEditing => '계속 편집';

  @override
  String get discard => '취소';

  @override
  String get takePicture => '사진 찍기';

  @override
  String get reorderImage => '이미지 순서 변경';

  @override
  String get cropImage => '이미지 자르기';

  @override
  String get deleteImage => '이미지 삭제';

  @override
  String get confirmDeleteImage => '이 이미지를 삭제하시겠습니까?';

  @override
  String get cancel => '취소';

  @override
  String get delete => '삭제';

  @override
  String confirmUndo(String action) {
    return '실행 취소: $action. 마지막 작업을 되돌립니다. 계속하시겠습니까?';
  }

  @override
  String get pdfSavedSuccessfully => 'PDF가 성공적으로 저장되었습니다';

  @override
  String get pdfSaveFailed => 'PDF 저장 실패';

  @override
  String get saveAs => '다른 이름으로 저장';

  @override
  String get fileName => '파일 이름';

  @override
  String get save => '저장';

  @override
  String get scanPrefix => '스캔';

  @override
  String get addMoreImages => '더 많은 이미지 추가';

  @override
  String get takePhoto => '사진 찍기';

  @override
  String get importFromAlbum => '앨범에서 가져오기';

  @override
  String get importFromOtherApps => 'Import from Other Apps';

  @override
  String get aiProcessing => 'AI Processing';

  @override
  String get lowResolution => 'Low Resolution';

  @override
  String imagePreviewTitle(int currentPage, int totalPages) {
    return 'Image $currentPage of $totalPages';
  }

  @override
  String get confirmExit => 'Confirm Exit';

  @override
  String get discardCropChangesContent =>
      'You haven\'t saved. Are you sure you want to discard the manually adjusted coordinates?';

  @override
  String get continueAdjusting => 'Cancel';

  @override
  String get discardChangesConfirmation => 'Confirm';

  @override
  String get restoreDefault => 'Restore Default';

  @override
  String get confirmRestoreDefaultContent =>
      'This action will discard your manually adjusted coordinates. Do you want to discard your recent adjustments?';

  @override
  String get imageNotLoadedError =>
      'Image not loaded yet, please try again later.';

  @override
  String get imageProcessingFailedError =>
      'Image processing failed, please try again.';

  @override
  String get dragCornersHint =>
      'Drag the blue dots to adjust the four corners of the crop area.';

  @override
  String get splashTitle => '사진PDF변환 매직툴';

  @override
  String get splashSubtitle => '회의 사진 자동 정리, 원터치로 자르기와 PDF 병합';

  @override
  String get splashOriginalPhoto => '원본 사진';

  @override
  String get splashGeneratedPdf => '자동 생성 PDF';

  @override
  String get splashWorkflow => '앱 시작 중, 잠시만 기다려주세요...';

  @override
  String get recent => '최근';

  @override
  String get favorite => '즐겨찾기';

  @override
  String loadedFiles(int count) {
    return '$count개의 파일을 스캔했습니다';
  }

  @override
  String get cannotLoadDirectoryFiles => '디렉토리 파일을 로드할 수 없습니다';

  @override
  String get cannotSelectFiles => '파일을 선택할 수 없습니다. 권한을 확인하세요';

  @override
  String get directorySelectionOnlyMobile => '디렉토리 선택은 iOS/Android에서만 가능합니다';

  @override
  String get selectingDirectory => '디렉토리 스캔 중...';

  @override
  String get cannotSelectDirectory => '디렉토리를 선택할 수 없습니다. 권한을 확인하세요';

  @override
  String get authorizeFolder => '폴더 승인';

  @override
  String get sort => '정렬';

  @override
  String get nameSort => '이름';

  @override
  String get lastModified => '최종 수정일';

  @override
  String get sizeSort => '크기';

  @override
  String get descending => '내림차순';

  @override
  String get apply => '적용';

  @override
  String get originalPhotosFolder => 'Original Photos';

  @override
  String get searchInPdf => 'Search in PDF';

  @override
  String get searchText => 'Search text';

  @override
  String get search => 'Search';

  @override
  String get unsavedChanges => 'Unsaved Changes';

  @override
  String get unsavedChangesMessage =>
      'You have unsaved changes. Are you sure you want to exit?';

  @override
  String get discardChanges => 'Discard Changes';

  @override
  String get rotate => 'Rotate';

  @override
  String get edit => 'Edit';

  @override
  String get thumbnails => 'Thumbnails';

  @override
  String get pdfSaved => 'PDF saved successfully';

  @override
  String selectAuthorizedDirectorySubtitle(String appName, String deviceType) {
    return 'iPhone/iPad 파일 디렉토리 접근 승인';
  }

  @override
  String get shareAppSubtitle => 'PDF 매직툴을 추천드립니다:';

  @override
  String get shareAppSettingsSubtitle => 'Recommend this app to your friends';

  @override
  String get weLikeYouToo => '애정 주셔서 감사합니다!';

  @override
  String get thankYouForFeedback => '만점 평가가 개선의 원동력입니다!';

  @override
  String get theBestWeCanGet => '멋져요, 만점 평가!';

  @override
  String get maybeLater => '나중에';

  @override
  String get rateNow => '지금 스토어에서 평가하기';

  @override
  String get languageSettings => 'UI 언어';

  @override
  String get selectAuthorizedDirectory => '승인된 디렉토리 관리';

  @override
  String get shareApp => '공유';

  @override
  String get rateApp => '평가';

  @override
  String get rateAppSubtitle => 'App Store에서 평가해주세요';

  @override
  String get aboutApp => '정보';

  @override
  String get aboutAppSubtitle => '앱 버전, 정책 등';

  @override
  String get systemLanguage => '시스템 언어';

  @override
  String currentLanguage(String languageName, Object language) {
    return '현재: $language';
  }

  @override
  String get selectLanguage => '언어 선택';

  @override
  String defaultAppName(int number) {
    return '앱 $number';
  }

  @override
  String get cannotAddDirectory => '디렉토리 인증에 실패했습니다. 다시 시도해주세요';

  @override
  String get directoryRemoved => '디렉토리 인증이 제거되었습니다';

  @override
  String get cancelAuthorization => '승인 취소';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return '$appName의 $directoryName에 대한 접근 승인을 정말로 취소하시겠습니까? 취소 후에는 이 디렉토리의 파일이 더 이상 표시되지 않습니다.';
  }

  @override
  String get noKeepIt => '아니요, 유지합니다';

  @override
  String get yesRemoveFolder => '예, 이 폴더는 더 이상 필요하지 않습니다';

  @override
  String filesCount(int count) {
    return '$count개 파일';
  }

  @override
  String directorySize(String size) {
    return '크기: $size';
  }

  @override
  String get editName => '이름 편집';

  @override
  String get removeDirectory => '제거';

  @override
  String get noAuthorizedDirectories => '승인된 디렉토리가 없습니다';

  @override
  String get noAuthorizedDirectoriesSubtitle => '해당 위치의 파일에 접근하려면 디렉토리를 추가하세요';

  @override
  String get addDirectory => '디렉토리 추가';

  @override
  String get authorizedDirectories => '승인된 디렉토리 관리';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appName은(는) 다음 디렉토리에 접근할 수 있습니다:';
  }

  @override
  String get nameForNewFolder => '나중에 쉽게 식별할 수 있도록 방금 선택한 앱 폴더에 이름을 지정해주세요';

  @override
  String get enterCustomName => '사용자 정의 이름 입력';

  @override
  String get customName => '사용자 정의 이름';

  @override
  String get version => '버전';

  @override
  String get appVersion => '1.0.0';

  @override
  String get appDescription => '스마트 문서 처리. AI 자동 자르기와 보정으로 전문성 구현.';

  @override
  String get privacyPolicy => '개인정보 처리방침';

  @override
  String get termsOfService => '서비스 약관';

  @override
  String get copyright => '© 2025';

  @override
  String get noFavoriteFiles => '아직 즐겨찾기한 파일이 없습니다';

  @override
  String get noFavoriteFilesHint =>
      '문서 페이지의 파일 옆에 있는 ☆ 별 아이콘을 탭하여 파일을 즐겨찾기에 추가할 수 있습니다';

  @override
  String get fileNotFound => '파일을 찾을 수 없음';

  @override
  String get noRecentFiles => '이 앱에서 열었던 PDF/Word/Excel/PPT 파일이 여기에 표시됩니다';

  @override
  String noFilesFoundForType(int count, String fileType) {
    return '$fileType 파일 $count개를 찾았습니다';
  }

  @override
  String get selectFolder => '폴더 승인';

  @override
  String get selectFolderSubtitle =>
      '이 앱이 PDF/Word/Excel/PPT 파일 디렉토리를 찾을 수 있도록 승인';

  @override
  String get photoToPdfSubtitle => '프레젠테이션 슬라이드 또는 노트를 하나의 PDF로 스캔';

  @override
  String get mergeImagesToPdf => '이미지를 PDF로 병합';

  @override
  String get mergeImagesToPdfSubtitle => '앨범 사진을 PDF로 병합';

  @override
  String get fabTutorial => '여기를 클릭하여 카메라로 문서를 스캔하거나 갤러리의 이미지를 PDF로 병합하십시오.';

  @override
  String get iGotIt => '알았어요';

  @override
  String get signatures => '서명';

  @override
  String get manageSignatures => '손글씨 서명 관리';

  @override
  String get manageSignaturesHint =>
      '여기서 손글씨 서명을 관리합니다. +버튼을 눌러 서명을 추가하세요. 그러면 PDF 편집 시 이 서명들을 사용할 수 있습니다.';

  @override
  String get addSignature => '서명 추가';

  @override
  String get deleteSignature => '서명 삭제';

  @override
  String get confirmDeleteSignature => '이 서명을 삭제하시겠습니까?';

  @override
  String get createSignature => '서명 생성';

  @override
  String get saveSignature => '서명 저장';

  @override
  String get strokeWidth => '획 두께';

  @override
  String get color => '색상';

  @override
  String get eraser => '지우개';

  @override
  String get thin => '얇게';

  @override
  String get medium => '중간';

  @override
  String get thick => '두껍게';

  @override
  String get signatureCreated => '서명이 생성되었습니다';

  @override
  String get editSignature => 'Edit Signature';

  @override
  String get signatureUpdated => 'Signature updated successfully';

  @override
  String get signatureDeleted => '서명이 삭제되었습니다';

  @override
  String get noSignatures => '서명이 없습니다';

  @override
  String get newSignature => '새 서명';

  @override
  String get signatureUsageHint => 'PDF 편집 시 이 서명들을 사용할 수 있습니다';

  @override
  String get scanningFiles => '기기의 PDF/Word/Excel/PPT 파일을 스캔 중';

  @override
  String pageRotated(int page, int degrees) {
    return '페이지 $page를 $degrees° 회전했습니다';
  }

  @override
  String get rotationFailed => '회전 실패';

  @override
  String get closeSearch => '검색 닫기';

  @override
  String get previous => '이전';

  @override
  String get next => '다음';

  @override
  String get cancelSignatureEdit => '서명 편집 취소';

  @override
  String get back => '뒤로';

  @override
  String get editMode => '편집 모드';

  @override
  String get highlightText => '텍스트 강조';

  @override
  String get draw => '그리기';

  @override
  String get signature => '서명';

  @override
  String get highlightColor => '강조 색상';

  @override
  String get brushColor => '브러시 색상';

  @override
  String get thickness => '두께';

  @override
  String get confirmPlacement => '배치 확인';

  @override
  String get dragToAdjustHint => '드래그하여 조정';

  @override
  String get selectSignature => '서명 선택';

  @override
  String get noSignaturesPleaseCreate => '서명이 없습니다, 하나 만들어 주세요';

  @override
  String pageIndicator(int current, int total) {
    return '페이지 $current/$total';
  }

  @override
  String get androidDirectoryManagementSubtitle => '액세스 가능한 폴더 관리 및 추가';

  @override
  String get loadingDocument => '문서 로딩 중...';

  @override
  String get cannotOpenFile => '파일을 열 수 없습니다';

  @override
  String get galleryAccessError => '선택한 이미지에 액세스할 수 없습니다';

  @override
  String get permissionDenied => '사진 액세스 권한이 거부되었습니다';

  @override
  String get invalidImageSelected => '유효하지 않은 이미지가 선택되었습니다';

  @override
  String get gallerySelectionFailed => '갤러리에서 이미지 선택에 실패했습니다';

  @override
  String get unexpectedError => '예상치 못한 오류가 발생했습니다';

  @override
  String get importFailed => '가져오기 실패';

  @override
  String get importResults => '가져오기 결과';

  @override
  String allImportsFailed(int count) {
    return '선택한 $count개 이미지 가져오기가 모두 실패했습니다';
  }

  @override
  String partialImportSuccess(int successCount, int failCount) {
    return '$successCount개 이미지 가져오기 성공, $failCount개 실패';
  }

  @override
  String successfullyImported(int count) {
    return '✅ 가져오기 성공: $count개 이미지';
  }

  @override
  String failedToImport(int count) {
    return '❌ 가져오기 실패: $count개 이미지';
  }

  @override
  String get tryAgainOrUseCamera => '다시 시도하거나 카메라를 사용해보세요';

  @override
  String get checkPermissionsInSettings => '설정에서 앱 권한을 확인해주세요';

  @override
  String get trySelectingDifferentImages => '다른 이미지를 선택해보세요';

  @override
  String get selectedImagesCorruptedOrInaccessible =>
      '선택한 이미지가 손상되었거나 접근할 수 없습니다. 다른 이미지를 선택해보세요.';

  @override
  String get failedFiles => '실패한 파일:';

  @override
  String andXMore(int count) {
    return '... 및 $count개 더';
  }

  @override
  String get tryAgain => '다시 시도';

  @override
  String get details => '세부사항';

  @override
  String get ok => '확인';

  @override
  String get galleryLoadFailed => '갤러리 로딩 실패';

  @override
  String get fileSelectionFailed => '파일 선택에 실패했습니다';

  @override
  String get storagePermissionDenied => '저장소 권한이 거부되었습니다';

  @override
  String get unsupportedFileType => '지원되지 않는 파일 형식이 선택되었습니다';

  @override
  String get playSlideshow => 'Play Slideshow';

  @override
  String get slideshowMode => 'Slideshow Mode';

  @override
  String get exitSlideshow => 'Exit Slideshow';

  @override
  String get searchTitle => '검색';

  @override
  String get searchHint => '파일 이름 입력';

  @override
  String get noResults => '결과 없음';

  @override
  String iosPermissionMessage(String deviceType) {
    return 'iPhone 권한 제한으로 인해 PDF, Word, Excel 및 PPT 파일이 포함된 폴더에 대한 접근을 먼저 승인해야 합니다.';
  }

  @override
  String get waitForDocLoad => 'PDF를 준비하기 전에 문서가 완전히 로드될 때까지 기다리십시오';

  @override
  String get generatingPdf => 'PDF 생성 중...';

  @override
  String get pdfGenerationFailed => 'PDF 준비에 실패했습니다. 다시 시도하십시오.';

  @override
  String pdfConversionFailed(String error) {
    return 'PDF 변환 실패: $error';
  }

  @override
  String get conversionSuccess => '변환 성공';

  @override
  String pdfSavedMessage(String fileName) {
    return 'PDF 파일 $fileName(으)로 저장되었습니다. 지금 열어 보시겠습니까?';
  }

  @override
  String get stayOnPage => '페이지에 머물기';

  @override
  String get openPdf => 'PDF 열기';

  @override
  String get generatePdfVersion => 'PDF 버전 생성';

  @override
  String get generatePdfToEdit => '편집하거나 회전하려면 PDF 버전을 생성해야 합니다. 계속하시겠습니까?';

  @override
  String get continueAction => '계속';

  @override
  String get openingCachedPdf => '캐시된 PDF 여는 중...';

  @override
  String get saveAsPdf => 'PDF로 저장';

  @override
  String get caseSensitive => '대소문자 구분';

  @override
  String notFound(String query) {
    return '\"$query\"을(를) 찾을 수 없습니다.';
  }

  @override
  String get previousMatch => '이전';

  @override
  String get nextMatch => '다음';

  @override
  String get webViewOnlyIos => 'WebView 문서 보기는 iOS에서만 지원됩니다.';

  @override
  String webViewFailed(String error) {
    return 'WebView를 로드하지 못했습니다: $error';
  }

  @override
  String get officeTimeout =>
      'Office 문서 로드 시간이 초과되었습니다. 파일이 너무 크거나 지원되지 않는 형식일 수 있습니다.';

  @override
  String get addSlideshowButton => '슬라이드쇼 버튼 추가';

  @override
  String get threshold400px => '임계값: 400px';

  @override
  String get confirmClear => '지우기 확인';

  @override
  String get confirmClearSignature => '이렇게 하면 전체 서명 이미지가 지워집니다. 지우시겠습니까?';

  @override
  String get clear => '지우기';

  @override
  String iosNetworkPermissionGuide(String appName) {
    return 'iPhone 설정에서 \"$appName\"을(를) 찾아 네트워크 액세스가 허용되었는지 확인하십시오.\n경로: 설정 > 개인 정보 보호 및 보안 > 로컬 네트워크 > $appName';
  }

  @override
  String get checkNetworkConnection => '네트워크 연결을 확인하십시오';

  @override
  String get networkPermission => '네트워크 권한';

  @override
  String get networkPermissionRequest =>
      '이 앱은 PDF 문서의 이미지를 처리하고 향상시키기 위해 네트워크에 액세스해야 합니다.';

  @override
  String get featuresInclude => '기능은 다음과 같습니다.';

  @override
  String get imageClarityEnhancement => '이미지 선명도 향상';

  @override
  String get documentEdgeDetection => '문서 가장자리 감지';

  @override
  String get imageOptimization => '이미지 최적화';

  @override
  String get requestPermission => '권한 요청';

  @override
  String get networkConnectionProblem => '네트워크 연결 문제';

  @override
  String get networkStatusDescription =>
      '네트워크 연결을 사용할 수 없습니다. 네트워크 설정을 확인하십시오.';

  @override
  String get troubleshootingSuggestions => '문제 해결 제안:';

  @override
  String get networkTroubleshootingTips =>
      'Wi-Fi 또는 모바일 데이터가 활성화되어 있는지 확인하십시오.\n장치가 사용 가능한 네트워크에 연결되어 있는지 확인하십시오.\n비행기 모드가 켜져 있는지 확인하십시오.\n다른 앱이나 웹사이트에 액세스하여 네트워크 상태를 확인하십시오.\n네트워크 연결 또는 장치를 다시 시작하십시오.';

  @override
  String get retry => '재시도';

  @override
  String get unfavorite => 'Unfavorite';

  @override
  String get open => 'Open';

  @override
  String get rename => 'Rename';

  @override
  String get cannotAccessFile =>
      'Cannot access this file. Please reauthorize in \"Authorized Directories\" or reselect the file.';

  @override
  String get installOfficeAppTitle => 'App required to open Office files';

  @override
  String get installOfficeAppContent =>
      'An app is required to open this file. Install from the app store?';

  @override
  String get install => 'Install';

  @override
  String get renameFile => 'Rename';

  @override
  String get renamed => 'Renamed';

  @override
  String renameFailed(String error) {
    return 'Rename failed: $error';
  }

  @override
  String confirmDeleteFile(String fileName) {
    return 'Confirm delete file:\n$fileName?';
  }

  @override
  String get deleted => 'Deleted';

  @override
  String deleteFailed(String error) {
    return 'Delete failed: $error';
  }

  @override
  String get networkConnected => '네트워크가 연결되었습니다';

  @override
  String get networkDisconnected => '네트워크가 끊어졌습니다';

  @override
  String get networkAccessDenied => '네트워크 액세스가 거부되었습니다';

  @override
  String get networkOperationFailed => '네트워크 작업에 실패했습니다';

  @override
  String get connected => 'Connected';

  @override
  String get noNetwork => 'No network';

  @override
  String get thumbnailsOnlyIos => '썸네일은 iOS에서만 사용할 수 있습니다';

  @override
  String get waitDocumentComplete => '문서 로드 완료를 기다리는 중...';

  @override
  String get thumbnailDescription => '전체 문서의 썸네일\n빠른 탐색에 편리합니다';

  @override
  String get generatingThumbnail => '전체 문서 썸네일 생성 중...';

  @override
  String get analyzingDocument => '문서 구조와 크기 분석 중';

  @override
  String get thumbnailUnavailable => '썸네일을 일시적으로 표시할 수 없습니다';

  @override
  String get clickRefreshRetry => '우상단의 새로고침 버튼을 클릭하여 다시 시도하세요';
}
