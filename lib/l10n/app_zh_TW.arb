{"@@locale": "zh_TW", "appName": "全能PDF編輯器", "documents": "文檔", "tools": "工具", "settings": "設定", "home": "首頁", "camera": "相機", "gallery": "相簿", "files": "檔案", "photoToPdf": "拍照轉PDF", "imageToPdf": "圖片轉PDF", "photoToPdfSubtitle": "將簡報投影片或筆記掃描成一個 PDF", "addImages": "新增圖片", "addImagesFromGallery": "從相簿新增{count}張圖片", "@addImagesFromGallery": {"placeholders": {"count": {"type": "int"}}}, "addImagesFromFiles": "從檔案新增{count}張圖片", "@addImagesFromFiles": {"placeholders": {"count": {"type": "int"}}}, "convert": "轉換", "undo": "復原", "discardChangesTitle": "放棄變更？", "discardChangesContent": "您有未儲存的變更。您確定要退出嗎？", "keepEditing": "繼續編輯", "discard": "放棄", "takePicture": "拍攝照片", "reorderImage": "調整圖片順序", "cropImage": "裁剪圖片", "deleteImage": "刪除圖片", "confirmDeleteImage": "您確定要刪除這張圖片嗎？", "cancel": "取消", "delete": "刪除", "confirmUndo": "您確定要復原「{action}」操作嗎？", "@confirmUndo": {"placeholders": {"action": {"type": "String"}}}, "pdfSavedSuccessfully": "PDF儲存成功", "pdfSaveFailed": "PDF儲存失敗", "saveAs": "另存新檔", "fileName": "檔案名稱", "save": "儲存", "scanPrefix": "掃描", "addMoreImages": "新增更多圖片", "takePhoto": "拍照", "importFromAlbum": "從相簿匯入", "importFromOtherApps": "從其他應用程式匯入", "aiProcessing": "AI處理中", "lowResolution": "解析度太低", "imagePreviewTitle": "第 {currentPage} 張 / 共 {totalPages} 張", "@imagePreviewTitle": {"description": "圖片預覽頁的標題，顯示目前圖片和總數", "placeholders": {"currentPage": {"type": "int"}, "totalPages": {"type": "int"}}}, "confirmExit": "確認退出", "discardCropChangesContent": "您尚未儲存。您確定要放棄手動調整的座標嗎？", "continueAdjusting": "手滑了，我要繼續調整", "discardChangesConfirmation": "對，我不要了", "restoreDefault": "恢復預設", "confirmRestoreDefaultContent": "此操作會捨棄您剛才手動調整的座標。是否要放棄剛才的調整？", "imageNotLoadedError": "圖片尚未載入完成，請稍候再試。", "imageProcessingFailedError": "圖片處理失敗，请重试。", "dragCornersHint": "拖曳藍色圓點調整裁剪區域的四個角。", "splashTitle": "拍照轉PDF神器", "splashSubtitle": "自動整理會議照片，一鍵裁剪合併為PDF", "splashOriginalPhoto": "原始照片", "splashGeneratedPdf": "自動生成PDF", "splashWorkflow": "正在啟動應用，請稍候...", "recent": "最近", "favorite": "收藏", "shareAppSubtitle": "給你推薦一款PDF神器：", "weLikeYouToo": "謝謝你的喜愛！", "thankYouForFeedback": "您的滿分是我們改進的動力！", "theBestWeCanGet": "太棒了，滿分好評！", "maybeLater": "稍後再說", "rateNow": "現在去商店評分", "cannotAddDirectory": "授權目錄沒有成功，請重試", "directoryRemoved": "目錄授權已移除", "appDescription": "智慧文檔處理。AI自動裁剪矯正，彰顯專業。", "noRecentFiles": "您在本App中曾開啟過的PDF/Word/Excel/PPT檔案會顯示在這裡", "noFilesFoundForType": "找到 {count} 個{fileType}檔案", "@noFilesFoundForType": {"placeholders": {"count": {"type": "int"}, "fileType": {"type": "String"}}}, "selectFolder": "授權資料夾", "selectFolderSubtitle": "授權本App尋找PDF/Word/Excel/PPT檔案目錄", "signatures": "簽名", "manageSignatures": "管理您的手寫簽名", "manageSignaturesHint": "在這裡管理您的手寫簽名。點擊+號按鈕新增您的簽名。之後您可以在編輯PDF時使用這些簽名。", "addSignature": "新增簽名", "deleteSignature": "刪除簽名", "confirmDeleteSignature": "確定要刪除這個簽名嗎？", "createSignature": "建立簽名", "saveSignature": "儲存簽名", "strokeWidth": "畫筆粗細", "color": "顏色", "eraser": "橡皮擦", "thin": "細", "medium": "中", "thick": "粗", "signatureCreated": "簽名建立成功", "signatureDeleted": "簽名已刪除", "noSignatures": "暫無簽名", "newSignature": "新簽名", "signatureUsageHint": "您在編輯PDF時可以使用這些簽名", "scanningFiles": "正在掃描您手機上的PDF/Word/Excel/PPT檔案", "pageRotated": "已旋轉第{page}頁 ({degrees}°)", "@pageRotated": {"placeholders": {"page": {"type": "int"}, "degrees": {"type": "int"}}}, "rotationFailed": "旋轉失敗", "closeSearch": "關閉搜尋", "previous": "上一個", "next": "下一個", "cancelSignatureEdit": "取消簽名編輯", "back": "返回", "editMode": "編輯模式", "highlightText": "標記文字", "draw": "繪製", "signature": "簽名", "highlightColor": "高亮顏色:", "brushColor": "畫筆:", "thickness": "粗細:", "confirmPlacement": "確定放置", "dragToAdjustHint": "拖動簽名調整位置，點擊確認放置", "selectSignature": "選擇簽名", "noSignaturesPleaseCreate": "暫無簽名，請先建立簽名", "pageIndicator": "第{current}/{total}頁", "@pageIndicator": {"placeholders": {"current": {"type": "int"}, "total": {"type": "int"}}}, "androidDirectoryManagementSubtitle": "管理和添加可存取的資料夾", "loadingDocument": "正在載入文件...", "cannotOpenFile": "無法開啟文件", "galleryAccessError": "無法存取所選圖片", "permissionDenied": "存取相片權限被拒絕", "invalidImageSelected": "選擇了無效的圖片", "gallerySelectionFailed": "從相簿選擇圖片失敗", "unexpectedError": "發生未預期的錯誤", "importFailed": "匯入失敗", "importResults": "匯入結果", "allImportsFailed": "所有{count}張圖片匯入失敗", "@allImportsFailed": {"placeholders": {"count": {"type": "int"}}}, "partialImportSuccess": "{successCount}張圖片匯入成功，{failCount}張失敗", "@partialImportSuccess": {"placeholders": {"successCount": {"type": "int"}, "failCount": {"type": "int"}}}, "successfullyImported": "✅ 成功匯入：{count}張圖片", "@successfullyImported": {"placeholders": {"count": {"type": "int"}}}, "failedToImport": "❌ 匯入失敗：{count}張圖片", "@failedToImport": {"placeholders": {"count": {"type": "int"}}}, "tryAgainOrUseCamera": "請重試或使用相機", "checkPermissionsInSettings": "請在設定中檢查應用程式權限", "trySelectingDifferentImages": "請嘗試選擇其他圖片", "selectedImagesCorruptedOrInaccessible": "所選圖片可能已損壞或無法存取。請嘗試選擇其他圖片。", "failedFiles": "失敗的檔案：", "andXMore": "... 以及其他{count}個", "@andXMore": {"placeholders": {"count": {"type": "int"}}}, "tryAgain": "重試", "details": "詳情", "ok": "確定", "galleryLoadFailed": "相簿載入失敗", "fileSelectionFailed": "檔案選擇失敗", "storagePermissionDenied": "儲存權限被拒絕", "unsupportedFileType": "選擇了不支援的檔案類型", "searchTitle": "搜尋", "searchHint": "輸入檔案名稱", "noResults": "暫無結果", "waitForDocLoad": "請等待文檔完全加載後再準備PDF", "generatingPdf": "正在生成PDF...", "pdfGenerationFailed": "PDF準備失敗，請重試", "pdfConversionFailed": "PDF轉換失敗: {error}", "@pdfConversionFailed": {"placeholders": {"error": {"type": "String"}}}, "conversionSuccess": "轉換成功", "pdfSavedMessage": "已另存為PDF檔案 {fileName}，是否立即開啟查看？", "@pdfSavedMessage": {"placeholders": {"fileName": {"type": "String"}}}, "stayOnPage": "留在當前頁面", "openPdf": "開啟PDF", "generatePdfVersion": "生成PDF版本", "generatePdfToEdit": "需要生成PDF版本才能編輯或旋轉。是否繼續？", "continueAction": "繼續", "openingCachedPdf": "正在開啟快取的PDF...", "saveAsPdf": "另存為PDF", "caseSensitive": "區分大小寫", "notFound": "未找到 \"{query}\"", "@notFound": {"placeholders": {"query": {"type": "String"}}}, "previousMatch": "上一個", "nextMatch": "下一個", "webViewOnlyIos": "WebView文件檢視僅支援iOS", "webViewFailed": "WebView載入失敗: {error}", "@webViewFailed": {"placeholders": {"error": {"type": "String"}}}, "officeTimeout": "Office文件載入超時。這可能是由於檔案過大或格式不支援。", "addSlideshowButton": "新增投影片播放按鈕", "threshold400px": "閾值：400像素", "confirmClear": "確認清除", "confirmClearSignature": "這將整個清除您的簽名圖像。您確定要清除嗎？", "clear": "清除", "iosNetworkPermissionGuide": "請在iPhone設定中找到\"{appName}\",確保允許網路存取。\n設定路徑：設定 > 隱私權與安全性 > 本地網路 > {appName}", "@iosNetworkPermissionGuide": {"placeholders": {"appName": {"type": "String"}}}, "checkNetworkConnection": "請檢查您的網路連線", "networkPermission": "網路權限", "networkPermissionRequest": "此應用程式需要存取網路才能處理和增強PDF文件中的影像。", "featuresInclude": "功能包括：", "imageClarityEnhancement": "影像清晰度增強", "documentEdgeDetection": "文件邊緣偵測", "imageOptimization": "影像最佳化", "requestPermission": "要求權限", "networkConnectionProblem": "網路連線問題", "networkStatusDescription": "網路連線不可用，請檢查您的網路設定", "troubleshootingSuggestions": "疑難排解建議：", "networkTroubleshootingTips": "檢查Wi-Fi或行動數據是否已啟用\n確認裝置已連線到可用的網路\n檢查是否處於飛航模式\n嘗試存取其他應用程式或網站以確認網路狀態\n重新啟動網路連線或裝置", "retry": "重試", "networkAccessDenied": "網路訪問被拒絕", "networkOperationFailed": "網路操作失敗", "@networkOperationFailed": {"placeholders": {"error": {"type": "String"}}}, "connected": "已連線", "noNetwork": "無網路", "unfavorite": "取消收藏", "open": "開啟", "rename": "重新命名", "cannotAccessFile": "無法存取此檔案。請在「已授權目錄」中重新授權或重新選擇檔案。", "installOfficeAppTitle": "需要安裝App才能開啟Office檔案", "installOfficeAppContent": "需要安裝一個應用程式才能開啟此檔案。是否從應用程式商店安裝？", "install": "安裝", "renameFile": "重新命名", "renamed": "已重新命名", "renameFailed": "重新命名失敗：{error}", "@renameFailed": {"placeholders": {"error": {"type": "String"}}}, "confirmDeleteFile": "確認刪除檔案：\n{fileName}?", "@confirmDeleteFile": {"placeholders": {"fileName": {"type": "String"}}}, "deleted": "已刪除", "deleteFailed": "刪除失敗：{error}", "@deleteFailed": {"placeholders": {"error": {"type": "String"}}}, "networkConnected": "網路已連接", "networkDisconnected": "網路已斷開", "thumbnailsOnlyIos": "縮圖僅在 iOS 上可用", "waitDocumentComplete": "等待文件載入完成...", "thumbnailDescription": "完整文件的縮圖\n方便快速導航", "generatingThumbnail": "正在生成完整文件縮圖...", "analyzingDocument": "正在分析文件結構和尺寸", "thumbnailUnavailable": "縮圖暫時無法顯示", "clickRefreshRetry": "點擊右上角的重新整理按鈕重試"}