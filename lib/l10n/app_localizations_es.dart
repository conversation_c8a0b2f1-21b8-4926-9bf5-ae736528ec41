// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appName => 'Editor de PDF todo';

  @override
  String get documents => 'Documentos';

  @override
  String get tools => 'Tools';

  @override
  String get settings => 'Configuración';

  @override
  String get home => 'Home';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get files => 'Files';

  @override
  String get photoToPdf => 'Foto a PDF';

  @override
  String get imageToPdf => 'Imagen a PDF';

  @override
  String get addImages => 'Add Images';

  @override
  String addImagesFromGallery(int count) {
    return 'Agregar $count imágenes de la galería';
  }

  @override
  String addImagesFromFiles(int count) {
    return 'Add $count images from files';
  }

  @override
  String get convert => 'Convertir';

  @override
  String get undo => 'Deshacer';

  @override
  String get discardChangesTitle => '¿Descartar cambios?';

  @override
  String get discardChangesContent =>
      'Tienes cambios sin guardar. Si sales ahora, se perderán.';

  @override
  String get keepEditing => 'Seguir editando';

  @override
  String get discard => 'Descartar';

  @override
  String get takePicture => 'Tomar foto';

  @override
  String get reorderImage => 'Reordenar imágenes';

  @override
  String get cropImage => 'Recortar imagen';

  @override
  String get deleteImage => 'Eliminar imagen';

  @override
  String get confirmDeleteImage =>
      '¿Está seguro de que desea eliminar esta imagen?';

  @override
  String get cancel => 'Cancelar';

  @override
  String get delete => 'Eliminar';

  @override
  String confirmUndo(String action) {
    return 'Deshacer: $action. Esto revertirá su última acción. ¿Continuar?';
  }

  @override
  String get pdfSavedSuccessfully => 'PDF guardado exitosamente';

  @override
  String get pdfSaveFailed => 'Error al guardar PDF';

  @override
  String get saveAs => 'Guardar como';

  @override
  String get fileName => 'Nombre del archivo';

  @override
  String get save => 'Guardar';

  @override
  String get scanPrefix => 'escanear';

  @override
  String get addMoreImages => 'Agregar más imágenes';

  @override
  String get takePhoto => 'Tomar foto';

  @override
  String get importFromAlbum => 'Importar del álbum';

  @override
  String get importFromOtherApps => 'Import from Other Apps';

  @override
  String get aiProcessing => 'AI Processing';

  @override
  String get lowResolution => 'Low Resolution';

  @override
  String imagePreviewTitle(int currentPage, int totalPages) {
    return 'Image $currentPage of $totalPages';
  }

  @override
  String get confirmExit => 'Confirm Exit';

  @override
  String get discardCropChangesContent =>
      'You haven\'t saved. Are you sure you want to discard the manually adjusted coordinates?';

  @override
  String get continueAdjusting => 'Cancel';

  @override
  String get discardChangesConfirmation => 'Confirm';

  @override
  String get restoreDefault => 'Restore Default';

  @override
  String get confirmRestoreDefaultContent =>
      'This action will discard your manually adjusted coordinates. Do you want to discard your recent adjustments?';

  @override
  String get imageNotLoadedError =>
      'Image not loaded yet, please try again later.';

  @override
  String get imageProcessingFailedError =>
      'Image processing failed, please try again.';

  @override
  String get dragCornersHint =>
      'Drag the blue dots to adjust the four corners of the crop area.';

  @override
  String get splashTitle => 'Herramienta Mágica Foto a PDF';

  @override
  String get splashSubtitle =>
      'Organiza automáticamente fotos de reuniones, recorta y combina en PDF con un toque';

  @override
  String get splashOriginalPhoto => 'Foto Original';

  @override
  String get splashGeneratedPdf => 'PDF auto-generado';

  @override
  String get splashWorkflow => 'Iniciando app, por favor espere...';

  @override
  String get recent => 'Recientes';

  @override
  String get favorite => 'Favoritos';

  @override
  String loadedFiles(int count) {
    return '$count archivos escaneados';
  }

  @override
  String get cannotLoadDirectoryFiles =>
      'No se pueden cargar archivos del directorio';

  @override
  String get cannotSelectFiles =>
      'No se pueden seleccionar archivos, verifique los permisos';

  @override
  String get directorySelectionOnlyMobile =>
      'La selección de directorio solo está disponible en iOS/Android';

  @override
  String get selectingDirectory => 'Escaneando directorio...';

  @override
  String get cannotSelectDirectory =>
      'No se puede seleccionar directorio, verifique los permisos';

  @override
  String get authorizeFolder => 'Autorizar Carpeta';

  @override
  String get sort => 'Ordenar';

  @override
  String get nameSort => 'Nombre';

  @override
  String get lastModified => 'Última Modificación';

  @override
  String get sizeSort => 'Tamaño';

  @override
  String get descending => 'Descendente';

  @override
  String get apply => 'Aplicar';

  @override
  String get originalPhotosFolder => 'Original Photos';

  @override
  String get searchInPdf => 'Search in PDF';

  @override
  String get searchText => 'Search text';

  @override
  String get search => 'Search';

  @override
  String get unsavedChanges => 'Unsaved Changes';

  @override
  String get unsavedChangesMessage =>
      'You have unsaved changes. Are you sure you want to exit?';

  @override
  String get discardChanges => 'Discard Changes';

  @override
  String get rotate => 'Rotate';

  @override
  String get edit => 'Edit';

  @override
  String get thumbnails => 'Thumbnails';

  @override
  String get pdfSaved => 'PDF saved successfully';

  @override
  String selectAuthorizedDirectorySubtitle(String appName, String deviceType) {
    return 'Autorizar acceso a directorios de iPhone/iPad';
  }

  @override
  String get shareAppSubtitle => 'Te recomiendo una herramienta PDF mágica:';

  @override
  String get shareAppSettingsSubtitle => 'Recommend this app to your friends';

  @override
  String get weLikeYouToo => '¡Gracias por tu cariño!';

  @override
  String get thankYouForFeedback =>
      '¡Tu calificación perfecta es nuestra motivación para mejorar!';

  @override
  String get theBestWeCanGet =>
      '¡Fantástico, calificación perfecta de 5 estrellas!';

  @override
  String get maybeLater => 'Tal vez más tarde';

  @override
  String get rateNow => 'Calificar ahora en la tienda';

  @override
  String get languageSettings => 'Idioma UI';

  @override
  String get selectAuthorizedDirectory => 'Administrar Directorio Autorizado';

  @override
  String get shareApp => 'Compartir';

  @override
  String get rateApp => 'Calificar';

  @override
  String get rateAppSubtitle => 'Califícanos en la App Store';

  @override
  String get aboutApp => 'Acerca de';

  @override
  String get aboutAppSubtitle => 'Versión de la app, políticas y más';

  @override
  String get systemLanguage => 'Idioma del Sistema';

  @override
  String currentLanguage(String languageName, Object language) {
    return 'Actual: $language';
  }

  @override
  String get selectLanguage => 'Seleccionar Idioma';

  @override
  String defaultAppName(int number) {
    return 'App $number';
  }

  @override
  String get cannotAddDirectory =>
      'La autorización del directorio falló, inténtalo de nuevo';

  @override
  String get directoryRemoved => 'Autorización del directorio eliminada';

  @override
  String get cancelAuthorization => 'Cancelar autorización';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return '¿Realmente desea cancelar la autorización de acceso para $directoryName de $appName? Después de la cancelación, los archivos en este directorio ya no estarán visibles.';
  }

  @override
  String get noKeepIt => 'No, mantenerlo';

  @override
  String get yesRemoveFolder => 'Sí, ya no necesito esta carpeta';

  @override
  String filesCount(int count) {
    return '$count archivos';
  }

  @override
  String directorySize(String size) {
    return 'Tamaño: $size';
  }

  @override
  String get editName => 'Editar nombre';

  @override
  String get removeDirectory => 'Eliminar';

  @override
  String get noAuthorizedDirectories => 'Sin directorios autorizados';

  @override
  String get noAuthorizedDirectoriesSubtitle =>
      'Agrega directorios para acceder a archivos desde esas ubicaciones';

  @override
  String get addDirectory => 'Agregar Directorio';

  @override
  String get authorizedDirectories => 'Administrar Directorios Autorizados';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appName puede acceder a los siguientes directorios:';
  }

  @override
  String get nameForNewFolder =>
      'Para una fácil identificación posterior, por favor asigne un nombre a la carpeta de la aplicación que acaba de seleccionar';

  @override
  String get enterCustomName => 'Introducir nombre personalizado';

  @override
  String get customName => 'Nombre personalizado';

  @override
  String get version => 'Versión';

  @override
  String get appVersion => '1.0.0';

  @override
  String get appDescription =>
      'Procesamiento inteligente de documentos. Recorte y corrección automáticos con IA para resultados profesionales.';

  @override
  String get privacyPolicy => 'Política de Privacidad';

  @override
  String get termsOfService => 'Términos de Servicio';

  @override
  String get copyright => '© 2025';

  @override
  String get noFavoriteFiles => 'Aún no has marcado archivos como favoritos';

  @override
  String get noFavoriteFilesHint =>
      'Puedes marcar archivos como favoritos tocando el ícono ☆ estrella junto a los archivos en la página de documentos';

  @override
  String get fileNotFound => 'Archivo no encontrado';

  @override
  String get noRecentFiles =>
      'Los archivos PDF/Word/Excel/PPT que hayas abierto en esta app se mostrarán aquí';

  @override
  String noFilesFoundForType(int count, String fileType) {
    return 'Se encontraron $count archivos $fileType';
  }

  @override
  String get selectFolder => 'Autorizar Carpeta';

  @override
  String get selectFolderSubtitle =>
      'Autorizar a esta aplicación para encontrar directorios de archivos PDF/Word/Excel/PPT';

  @override
  String get photoToPdfSubtitle =>
      'Escanee diapositivas de presentaciones o notas en un solo PDF';

  @override
  String get mergeImagesToPdf => 'Combinar Imágenes en PDF';

  @override
  String get mergeImagesToPdfSubtitle => 'Combinar Fotos del Álbum en PDF';

  @override
  String get fabTutorial =>
      'Haga clic aquí para escanear documentos con su cámara o combinar imágenes de su galería en un PDF.';

  @override
  String get iGotIt => 'Lo tengo';

  @override
  String get signatures => 'Firmas';

  @override
  String get manageSignatures => 'Gestionar sus firmas manuscritas';

  @override
  String get manageSignaturesHint =>
      'Gestione sus firmas manuscritas aquí. Toque el botón + para agregar su firma. Luego podrá usar estas firmas al editar PDFs.';

  @override
  String get addSignature => 'Agregar firma';

  @override
  String get deleteSignature => 'Eliminar firma';

  @override
  String get confirmDeleteSignature =>
      '¿Está seguro de que desea eliminar esta firma?';

  @override
  String get createSignature => 'Crear firma';

  @override
  String get saveSignature => 'Guardar firma';

  @override
  String get strokeWidth => 'Grosor del trazo';

  @override
  String get color => 'Color';

  @override
  String get eraser => 'Borrador';

  @override
  String get thin => 'Delgado';

  @override
  String get medium => 'Medio';

  @override
  String get thick => 'Grueso';

  @override
  String get signatureCreated => 'Firma creada exitosamente';

  @override
  String get editSignature => 'Edit Signature';

  @override
  String get signatureUpdated => 'Signature updated successfully';

  @override
  String get signatureDeleted => 'Firma eliminada';

  @override
  String get noSignatures => 'Sin firmas';

  @override
  String get newSignature => 'Nueva firma';

  @override
  String get signatureUsageHint => 'Puede usar estas firmas al editar PDFs';

  @override
  String get scanningFiles =>
      'Buscando archivos PDF/Word/Excel/PPT en su dispositivo';

  @override
  String pageRotated(int page, int degrees) {
    return 'Página $page rotada $degrees°';
  }

  @override
  String get rotationFailed => 'Rotación fallida';

  @override
  String get closeSearch => 'Cerrar búsqueda';

  @override
  String get previous => 'Anterior';

  @override
  String get next => 'Siguiente';

  @override
  String get cancelSignatureEdit => 'Cancelar edición de firma';

  @override
  String get back => 'Atrás';

  @override
  String get editMode => 'Modo de edición';

  @override
  String get highlightText => 'Resaltar texto';

  @override
  String get draw => 'Dibujar';

  @override
  String get signature => 'Firma';

  @override
  String get highlightColor => 'Color de resaltado';

  @override
  String get brushColor => 'Color del pincel';

  @override
  String get thickness => 'Grosor';

  @override
  String get confirmPlacement => 'Confirmar colocación';

  @override
  String get dragToAdjustHint => 'Arrastra para ajustar';

  @override
  String get selectSignature => 'Seleccionar firma';

  @override
  String get noSignaturesPleaseCreate => 'Sin firmas, por favor crea una';

  @override
  String pageIndicator(int current, int total) {
    return 'Página $current/$total';
  }

  @override
  String get androidDirectoryManagementSubtitle =>
      'Gestionar y agregar carpetas accesibles';

  @override
  String get loadingDocument => 'Cargando documento...';

  @override
  String get cannotOpenFile => 'No se puede abrir el archivo';

  @override
  String get galleryAccessError =>
      'No se puede acceder a las imágenes seleccionadas';

  @override
  String get permissionDenied => 'Permiso denegado para acceder a las fotos';

  @override
  String get invalidImageSelected => 'Imagen inválida seleccionada';

  @override
  String get gallerySelectionFailed =>
      'Falla al seleccionar imágenes de la galería';

  @override
  String get unexpectedError => 'Ocurrió un error inesperado';

  @override
  String get importFailed => 'Importación fallida';

  @override
  String get importResults => 'Resultados de importación';

  @override
  String allImportsFailed(int count) {
    return 'Falló la importación de todas las $count imágenes seleccionadas';
  }

  @override
  String partialImportSuccess(int successCount, int failCount) {
    return '$successCount imágenes importadas exitosamente, $failCount fallaron';
  }

  @override
  String successfullyImported(int count) {
    return '✅ Importación exitosa: $count imágenes';
  }

  @override
  String failedToImport(int count) {
    return '❌ Falló la importación: $count imágenes';
  }

  @override
  String get tryAgainOrUseCamera =>
      'Por favor intenta de nuevo o usa la cámara';

  @override
  String get checkPermissionsInSettings =>
      'Por favor verifica los permisos de la app en Configuración';

  @override
  String get trySelectingDifferentImages =>
      'Por favor intenta seleccionar diferentes imágenes';

  @override
  String get selectedImagesCorruptedOrInaccessible =>
      'Las imágenes seleccionadas pueden estar corruptas o inaccesibles. Por favor intenta seleccionar diferentes imágenes.';

  @override
  String get failedFiles => 'Archivos fallidos:';

  @override
  String andXMore(int count) {
    return '... y $count más';
  }

  @override
  String get tryAgain => 'Intentar de nuevo';

  @override
  String get details => 'Detalles';

  @override
  String get ok => 'OK';

  @override
  String get galleryLoadFailed => 'Falla al cargar la galería';

  @override
  String get fileSelectionFailed => 'Falla en la selección de archivo';

  @override
  String get storagePermissionDenied => 'Permiso de almacenamiento denegado';

  @override
  String get unsupportedFileType => 'Tipo de archivo no soportado seleccionado';

  @override
  String get playSlideshow => 'Play Slideshow';

  @override
  String get slideshowMode => 'Slideshow Mode';

  @override
  String get exitSlideshow => 'Exit Slideshow';

  @override
  String get searchTitle => 'Buscar';

  @override
  String get searchHint => 'Introducir nombre de archivo';

  @override
  String get noResults => 'No hay resultados';

  @override
  String iosPermissionMessage(String deviceType) {
    return 'Debido a las restricciones de permisos del iPhone, necesitas autorizar primero el acceso a las carpetas que contienen tus archivos PDF, Word, Excel y PPT.';
  }

  @override
  String get waitForDocLoad =>
      'Por favor, espere a que el documento se cargue completamente antes de preparar el PDF';

  @override
  String get generatingPdf => 'Generando PDF...';

  @override
  String get pdfGenerationFailed =>
      'La preparación del PDF ha fallado, por favor, inténtelo de nuevo';

  @override
  String pdfConversionFailed(String error) {
    return 'La conversión a PDF ha fallado: $error';
  }

  @override
  String get conversionSuccess => 'Conversión exitosa';

  @override
  String pdfSavedMessage(String fileName) {
    return 'Guardado como archivo PDF $fileName, ¿abrir para ver ahora?';
  }

  @override
  String get stayOnPage => 'Permanecer en la página';

  @override
  String get openPdf => 'Abrir PDF';

  @override
  String get generatePdfVersion => 'Generar versión PDF';

  @override
  String get generatePdfToEdit =>
      'Es necesario generar una versión PDF para editar o girar. ¿Continuar?';

  @override
  String get continueAction => 'Continuar';

  @override
  String get openingCachedPdf => 'Abriendo PDF en caché...';

  @override
  String get saveAsPdf => 'Guardar como PDF';

  @override
  String get caseSensitive => 'Distinguir mayúsculas y minúsculas';

  @override
  String notFound(String query) {
    return 'No se encontró \"$query\"';
  }

  @override
  String get previousMatch => 'Anterior';

  @override
  String get nextMatch => 'Siguiente';

  @override
  String get webViewOnlyIos =>
      'La visualización de documentos WebView solo es compatible con iOS';

  @override
  String webViewFailed(String error) {
    return 'WebView no se pudo cargar: $error';
  }

  @override
  String get officeTimeout =>
      'Se agotó el tiempo de carga del documento de Office. Esto puede deberse a un archivo grande o a un formato no compatible.';

  @override
  String get addSlideshowButton =>
      'Añadir botón de presentación de diapositivas';

  @override
  String get threshold400px => 'Umbral: 400px';

  @override
  String get confirmClear => 'Confirmar borrado';

  @override
  String get confirmClearSignature =>
      'Esto borrará toda la imagen de su firma. ¿Está seguro de que desea borrarla?';

  @override
  String get clear => 'Borrar';

  @override
  String iosNetworkPermissionGuide(String appName) {
    return 'Por favor, busque \"$appName\" en los Ajustes de su iPhone y asegúrese de que el acceso a la red está permitido.\nRuta: Ajustes > Privacidad y Seguridad > Red local > $appName';
  }

  @override
  String get checkNetworkConnection =>
      'Por favor, compruebe su conexión de red';

  @override
  String get networkPermission => 'Permiso de red';

  @override
  String get networkPermissionRequest =>
      'Esta aplicación necesita acceder a la red para procesar y mejorar las imágenes de los documentos PDF.';

  @override
  String get featuresInclude => 'Las características incluyen:';

  @override
  String get imageClarityEnhancement => 'Mejora de la claridad de la imagen';

  @override
  String get documentEdgeDetection => 'Detección de bordes de documentos';

  @override
  String get imageOptimization => 'Optimización de la imagen';

  @override
  String get requestPermission => 'Solicitar permiso';

  @override
  String get networkConnectionProblem => 'Problema de conexión de red';

  @override
  String get networkStatusDescription =>
      'La conexión de red no está disponible, compruebe la configuración de red';

  @override
  String get troubleshootingSuggestions =>
      'Sugerencias para la solución de problemas:';

  @override
  String get networkTroubleshootingTips =>
      'Compruebe si el Wi-Fi o los datos móviles están activados\nConfirme que el dispositivo está conectado a una red utilizable\nCompruebe si el modo avión está activado\nIntente acceder a otras aplicaciones o sitios web para confirmar el estado de la red\nReinicie la conexión de red o el dispositivo';

  @override
  String get retry => 'Reintentar';

  @override
  String get unfavorite => 'Unfavorite';

  @override
  String get open => 'Open';

  @override
  String get rename => 'Rename';

  @override
  String get cannotAccessFile =>
      'Cannot access this file. Please reauthorize in \"Authorized Directories\" or reselect the file.';

  @override
  String get installOfficeAppTitle => 'App required to open Office files';

  @override
  String get installOfficeAppContent =>
      'An app is required to open this file. Install from the app store?';

  @override
  String get install => 'Install';

  @override
  String get renameFile => 'Rename';

  @override
  String get renamed => 'Renamed';

  @override
  String renameFailed(String error) {
    return 'Rename failed: $error';
  }

  @override
  String confirmDeleteFile(String fileName) {
    return 'Confirm delete file:\n$fileName?';
  }

  @override
  String get deleted => 'Deleted';

  @override
  String deleteFailed(String error) {
    return 'Delete failed: $error';
  }

  @override
  String get networkConnected => 'Red conectada';

  @override
  String get networkDisconnected => 'Red desconectada';

  @override
  String get networkAccessDenied => 'Acceso a la red denegado';

  @override
  String get networkOperationFailed => 'Operación de red fallida';

  @override
  String get connected => 'Connected';

  @override
  String get noNetwork => 'No network';

  @override
  String get thumbnailsOnlyIos =>
      'Las miniaturas solo están disponibles en iOS';

  @override
  String get waitDocumentComplete =>
      'Esperando a que se complete la carga del documento...';

  @override
  String get thumbnailDescription =>
      'Miniatura del documento completo\nConveniente para navegación rápida';

  @override
  String get generatingThumbnail =>
      'Generando miniatura del documento completo...';

  @override
  String get analyzingDocument =>
      'Analizando estructura y dimensiones del documento';

  @override
  String get thumbnailUnavailable =>
      'La miniatura no está disponible temporalmente';

  @override
  String get clickRefreshRetry =>
      'Haz clic en el botón de actualizar en la esquina superior derecha para reintentar';
}
