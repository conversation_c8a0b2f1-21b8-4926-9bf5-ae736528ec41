// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'All PDF Editor';

  @override
  String get documents => 'Documents';

  @override
  String get tools => 'Tools';

  @override
  String get settings => 'Settings';

  @override
  String get home => 'Home';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get files => 'Files';

  @override
  String get photoToPdf => 'Scan to PDF';

  @override
  String get imageToPdf => 'Image to PDF';

  @override
  String get addImages => 'Add Images';

  @override
  String addImagesFromGallery(int count) {
    return 'Add $count images from gallery';
  }

  @override
  String addImagesFromFiles(int count) {
    return 'Add $count images from files';
  }

  @override
  String get convert => 'Convert';

  @override
  String get undo => 'Undo';

  @override
  String get discardChangesTitle => 'Discard changes?';

  @override
  String get discardChangesContent =>
      'You have unsaved changes. Are you sure you want to exit?';

  @override
  String get keepEditing => 'Keep Editing';

  @override
  String get discard => 'Discard';

  @override
  String get takePicture => 'Take a picture';

  @override
  String get reorderImage => 'Reorder image';

  @override
  String get cropImage => 'Crop Image';

  @override
  String get deleteImage => 'Delete Image';

  @override
  String get confirmDeleteImage =>
      'Are you sure you want to delete this image?';

  @override
  String get cancel => 'Cancel';

  @override
  String get delete => 'Delete';

  @override
  String confirmUndo(String action) {
    return 'Are you sure you want to undo \"$action\"?';
  }

  @override
  String get pdfSavedSuccessfully => 'PDF saved successfully';

  @override
  String get pdfSaveFailed => 'Failed to save PDF';

  @override
  String get saveAs => 'Save As';

  @override
  String get fileName => 'File Name';

  @override
  String get save => 'Save';

  @override
  String get scanPrefix => 'Scan';

  @override
  String get addMoreImages => 'Add More Images';

  @override
  String get takePhoto => 'Take Photo';

  @override
  String get importFromAlbum => 'Import from Album';

  @override
  String get importFromOtherApps => 'Import from Other Apps';

  @override
  String get aiProcessing => 'AI Processing';

  @override
  String get lowResolution => 'Low Resolution';

  @override
  String imagePreviewTitle(int currentPage, int totalPages) {
    return 'Image $currentPage of $totalPages';
  }

  @override
  String get confirmExit => 'Confirm Exit';

  @override
  String get discardCropChangesContent =>
      'You haven\'t saved. Are you sure you want to discard the manually adjusted coordinates?';

  @override
  String get continueAdjusting => 'Cancel';

  @override
  String get discardChangesConfirmation => 'Confirm';

  @override
  String get restoreDefault => 'Restore Default';

  @override
  String get confirmRestoreDefaultContent =>
      'This action will discard your manually adjusted coordinates. Do you want to discard your recent adjustments?';

  @override
  String get imageNotLoadedError =>
      'Image not loaded yet, please try again later.';

  @override
  String get imageProcessingFailedError =>
      'Image processing failed, please try again.';

  @override
  String get dragCornersHint =>
      'Drag the blue dots to adjust the four corners of the crop area.';

  @override
  String get splashTitle => 'Photo to PDF Magic Tool';

  @override
  String get splashSubtitle =>
      'Auto organize meeting photos, crop and merge into PDF with one tap';

  @override
  String get splashOriginalPhoto => 'Original Photo';

  @override
  String get splashGeneratedPdf => 'Auto Generated PDF';

  @override
  String get splashWorkflow => 'App is starting, please wait...';

  @override
  String get recent => 'Recent';

  @override
  String get favorite => 'Favorites';

  @override
  String loadedFiles(int count) {
    return 'Loaded $count files';
  }

  @override
  String get cannotLoadDirectoryFiles => 'Cannot load directory files';

  @override
  String get cannotSelectFiles => 'Cannot select files';

  @override
  String get directorySelectionOnlyMobile =>
      'Directory selection is only available on mobile';

  @override
  String get selectingDirectory => 'Selecting directory...';

  @override
  String get cannotSelectDirectory => 'Cannot select directory';

  @override
  String get authorizeFolder => 'Authorize Folder';

  @override
  String get sort => 'Sort';

  @override
  String get nameSort => 'Name';

  @override
  String get lastModified => 'Last Modified';

  @override
  String get sizeSort => 'Size';

  @override
  String get descending => 'Descending';

  @override
  String get apply => 'Apply';

  @override
  String get originalPhotosFolder => 'Original Photos';

  @override
  String get searchInPdf => 'Search in PDF';

  @override
  String get searchText => 'Search text';

  @override
  String get search => 'Search';

  @override
  String get unsavedChanges => 'Unsaved Changes';

  @override
  String get unsavedChangesMessage =>
      'You have unsaved changes. Are you sure you want to exit?';

  @override
  String get discardChanges => 'Discard Changes';

  @override
  String get rotate => 'Rotate';

  @override
  String get edit => 'Edit';

  @override
  String get thumbnails => 'Thumbnails';

  @override
  String get pdfSaved => 'PDF saved successfully';

  @override
  String selectAuthorizedDirectorySubtitle(String appName, String deviceType) {
    return 'Select a directory for $appName to access on your $deviceType';
  }

  @override
  String get shareAppSubtitle => 'Recommend you a PDF magic tool:';

  @override
  String get shareAppSettingsSubtitle => 'Recommend this app to your friends';

  @override
  String get weLikeYouToo => 'Thank you for your love!';

  @override
  String get thankYouForFeedback =>
      'Your perfect score motivates us to improve!';

  @override
  String get theBestWeCanGet => 'Your feedback is the best we can get!';

  @override
  String get maybeLater => 'Maybe Later';

  @override
  String get rateNow => 'Rate Now';

  @override
  String get languageSettings => 'Language Settings';

  @override
  String get selectAuthorizedDirectory => 'Select Authorized Directory';

  @override
  String get shareApp => 'Share App';

  @override
  String get rateApp => 'Rate App';

  @override
  String get rateAppSubtitle => 'Rate us in the App Store';

  @override
  String get aboutApp => 'About App';

  @override
  String get aboutAppSubtitle => 'Version, legal information';

  @override
  String get systemLanguage => 'System Language';

  @override
  String currentLanguage(String languageName, Object language) {
    return '$languageName (Current)';
  }

  @override
  String get selectLanguage => 'Select Language';

  @override
  String defaultAppName(int number) {
    return 'App $number';
  }

  @override
  String get cannotAddDirectory =>
      'Directory authorization failed, please try again';

  @override
  String get directoryRemoved => 'Directory authorization removed';

  @override
  String get cancelAuthorization => 'Cancel Authorization';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return 'Are you sure you want to remove access to \"$directoryName\" for $appName?';
  }

  @override
  String get noKeepIt => 'No, Keep It';

  @override
  String get yesRemoveFolder => 'Yes, Remove Folder';

  @override
  String filesCount(int count) {
    return '$count files';
  }

  @override
  String directorySize(String size) {
    return '$size';
  }

  @override
  String get editName => 'Edit Name';

  @override
  String get removeDirectory => 'Remove Directory';

  @override
  String get noAuthorizedDirectories => 'No Authorized Directories';

  @override
  String get noAuthorizedDirectoriesSubtitle =>
      'Add directories to access files from different locations';

  @override
  String get addDirectory => 'Add Directory';

  @override
  String get authorizedDirectories => 'Authorized Directories';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appName can access the following directories:';
  }

  @override
  String get nameForNewFolder => 'Name for New Folder';

  @override
  String get enterCustomName => 'Enter Custom Name';

  @override
  String get customName => 'Custom Name';

  @override
  String get version => 'Version';

  @override
  String get appVersion => '1.0.0';

  @override
  String get appDescription =>
      'Smart document processing. AI auto crop and correct, showcase professionalism.';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get copyright => '© 2025 All PDF Editor. All rights reserved.';

  @override
  String get noFavoriteFiles => 'No Favorite Files';

  @override
  String get noFavoriteFilesHint =>
      'Star files to add them to favorites for quick access';

  @override
  String get fileNotFound => 'File not found';

  @override
  String get noRecentFiles =>
      'PDF/Word/Excel/PPT files you\'ve opened in this app will appear here';

  @override
  String noFilesFoundForType(int count, String fileType) {
    return 'Found $count $fileType files';
  }

  @override
  String get selectFolder => 'Authorize Folder';

  @override
  String get selectFolderSubtitle =>
      'Grant permission to find PDF/Word/Excel/PPT file directories';

  @override
  String get photoToPdfSubtitle =>
      'Scan presentation slides or notes into one PDF';

  @override
  String get mergeImagesToPdf => 'Merge Images to PDF';

  @override
  String get mergeImagesToPdfSubtitle => 'Combine multiple images into one PDF';

  @override
  String get fabTutorial =>
      'Tap the + button to start creating PDFs from images';

  @override
  String get iGotIt => 'I Got It';

  @override
  String get signatures => 'Signatures';

  @override
  String get manageSignatures => 'Manage Your Handwritten Signatures';

  @override
  String get manageSignaturesHint =>
      'Manage your handwritten signatures here. Tap the + button to add your signature. You can then use these signatures when editing PDFs.';

  @override
  String get addSignature => 'Add Signature';

  @override
  String get deleteSignature => 'Delete Signature';

  @override
  String get confirmDeleteSignature =>
      'Are you sure you want to delete this signature?';

  @override
  String get createSignature => 'Create Signature';

  @override
  String get saveSignature => 'Save Signature';

  @override
  String get strokeWidth => 'Stroke Width';

  @override
  String get color => 'Color';

  @override
  String get eraser => 'Eraser';

  @override
  String get thin => 'Thin';

  @override
  String get medium => 'Medium';

  @override
  String get thick => 'Thick';

  @override
  String get signatureCreated => 'Signature created successfully';

  @override
  String get editSignature => 'Edit Signature';

  @override
  String get signatureUpdated => 'Signature updated successfully';

  @override
  String get signatureDeleted => 'Signature deleted';

  @override
  String get noSignatures => 'No Signatures';

  @override
  String get newSignature => 'New Signature';

  @override
  String get signatureUsageHint =>
      'You can use these signatures when editing PDFs';

  @override
  String get scanningFiles =>
      'Scanning for PDF/Word/Excel/PPT files on your device';

  @override
  String pageRotated(int page, int degrees) {
    return 'Rotated page $page ($degrees°)';
  }

  @override
  String get rotationFailed => 'Rotation failed';

  @override
  String get closeSearch => 'Close search';

  @override
  String get previous => 'Previous';

  @override
  String get next => 'Next';

  @override
  String get cancelSignatureEdit => 'Cancel signature edit';

  @override
  String get back => 'Back';

  @override
  String get editMode => 'Edit Mode';

  @override
  String get highlightText => 'Highlight Text';

  @override
  String get draw => 'Draw';

  @override
  String get signature => 'Signature';

  @override
  String get highlightColor => 'Highlight Color:';

  @override
  String get brushColor => 'Brush:';

  @override
  String get thickness => 'Thickness:';

  @override
  String get confirmPlacement => 'Confirm Placement';

  @override
  String get dragToAdjustHint =>
      'Drag signature to adjust position, then tap to confirm placement';

  @override
  String get selectSignature => 'Select Signature';

  @override
  String get noSignaturesPleaseCreate =>
      'No signatures, please create one first';

  @override
  String pageIndicator(int current, int total) {
    return 'Page $current/$total';
  }

  @override
  String get androidDirectoryManagementSubtitle =>
      'Manage and add accessible folders';

  @override
  String get loadingDocument => 'Loading document...';

  @override
  String get cannotOpenFile => 'Cannot open file';

  @override
  String get galleryAccessError => 'Cannot access the selected images';

  @override
  String get permissionDenied => 'Permission denied to access photos';

  @override
  String get invalidImageSelected => 'Invalid image selected';

  @override
  String get gallerySelectionFailed => 'Failed to select images from gallery';

  @override
  String get unexpectedError => 'An unexpected error occurred';

  @override
  String get importFailed => 'Import Failed';

  @override
  String get importResults => 'Import Results';

  @override
  String allImportsFailed(int count) {
    return 'Failed to import all $count selected images';
  }

  @override
  String partialImportSuccess(int successCount, int failCount) {
    return 'Imported $successCount images successfully, $failCount failed';
  }

  @override
  String successfullyImported(int count) {
    return '✅ Successfully imported: $count images';
  }

  @override
  String failedToImport(int count) {
    return '❌ Failed to import: $count images';
  }

  @override
  String get tryAgainOrUseCamera => 'Please try again or use camera instead';

  @override
  String get checkPermissionsInSettings =>
      'Please check app permissions in Settings';

  @override
  String get trySelectingDifferentImages =>
      'Please try selecting different images';

  @override
  String get selectedImagesCorruptedOrInaccessible =>
      'The selected images may be corrupted or inaccessible. Please try selecting different images.';

  @override
  String get failedFiles => 'Failed files:';

  @override
  String andXMore(int count) {
    return '... and $count more';
  }

  @override
  String get tryAgain => 'Try Again';

  @override
  String get details => 'Details';

  @override
  String get ok => 'OK';

  @override
  String get galleryLoadFailed => 'Gallery Loading Failed';

  @override
  String get fileSelectionFailed => 'Failed to select files';

  @override
  String get storagePermissionDenied => 'Storage permission denied';

  @override
  String get unsupportedFileType => 'Unsupported file type selected';

  @override
  String get playSlideshow => 'Play Slideshow';

  @override
  String get slideshowMode => 'Slideshow Mode';

  @override
  String get exitSlideshow => 'Exit Slideshow';

  @override
  String get searchTitle => 'Search';

  @override
  String get searchHint => 'Enter file name';

  @override
  String get noResults => 'No results';

  @override
  String iosPermissionMessage(String deviceType) {
    return 'Here are your existing PDF, Word, Excel, PPT files on your phone. Due to $deviceType permission restrictions, you need to authorize access to the folders containing your files first.';
  }

  @override
  String get waitForDocLoad =>
      'Please wait for the document to load completely before preparing the PDF';

  @override
  String get generatingPdf => 'Generating PDF...';

  @override
  String get pdfGenerationFailed => 'PDF preparation failed, please try again';

  @override
  String pdfConversionFailed(String error) {
    return 'PDF conversion failed: $error';
  }

  @override
  String get conversionSuccess => 'Conversion successful';

  @override
  String pdfSavedMessage(String fileName) {
    return 'Saved as PDF file $fileName, open to view now?';
  }

  @override
  String get stayOnPage => 'Stay on page';

  @override
  String get openPdf => 'Open PDF';

  @override
  String get generatePdfVersion => 'Generate PDF version';

  @override
  String get generatePdfToEdit =>
      'A PDF version needs to be generated to edit or rotate. Continue?';

  @override
  String get continueAction => 'Continue';

  @override
  String get openingCachedPdf => 'Opening cached PDF...';

  @override
  String get saveAsPdf => 'Save as PDF';

  @override
  String get caseSensitive => 'Case sensitive';

  @override
  String notFound(String query) {
    return 'Not found \"$query\"';
  }

  @override
  String get previousMatch => 'Previous';

  @override
  String get nextMatch => 'Next';

  @override
  String get webViewOnlyIos =>
      'WebView document viewing is only supported on iOS';

  @override
  String webViewFailed(String error) {
    return 'WebView failed to load: $error';
  }

  @override
  String get officeTimeout =>
      'Office document loading timed out. This may be due to a large file or unsupported format.';

  @override
  String get addSlideshowButton => 'Add slideshow button';

  @override
  String get threshold400px => 'Threshold: 400px';

  @override
  String get confirmClear => 'Confirm Clear';

  @override
  String get confirmClearSignature =>
      'This will clear your entire signature image. Are you sure you want to clear it?';

  @override
  String get clear => 'Clear';

  @override
  String iosNetworkPermissionGuide(String appName) {
    return 'Please find \"$appName\" in your iPhone\'s Settings and ensure network access is allowed.\nPath: Settings > Privacy & Security > Local Network > $appName';
  }

  @override
  String get checkNetworkConnection => 'Please check your network connection';

  @override
  String get networkPermission => 'Network Permission';

  @override
  String get networkPermissionRequest =>
      'This app needs to access the network to process and enhance images in PDF documents.';

  @override
  String get featuresInclude => 'Features include:';

  @override
  String get imageClarityEnhancement => 'Image clarity enhancement';

  @override
  String get documentEdgeDetection => 'Document edge detection';

  @override
  String get imageOptimization => 'Image optimization';

  @override
  String get requestPermission => 'Request Permission';

  @override
  String get networkConnectionProblem => 'Network Connection Problem';

  @override
  String get networkStatusDescription =>
      'Network connection is unavailable, please check your network settings';

  @override
  String get troubleshootingSuggestions => 'Troubleshooting suggestions:';

  @override
  String get networkTroubleshootingTips =>
      'Check if Wi-Fi or mobile data is enabled\nConfirm the device is connected to a usable network\nCheck if airplane mode is on\nTry to access other apps or websites to confirm network status\nRestart the network connection or device';

  @override
  String get retry => 'Retry';

  @override
  String get unfavorite => 'Unfavorite';

  @override
  String get open => 'Open';

  @override
  String get rename => 'Rename';

  @override
  String get cannotAccessFile =>
      'Cannot access this file. Please reauthorize in \"Authorized Directories\" or reselect the file.';

  @override
  String get installOfficeAppTitle => 'App required to open Office files';

  @override
  String get installOfficeAppContent =>
      'An app is required to open this file. Install from the app store?';

  @override
  String get install => 'Install';

  @override
  String get renameFile => 'Rename';

  @override
  String get renamed => 'Renamed';

  @override
  String renameFailed(String error) {
    return 'Rename failed: $error';
  }

  @override
  String confirmDeleteFile(String fileName) {
    return 'Confirm delete file:\n$fileName?';
  }

  @override
  String get deleted => 'Deleted';

  @override
  String deleteFailed(String error) {
    return 'Delete failed: $error';
  }

  @override
  String get networkConnected => 'Network connected';

  @override
  String get networkDisconnected => 'Network disconnected';

  @override
  String get networkAccessDenied => 'Network access denied';

  @override
  String get networkOperationFailed => 'Network operation failed';

  @override
  String get connected => 'Connected';

  @override
  String get noNetwork => 'No network';

  @override
  String get thumbnailsOnlyIos => 'Thumbnails are only available on iOS';

  @override
  String get waitDocumentComplete =>
      'Waiting for document to load completely...';

  @override
  String get thumbnailDescription =>
      'Full document thumbnail\nfor easy navigation';

  @override
  String get generatingThumbnail => 'Generating complete document thumbnail...';

  @override
  String get analyzingDocument => 'Analyzing document structure and dimensions';

  @override
  String get thumbnailUnavailable => 'Thumbnail temporarily unavailable';

  @override
  String get clickRefreshRetry =>
      'Click the refresh button in the top right to retry';
}
