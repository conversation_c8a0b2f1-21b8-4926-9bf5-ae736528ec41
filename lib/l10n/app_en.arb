{"@@locale": "en", "appName": "All PDF Editor", "documents": "Documents", "tools": "Tools", "settings": "Settings", "home": "Home", "camera": "Camera", "gallery": "Gallery", "files": "Files", "photoToPdf": "Scan to PDF", "imageToPdf": "Image to PDF", "addImages": "Add Images", "addImagesFromGallery": "Add {count} images from gallery", "@addImagesFromGallery": {"placeholders": {"count": {"type": "int"}}}, "addImagesFromFiles": "Add {count} images from files", "@addImagesFromFiles": {"placeholders": {"count": {"type": "int"}}}, "convert": "Convert", "undo": "Undo", "discardChangesTitle": "Discard changes?", "discardChangesContent": "You have unsaved changes. Are you sure you want to exit?", "keepEditing": "Keep Editing", "discard": "Discard", "takePicture": "Take a picture", "reorderImage": "Reorder image", "cropImage": "Crop Image", "deleteImage": "Delete Image", "confirmDeleteImage": "Are you sure you want to delete this image?", "cancel": "Cancel", "delete": "Delete", "confirmUndo": "Are you sure you want to undo \"{action}\"?", "@confirmUndo": {"placeholders": {"action": {"type": "String"}}}, "pdfSavedSuccessfully": "PDF saved successfully", "pdfSaveFailed": "Failed to save PDF", "saveAs": "Save As", "fileName": "File Name", "save": "Save", "scanPrefix": "<PERSON><PERSON>", "addMoreImages": "Add More Images", "takePhoto": "Take Photo", "importFromAlbum": "Import from Album", "importFromOtherApps": "Import from Other Apps", "aiProcessing": "AI Processing", "lowResolution": "Low Resolution", "imagePreviewTitle": "Image {currentPage} of {totalPages}", "@imagePreviewTitle": {"description": "Title for the image preview screen showing the current image number and total count", "placeholders": {"currentPage": {"type": "int"}, "totalPages": {"type": "int"}}}, "confirmExit": "Confirm Exit", "discardCropChangesContent": "You haven't saved. Are you sure you want to discard the manually adjusted coordinates?", "continueAdjusting": "Cancel", "discardChangesConfirmation": "Confirm", "restoreDefault": "<PERSON><PERSON>", "confirmRestoreDefaultContent": "This action will discard your manually adjusted coordinates. Do you want to discard your recent adjustments?", "imageNotLoadedError": "Image not loaded yet, please try again later.", "imageProcessingFailedError": "Image processing failed, please try again.", "dragCornersHint": "Drag the blue dots to adjust the four corners of the crop area.", "splashTitle": "Photo to PDF Magic Tool", "splashSubtitle": "Auto organize meeting photos, crop and merge into PDF with one tap", "splashOriginalPhoto": "Original Photo", "splashGeneratedPdf": "Auto Generated PDF", "splashWorkflow": "App is starting, please wait...", "recent": "Recent", "favorite": "Favorites", "loadedFiles": "Loaded {count} files", "@loadedFiles": {"placeholders": {"count": {"type": "int"}}}, "cannotLoadDirectoryFiles": "Cannot load directory files", "cannotSelectFiles": "Cannot select files", "directorySelectionOnlyMobile": "Directory selection is only available on mobile", "selectingDirectory": "Selecting directory...", "cannotSelectDirectory": "Cannot select directory", "authorizeFolder": "Authorize Folder", "sort": "Sort", "nameSort": "Name", "lastModified": "Last Modified", "sizeSort": "Size", "descending": "Descending", "apply": "Apply", "originalPhotosFolder": "Original Photos", "searchInPdf": "Search in PDF", "searchText": "Search text", "search": "Search", "unsavedChanges": "Unsaved Changes", "unsavedChangesMessage": "You have unsaved changes. Are you sure you want to exit?", "discardChanges": "Discard Changes", "rotate": "Rotate", "edit": "Edit", "thumbnails": "Thumbnails", "pdfSaved": "PDF saved successfully", "selectAuthorizedDirectorySubtitle": "Select a directory for {appName} to access on your {deviceType}", "@selectAuthorizedDirectorySubtitle": {"placeholders": {"appName": {"type": "String"}, "deviceType": {"type": "String"}}}, "shareAppSubtitle": "Recommend you a PDF magic tool:", "shareAppSettingsSubtitle": "Recommend this app to your friends", "weLikeYouToo": "Thank you for your love!", "thankYouForFeedback": "Your perfect score motivates us to improve!", "theBestWeCanGet": "Your feedback is the best we can get!", "maybeLater": "Maybe Later", "rateNow": "Rate Now", "languageSettings": "Language Settings", "selectAuthorizedDirectory": "Select Authorized Directory", "shareApp": "Share App", "rateApp": "Rate App", "rateAppSubtitle": "Rate us in the App Store", "aboutApp": "About App", "aboutAppSubtitle": "Version, legal information", "systemLanguage": "System Language", "currentLanguage": "{languageName} (Current)", "@currentLanguage": {"placeholders": {"languageName": {"type": "String"}}}, "selectLanguage": "Select Language", "defaultAppName": "App {number}", "@defaultAppName": {"placeholders": {"number": {"type": "int"}}}, "cannotAddDirectory": "Directory authorization failed, please try again", "directoryRemoved": "Directory authorization removed", "cancelAuthorization": "Cancel Authorization", "confirmCancelAuthorization": "Are you sure you want to remove access to \"{directoryName}\" for {appName}?", "@confirmCancelAuthorization": {"placeholders": {"appName": {"type": "String"}, "directoryName": {"type": "String"}}}, "noKeepIt": "No, Keep It", "yesRemoveFolder": "Yes, Remove Folder", "filesCount": "{count} files", "@filesCount": {"placeholders": {"count": {"type": "int"}}}, "directorySize": "{size}", "@directorySize": {"placeholders": {"size": {"type": "String"}}}, "editName": "Edit Name", "removeDirectory": "Remove Directory", "noAuthorizedDirectories": "No Authorized Directories", "noAuthorizedDirectoriesSubtitle": "Add directories to access files from different locations", "addDirectory": "Add Directory", "authorizedDirectories": "Authorized Directories", "appCanAccessDirectories": "{appName} can access the following directories:", "@appCanAccessDirectories": {"placeholders": {"appName": {"type": "String"}}}, "nameForNewFolder": "Name for New Folder", "enterCustomName": "Enter Custom Name", "customName": "Custom Name", "version": "Version", "appVersion": "1.0.0", "appDescription": "Smart document processing. AI auto crop and correct, showcase professionalism.", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "copyright": "© 2025 All PDF Editor. All rights reserved.", "noFavoriteFiles": "No Favorite Files", "noFavoriteFilesHint": "Star files to add them to favorites for quick access", "fileNotFound": "File not found", "noRecentFiles": "PDF/Word/Excel/PPT files you've opened in this app will appear here", "noFilesFoundForType": "Found {count} {fileType} files", "@noFilesFoundForType": {"placeholders": {"count": {"type": "int"}, "fileType": {"type": "String"}}}, "selectFolder": "Authorize Folder", "selectFolderSubtitle": "Grant permission to find PDF/Word/Excel/PPT file directories", "photoToPdfSubtitle": "Scan presentation slides or notes into one PDF", "mergeImagesToPdf": "Merge Images to PDF", "mergeImagesToPdfSubtitle": "Combine multiple images into one PDF", "fabTutorial": "Tap the + button to start creating PDFs from images", "iGotIt": "I Got It", "signatures": "Signatures", "manageSignatures": "Manage Your Handwritten Signatures", "manageSignaturesHint": "Manage your handwritten signatures here. Tap the + button to add your signature. You can then use these signatures when editing PDFs.", "addSignature": "Add Signature", "deleteSignature": "Delete Signature", "confirmDeleteSignature": "Are you sure you want to delete this signature?", "createSignature": "Create Signature", "saveSignature": "Save Signature", "strokeWidth": "Stroke Width", "color": "Color", "eraser": "Eraser", "thin": "Thin", "medium": "Medium", "thick": "<PERSON><PERSON><PERSON>", "signatureCreated": "Signature created successfully", "editSignature": "Edit Signature", "signatureUpdated": "Signature updated successfully", "signatureDeleted": "Signature deleted", "noSignatures": "No Signatures", "newSignature": "New Signature", "signatureUsageHint": "You can use these signatures when editing PDFs", "scanningFiles": "Scanning for PDF/Word/Excel/PPT files on your device", "pageRotated": "Rotated page {page} ({degrees}°)", "@pageRotated": {"placeholders": {"page": {"type": "int"}, "degrees": {"type": "int"}}}, "rotationFailed": "Rotation failed", "closeSearch": "Close search", "previous": "Previous", "next": "Next", "cancelSignatureEdit": "<PERSON>cel signature edit", "back": "Back", "editMode": "Edit Mode", "highlightText": "Highlight Text", "draw": "Draw", "signature": "Signature", "highlightColor": "Highlight Color:", "brushColor": "Brush:", "thickness": "Thickness:", "confirmPlacement": "Confirm Placement", "dragToAdjustHint": "Drag signature to adjust position, then tap to confirm placement", "selectSignature": "Select Signature", "noSignaturesPleaseCreate": "No signatures, please create one first", "pageIndicator": "Page {current}/{total}", "@pageIndicator": {"placeholders": {"current": {"type": "int"}, "total": {"type": "int"}}}, "androidDirectoryManagementSubtitle": "Manage and add accessible folders", "loadingDocument": "Loading document...", "cannotOpenFile": "Cannot open file", "galleryAccessError": "Cannot access the selected images", "permissionDenied": "Permission denied to access photos", "invalidImageSelected": "Invalid image selected", "gallerySelectionFailed": "Failed to select images from gallery", "unexpectedError": "An unexpected error occurred", "importFailed": "Import Failed", "importResults": "Import Results", "allImportsFailed": "Failed to import all {count} selected images", "@allImportsFailed": {"placeholders": {"count": {"type": "int"}}}, "partialImportSuccess": "Imported {successCount} images successfully, {failCount} failed", "@partialImportSuccess": {"placeholders": {"successCount": {"type": "int"}, "failCount": {"type": "int"}}}, "successfullyImported": "✅ Successfully imported: {count} images", "@successfullyImported": {"placeholders": {"count": {"type": "int"}}}, "failedToImport": "❌ Failed to import: {count} images", "@failedToImport": {"placeholders": {"count": {"type": "int"}}}, "tryAgainOrUseCamera": "Please try again or use camera instead", "checkPermissionsInSettings": "Please check app permissions in Settings", "trySelectingDifferentImages": "Please try selecting different images", "selectedImagesCorruptedOrInaccessible": "The selected images may be corrupted or inaccessible. Please try selecting different images.", "failedFiles": "Failed files:", "andXMore": "... and {count} more", "@andXMore": {"placeholders": {"count": {"type": "int"}}}, "tryAgain": "Try Again", "details": "Details", "ok": "OK", "galleryLoadFailed": "Gallery Loading Failed", "fileSelectionFailed": "Failed to select files", "storagePermissionDenied": "Storage permission denied", "unsupportedFileType": "Unsupported file type selected", "playSlideshow": "Play Slideshow", "slideshowMode": "Slideshow Mode", "exitSlideshow": "Exit Slideshow", "searchTitle": "Search", "searchHint": "Enter file name", "noResults": "No results", "iosPermissionMessage": "Here are your existing PDF, Word, Excel, PPT files on your phone. Due to {deviceType} permission restrictions, you need to authorize access to the folders containing your files first.", "@iosPermissionMessage": {"placeholders": {"deviceType": {"type": "String"}}}, "waitForDocLoad": "Please wait for the document to load completely before preparing the PDF", "generatingPdf": "Generating PDF...", "pdfGenerationFailed": "PDF preparation failed, please try again", "pdfConversionFailed": "PDF conversion failed: {error}", "@pdfConversionFailed": {"placeholders": {"error": {"type": "String"}}}, "conversionSuccess": "Conversion successful", "pdfSavedMessage": "Saved as PDF file {fileName}, open to view now?", "@pdfSavedMessage": {"placeholders": {"fileName": {"type": "String"}}}, "stayOnPage": "Stay on page", "openPdf": "Open PDF", "generatePdfVersion": "Generate PDF version", "generatePdfToEdit": "A PDF version needs to be generated to edit or rotate. Continue?", "continueAction": "Continue", "openingCachedPdf": "Opening cached PDF...", "saveAsPdf": "Save as PDF", "caseSensitive": "Case sensitive", "notFound": "Not found \"{query}\"", "@notFound": {"placeholders": {"query": {"type": "String"}}}, "previousMatch": "Previous", "nextMatch": "Next", "webViewOnlyIos": "WebView document viewing is only supported on iOS", "webViewFailed": "WebView failed to load: {error}", "@webViewFailed": {"placeholders": {"error": {"type": "String"}}}, "officeTimeout": "Office document loading timed out. This may be due to a large file or unsupported format.", "addSlideshowButton": "Add slideshow button", "threshold400px": "Threshold: 400px", "confirmClear": "Confirm Clear", "confirmClearSignature": "This will clear your entire signature image. Are you sure you want to clear it?", "clear": "Clear", "iosNetworkPermissionGuide": "Please find \"{appName}\" in your iPhone's Settings and ensure network access is allowed.\nPath: Settings > Privacy & Security > Local Network > {appName}", "@iosNetworkPermissionGuide": {"placeholders": {"appName": {"type": "String"}}}, "checkNetworkConnection": "Please check your network connection", "networkPermission": "Network Permission", "networkPermissionRequest": "This app needs to access the network to process and enhance images in PDF documents.", "featuresInclude": "Features include:", "imageClarityEnhancement": "Image clarity enhancement", "documentEdgeDetection": "Document edge detection", "imageOptimization": "Image optimization", "requestPermission": "Request Permission", "networkConnectionProblem": "Network Connection Problem", "networkStatusDescription": "Network connection is unavailable, please check your network settings", "troubleshootingSuggestions": "Troubleshooting suggestions:", "networkTroubleshootingTips": "Check if Wi-Fi or mobile data is enabled\nConfirm the device is connected to a usable network\nCheck if airplane mode is on\nTry to access other apps or websites to confirm network status\nRestart the network connection or device", "retry": "Retry", "unfavorite": "Unfavorite", "open": "Open", "rename": "<PERSON><PERSON>", "cannotAccessFile": "Cannot access this file. Please reauthorize in \"Authorized Directories\" or reselect the file.", "installOfficeAppTitle": "App required to open Office files", "installOfficeAppContent": "An app is required to open this file. Install from the app store?", "install": "Install", "renameFile": "<PERSON><PERSON>", "renamed": "<PERSON>amed", "renameFailed": "<PERSON><PERSON> failed: {error}", "@renameFailed": {"placeholders": {"error": {"type": "String"}}}, "confirmDeleteFile": "Confirm delete file:\n{fileName}?", "@confirmDeleteFile": {"placeholders": {"fileName": {"type": "String"}}}, "deleted": "Deleted", "deleteFailed": "Delete failed: {error}", "@deleteFailed": {"placeholders": {"error": {"type": "String"}}}, "networkConnected": "Network connected", "networkDisconnected": "Network disconnected", "networkAccessDenied": "Network access denied", "networkOperationFailed": "Network operation failed", "connected": "Connected", "noNetwork": "No network", "thumbnailsOnlyIos": "Thumbnails are only available on iOS", "waitDocumentComplete": "Waiting for document to load completely...", "thumbnailDescription": "Full document thumbnail\nfor easy navigation", "generatingThumbnail": "Generating complete document thumbnail...", "analyzingDocument": "Analyzing document structure and dimensions", "thumbnailUnavailable": "Thumbnail temporarily unavailable", "clickRefreshRetry": "Click the refresh button in the top right to retry"}