// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appName => '全能PDF编辑器';

  @override
  String get documents => '文档';

  @override
  String get tools => '工具';

  @override
  String get settings => '设置';

  @override
  String get home => '首页';

  @override
  String get camera => '相机';

  @override
  String get gallery => '相册';

  @override
  String get files => '文件';

  @override
  String get photoToPdf => '拍照转PDF';

  @override
  String get imageToPdf => '图片转PDF';

  @override
  String get addImages => '添加图片';

  @override
  String addImagesFromGallery(int count) {
    return '从相册添加$count张图片';
  }

  @override
  String addImagesFromFiles(int count) {
    return '从文件添加$count张图片';
  }

  @override
  String get convert => '转换';

  @override
  String get undo => '撤销';

  @override
  String get discardChangesTitle => '放弃更改？';

  @override
  String get discardChangesContent => '您有未保存的更改。您确定要退出吗？';

  @override
  String get keepEditing => '继续编辑';

  @override
  String get discard => '放弃';

  @override
  String get takePicture => '拍摄照片';

  @override
  String get reorderImage => '调整图片顺序';

  @override
  String get cropImage => '裁剪图片';

  @override
  String get deleteImage => '删除图片';

  @override
  String get confirmDeleteImage => '您确定要删除这张图片吗？';

  @override
  String get cancel => '取消';

  @override
  String get delete => '删除';

  @override
  String confirmUndo(String action) {
    return '您确定要撤销“$action”操作吗？';
  }

  @override
  String get pdfSavedSuccessfully => 'PDF保存成功';

  @override
  String get pdfSaveFailed => 'PDF保存失败';

  @override
  String get saveAs => '另存为';

  @override
  String get fileName => '文件名';

  @override
  String get save => '保存';

  @override
  String get scanPrefix => '扫描';

  @override
  String get addMoreImages => '添加更多图片';

  @override
  String get takePhoto => '拍照';

  @override
  String get importFromAlbum => '从相册导入';

  @override
  String get importFromOtherApps => '从其他应用导入';

  @override
  String get aiProcessing => 'AI处理中';

  @override
  String get lowResolution => '分辨率太低';

  @override
  String imagePreviewTitle(int currentPage, int totalPages) {
    return '第 $currentPage 张 / 共 $totalPages 张';
  }

  @override
  String get confirmExit => '确认退出';

  @override
  String get discardCropChangesContent => '您还没有保存。您确定要放弃手工调整的坐标吗？';

  @override
  String get continueAdjusting => '手滑了，我要继续调整';

  @override
  String get discardChangesConfirmation => '对，我不要了';

  @override
  String get restoreDefault => '恢复默认';

  @override
  String get confirmRestoreDefaultContent => '此操作会丢弃你刚手工调整的坐标。是否放弃刚才的调整？';

  @override
  String get imageNotLoadedError => '图片尚未加载完成，请稍候再试。';

  @override
  String get imageProcessingFailedError => '图片处理失败，请重试。';

  @override
  String get dragCornersHint => '拖拽蓝色圆点调整裁剪区域的四个角。';

  @override
  String get splashTitle => '拍照转PDF神器';

  @override
  String get splashSubtitle => '自动整理会议照片，一键裁剪合并为PDF';

  @override
  String get splashOriginalPhoto => '原始照片';

  @override
  String get splashGeneratedPdf => '自动生成PDF';

  @override
  String get splashWorkflow => '正在启动应用，请稍候...';

  @override
  String get recent => '最近';

  @override
  String get favorite => '收藏';

  @override
  String loadedFiles(int count) {
    return '已加载$count个文件';
  }

  @override
  String get cannotLoadDirectoryFiles => '无法加载目录文件';

  @override
  String get cannotSelectFiles => '无法选择文件';

  @override
  String get directorySelectionOnlyMobile => '目录选择仅在移动设备上可用';

  @override
  String get selectingDirectory => '正在选择目录...';

  @override
  String get cannotSelectDirectory => '无法选择目录';

  @override
  String get authorizeFolder => '授权文件夹';

  @override
  String get sort => '排序';

  @override
  String get nameSort => '名称';

  @override
  String get lastModified => '最后修改';

  @override
  String get sizeSort => '大小';

  @override
  String get descending => '降序';

  @override
  String get apply => '应用';

  @override
  String get originalPhotosFolder => '拍照原图';

  @override
  String get searchInPdf => '在PDF中搜索';

  @override
  String get searchText => '搜索文本';

  @override
  String get search => '搜索';

  @override
  String get unsavedChanges => '未保存的更改';

  @override
  String get unsavedChangesMessage => '您有未保存的更改。您确定要退出吗？';

  @override
  String get discardChanges => '丢弃更改';

  @override
  String get rotate => '旋转';

  @override
  String get edit => '编辑';

  @override
  String get thumbnails => '缩略图';

  @override
  String get pdfSaved => 'PDF保存成功';

  @override
  String selectAuthorizedDirectorySubtitle(String appName, String deviceType) {
    return '为$appName选择一个可在您的$deviceType上访问的目录';
  }

  @override
  String get shareAppSubtitle => '给你推荐一款PDF神器：';

  @override
  String get shareAppSettingsSubtitle => '向你的朋友推荐这款App';

  @override
  String get weLikeYouToo => '谢谢你的喜爱！';

  @override
  String get thankYouForFeedback => '您的满分是我们改进的动力！';

  @override
  String get theBestWeCanGet => '太棒了，满分好评！';

  @override
  String get maybeLater => '稍后再说';

  @override
  String get rateNow => '现在去商店评分';

  @override
  String get languageSettings => '语言设置';

  @override
  String get selectAuthorizedDirectory => '选择授权目录';

  @override
  String get shareApp => '分享应用';

  @override
  String get rateApp => '评价应用';

  @override
  String get rateAppSubtitle => '在App Store中为我们评分';

  @override
  String get aboutApp => '关于应用';

  @override
  String get aboutAppSubtitle => '版本、法律信息';

  @override
  String get systemLanguage => '系统语言';

  @override
  String currentLanguage(String languageName, Object language) {
    return '$languageName（当前）';
  }

  @override
  String get selectLanguage => '选择语言';

  @override
  String defaultAppName(int number) {
    return '应用 $number';
  }

  @override
  String get cannotAddDirectory => '授权目录没有成功，请重试';

  @override
  String get directoryRemoved => '目录授权已移除';

  @override
  String get cancelAuthorization => '取消授权';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return '您确定要取消$appName对\"$directoryName\"的访问权限吗？';
  }

  @override
  String get noKeepIt => '不，保留它';

  @override
  String get yesRemoveFolder => '是，移除文件夹';

  @override
  String filesCount(int count) {
    return '$count个文件';
  }

  @override
  String directorySize(String size) {
    return '$size';
  }

  @override
  String get editName => '编辑名称';

  @override
  String get removeDirectory => '移除目录';

  @override
  String get noAuthorizedDirectories => '没有授权目录';

  @override
  String get noAuthorizedDirectoriesSubtitle => '添加目录以访问不同位置的文件';

  @override
  String get addDirectory => '添加目录';

  @override
  String get authorizedDirectories => '授权目录';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appName可以访问以下目录：';
  }

  @override
  String get nameForNewFolder => '新文件夹名称';

  @override
  String get enterCustomName => '输入自定义名称';

  @override
  String get customName => '自定义名称';

  @override
  String get version => '版本';

  @override
  String get appVersion => '1.0.0';

  @override
  String get appDescription => '智能文档处理。AI自动裁剪矫正，彰显专业。';

  @override
  String get privacyPolicy => '隐私政策';

  @override
  String get termsOfService => '服务条款';

  @override
  String get copyright => '© 2025 全能PDF编辑器。保留所有权利。';

  @override
  String get noFavoriteFiles => '没有收藏文件';

  @override
  String get noFavoriteFilesHint => '点击星标将文件添加到收藏以便快速访问';

  @override
  String get fileNotFound => '文件未找到';

  @override
  String get noRecentFiles => '您在本App中曾打开过的PDF/Word/Excel/PPT文件会显示在这里';

  @override
  String noFilesFoundForType(int count, String fileType) {
    return '找到 $count 个$fileType文件';
  }

  @override
  String get selectFolder => '授权文件夹';

  @override
  String get selectFolderSubtitle => '授权本App查找PDF/Word/Excel/PPT文件目录';

  @override
  String get photoToPdfSubtitle => '批量拍照合成PDF文档';

  @override
  String get mergeImagesToPdf => '合并图片为PDF';

  @override
  String get mergeImagesToPdfSubtitle => '将多张图片合并成一个PDF';

  @override
  String get fabTutorial => '点击+按钮开始从图片创建PDF';

  @override
  String get iGotIt => '我知道了';

  @override
  String get signatures => '签名';

  @override
  String get manageSignatures => '管理您的手写签名';

  @override
  String get manageSignaturesHint =>
      '这里管理您的手写签名。点击+号按钮添加您的签名。之后您可以在编辑PDF时使用这些签名。';

  @override
  String get addSignature => '添加签名';

  @override
  String get deleteSignature => '删除签名';

  @override
  String get confirmDeleteSignature => '确定要删除这个签名吗？';

  @override
  String get createSignature => '创建签名';

  @override
  String get saveSignature => '保存签名';

  @override
  String get strokeWidth => '画笔粗细';

  @override
  String get color => '颜色';

  @override
  String get eraser => '橡皮擦';

  @override
  String get thin => '细';

  @override
  String get medium => '中';

  @override
  String get thick => '粗';

  @override
  String get signatureCreated => '签名创建成功';

  @override
  String get editSignature => '编辑签名';

  @override
  String get signatureUpdated => '签名更新成功';

  @override
  String get signatureDeleted => '签名已删除';

  @override
  String get noSignatures => '暂无签名';

  @override
  String get newSignature => '新签名';

  @override
  String get signatureUsageHint => '您在编辑PDF时可以使用这些签名';

  @override
  String get scanningFiles => '正在扫描您手机上的PDF/Word/Excel/PPT文件';

  @override
  String pageRotated(int page, int degrees) {
    return '已旋转第$page页 ($degrees°)';
  }

  @override
  String get rotationFailed => '旋转失败';

  @override
  String get closeSearch => '关闭搜索';

  @override
  String get previous => '上一个';

  @override
  String get next => '下一个';

  @override
  String get cancelSignatureEdit => '取消签名编辑';

  @override
  String get back => '返回';

  @override
  String get editMode => '编辑模式';

  @override
  String get highlightText => '标记文字';

  @override
  String get draw => '绘制';

  @override
  String get signature => '签名';

  @override
  String get highlightColor => '高亮颜色:';

  @override
  String get brushColor => '画笔:';

  @override
  String get thickness => '粗细:';

  @override
  String get confirmPlacement => '确定放置';

  @override
  String get dragToAdjustHint => '拖动签名调整位置，点击确认放置';

  @override
  String get selectSignature => '选择签名';

  @override
  String get noSignaturesPleaseCreate => '暂无签名，请先创建签名';

  @override
  String pageIndicator(int current, int total) {
    return '第$current/$total页';
  }

  @override
  String get androidDirectoryManagementSubtitle => '管理和添加可访问的文件夹';

  @override
  String get loadingDocument => '正在加载文档...';

  @override
  String get cannotOpenFile => '无法打开文件';

  @override
  String get galleryAccessError => '无法访问所选图片';

  @override
  String get permissionDenied => '访问相册权限被拒绝';

  @override
  String get invalidImageSelected => '选择了无效的图片';

  @override
  String get gallerySelectionFailed => '从相册选择图片失败';

  @override
  String get unexpectedError => '发生意外错误';

  @override
  String get importFailed => '导入失败';

  @override
  String get importResults => '导入结果';

  @override
  String allImportsFailed(int count) {
    return '所有$count张图片导入失败';
  }

  @override
  String partialImportSuccess(int successCount, int failCount) {
    return '$successCount张图片导入成功，$failCount张失败';
  }

  @override
  String successfullyImported(int count) {
    return '✅ 成功导入：$count张图片';
  }

  @override
  String failedToImport(int count) {
    return '❌ 导入失败：$count张图片';
  }

  @override
  String get tryAgainOrUseCamera => '请重试或使用相机';

  @override
  String get checkPermissionsInSettings => '请在设置中检查应用权限';

  @override
  String get trySelectingDifferentImages => '请尝试选择其他图片';

  @override
  String get selectedImagesCorruptedOrInaccessible =>
      '所选图片可能已损坏或无法访问。请尝试选择其他图片。';

  @override
  String get failedFiles => '失败的文件：';

  @override
  String andXMore(int count) {
    return '... 以及其他$count个';
  }

  @override
  String get tryAgain => '重试';

  @override
  String get details => '详情';

  @override
  String get ok => '确定';

  @override
  String get galleryLoadFailed => '相册加载失败';

  @override
  String get fileSelectionFailed => '文件选择失败';

  @override
  String get storagePermissionDenied => '存储权限被拒绝';

  @override
  String get unsupportedFileType => '选择了不支持的文件类型';

  @override
  String get playSlideshow => '播放幻灯片';

  @override
  String get slideshowMode => '幻灯片模式';

  @override
  String get exitSlideshow => '退出幻灯片';

  @override
  String get searchTitle => '搜索';

  @override
  String get searchHint => '输入文件名';

  @override
  String get noResults => '暂无结果';

  @override
  String iosPermissionMessage(String deviceType) {
    return '这里显示您手机上已有的PDF、Word、Excel、PPT文件。由于$deviceType的权限限制，您需要先授权访问文件所在的文件夹。';
  }

  @override
  String get waitForDocLoad => '请等待文档完全加载后再准备PDF';

  @override
  String get generatingPdf => '正在生成PDF...';

  @override
  String get pdfGenerationFailed => 'PDF准备失败，请重试';

  @override
  String pdfConversionFailed(String error) {
    return 'PDF转换失败: $error';
  }

  @override
  String get conversionSuccess => '转换成功';

  @override
  String pdfSavedMessage(String fileName) {
    return '已保存为PDF文件 $fileName，是否现在打开查看？';
  }

  @override
  String get stayOnPage => '留在当前页面';

  @override
  String get openPdf => '打开PDF';

  @override
  String get generatePdfVersion => '生成PDF版本';

  @override
  String get generatePdfToEdit => '需要生成PDF版本才能编辑或旋转。是否继续？';

  @override
  String get continueAction => '继续';

  @override
  String get openingCachedPdf => '正在打开缓存的PDF...';

  @override
  String get saveAsPdf => '保存为PDF';

  @override
  String get caseSensitive => '区分大小写';

  @override
  String notFound(String query) {
    return '未找到 \"$query\"';
  }

  @override
  String get previousMatch => '上一个';

  @override
  String get nextMatch => '下一个';

  @override
  String get webViewOnlyIos =>
      'WebView document viewing is only supported on iOS';

  @override
  String webViewFailed(String error) {
    return 'WebView加载失败: $error';
  }

  @override
  String get officeTimeout => 'Office 文档加载超时。这可能是由于文件过大或格式不支持。';

  @override
  String get addSlideshowButton => '添加播放幻灯片按钮';

  @override
  String get threshold400px => '阈值：400像素';

  @override
  String get confirmClear => '确认清除';

  @override
  String get confirmClearSignature => '这将整个清除您的签名图像。您确定要清除吗？';

  @override
  String get clear => '清除';

  @override
  String iosNetworkPermissionGuide(String appName) {
    return '请在iPhone设置中找到\"$appName\",确保允许网络访问。\n设置路径：设置 > 隐私与安全性 > 本地网络 > $appName';
  }

  @override
  String get checkNetworkConnection => '请检查网络连接是否正常';

  @override
  String get networkPermission => '网络权限';

  @override
  String get networkPermissionRequest => '该应用需要访问网络来处理和增强PDF文档中的图片。';

  @override
  String get featuresInclude => '功能包括：';

  @override
  String get imageClarityEnhancement => '图片清晰度增强';

  @override
  String get documentEdgeDetection => '文档边缘检测';

  @override
  String get imageOptimization => '图像优化处理';

  @override
  String get requestPermission => '申请权限';

  @override
  String get networkConnectionProblem => '网络连接问题';

  @override
  String get networkStatusDescription => '网络连接不可用，请检查网络设置';

  @override
  String get troubleshootingSuggestions => '故障排除建议：';

  @override
  String get networkTroubleshootingTips =>
      '检查Wi-Fi或移动数据是否已开启\n确认设备已连接到可用的网络\n检查是否在飞行模式下\n尝试访问其他应用或网站确认网络状态\n重启网络连接或设备';

  @override
  String get retry => '重试';

  @override
  String get unfavorite => '取消收藏';

  @override
  String get open => '打开';

  @override
  String get rename => '改名';

  @override
  String get cannotAccessFile => '无法访问该文件。请在“已授权目录”中重新授权或重新选择文件。';

  @override
  String get installOfficeAppTitle => '需要安装App来打开Office文件';

  @override
  String get installOfficeAppContent => '需要安装一个应用来打开这个文件。是否从应用商店安装？';

  @override
  String get install => '安装';

  @override
  String get renameFile => '改名';

  @override
  String get renamed => '已改名';

  @override
  String renameFailed(String error) {
    return '改名失败: $error';
  }

  @override
  String confirmDeleteFile(String fileName) {
    return '确认删除文件：\n$fileName?';
  }

  @override
  String get deleted => '已删除';

  @override
  String deleteFailed(String error) {
    return '删除失败: $error';
  }

  @override
  String get networkConnected => '网络已连接';

  @override
  String get networkDisconnected => '网络已断开';

  @override
  String get networkAccessDenied => '网络访问被拒绝';

  @override
  String get networkOperationFailed => '网络操作失败';

  @override
  String get connected => '已连接';

  @override
  String get noNetwork => '无网络';

  @override
  String get thumbnailsOnlyIos => '缩略图仅在 iOS 上可用';

  @override
  String get waitDocumentComplete => '等待文档加载完成...';

  @override
  String get thumbnailDescription => '完整文档的缩略图\n方便快速导航';

  @override
  String get generatingThumbnail => '正在生成完整文档缩略图...';

  @override
  String get analyzingDocument => '正在分析文档结构和尺寸';

  @override
  String get thumbnailUnavailable => '缩略图暂时无法显示';

  @override
  String get clickRefreshRetry => '点击右上角的刷新按钮重试';
}

/// The translations for Chinese, as used in Taiwan (`zh_TW`).
class AppLocalizationsZhTw extends AppLocalizationsZh {
  AppLocalizationsZhTw() : super('zh_TW');

  @override
  String get appName => '全能PDF編輯器';

  @override
  String get documents => '文檔';

  @override
  String get tools => '工具';

  @override
  String get settings => '設定';

  @override
  String get home => '首頁';

  @override
  String get camera => '相機';

  @override
  String get gallery => '相簿';

  @override
  String get files => '檔案';

  @override
  String get photoToPdf => '拍照轉PDF';

  @override
  String get imageToPdf => '圖片轉PDF';

  @override
  String get addImages => '新增圖片';

  @override
  String addImagesFromGallery(int count) {
    return '從相簿新增$count張圖片';
  }

  @override
  String addImagesFromFiles(int count) {
    return '從檔案新增$count張圖片';
  }

  @override
  String get convert => '轉換';

  @override
  String get undo => '復原';

  @override
  String get discardChangesTitle => '放棄變更？';

  @override
  String get discardChangesContent => '您有未儲存的變更。您確定要退出嗎？';

  @override
  String get keepEditing => '繼續編輯';

  @override
  String get discard => '放棄';

  @override
  String get takePicture => '拍攝照片';

  @override
  String get reorderImage => '調整圖片順序';

  @override
  String get cropImage => '裁剪圖片';

  @override
  String get deleteImage => '刪除圖片';

  @override
  String get confirmDeleteImage => '您確定要刪除這張圖片嗎？';

  @override
  String get cancel => '取消';

  @override
  String get delete => '刪除';

  @override
  String confirmUndo(String action) {
    return '您確定要復原「$action」操作嗎？';
  }

  @override
  String get pdfSavedSuccessfully => 'PDF儲存成功';

  @override
  String get pdfSaveFailed => 'PDF儲存失敗';

  @override
  String get saveAs => '另存新檔';

  @override
  String get fileName => '檔案名稱';

  @override
  String get save => '儲存';

  @override
  String get scanPrefix => '掃描';

  @override
  String get addMoreImages => '新增更多圖片';

  @override
  String get takePhoto => '拍照';

  @override
  String get importFromAlbum => '從相簿匯入';

  @override
  String get importFromOtherApps => '從其他應用程式匯入';

  @override
  String get aiProcessing => 'AI處理中';

  @override
  String get lowResolution => '解析度太低';

  @override
  String imagePreviewTitle(int currentPage, int totalPages) {
    return '第 $currentPage 張 / 共 $totalPages 張';
  }

  @override
  String get confirmExit => '確認退出';

  @override
  String get discardCropChangesContent => '您尚未儲存。您確定要放棄手動調整的座標嗎？';

  @override
  String get continueAdjusting => '手滑了，我要繼續調整';

  @override
  String get discardChangesConfirmation => '對，我不要了';

  @override
  String get restoreDefault => '恢復預設';

  @override
  String get confirmRestoreDefaultContent => '此操作會捨棄您剛才手動調整的座標。是否要放棄剛才的調整？';

  @override
  String get imageNotLoadedError => '圖片尚未載入完成，請稍候再試。';

  @override
  String get imageProcessingFailedError => '圖片處理失敗，请重试。';

  @override
  String get dragCornersHint => '拖曳藍色圓點調整裁剪區域的四個角。';

  @override
  String get splashTitle => '拍照轉PDF神器';

  @override
  String get splashSubtitle => '自動整理會議照片，一鍵裁剪合併為PDF';

  @override
  String get splashOriginalPhoto => '原始照片';

  @override
  String get splashGeneratedPdf => '自動生成PDF';

  @override
  String get splashWorkflow => '正在啟動應用，請稍候...';

  @override
  String get recent => '最近';

  @override
  String get favorite => '收藏';

  @override
  String get shareAppSubtitle => '給你推薦一款PDF神器：';

  @override
  String get weLikeYouToo => '謝謝你的喜愛！';

  @override
  String get thankYouForFeedback => '您的滿分是我們改進的動力！';

  @override
  String get theBestWeCanGet => '太棒了，滿分好評！';

  @override
  String get maybeLater => '稍後再說';

  @override
  String get rateNow => '現在去商店評分';

  @override
  String get cannotAddDirectory => '授權目錄沒有成功，請重試';

  @override
  String get directoryRemoved => '目錄授權已移除';

  @override
  String get appDescription => '智慧文檔處理。AI自動裁剪矯正，彰顯專業。';

  @override
  String get noRecentFiles => '您在本App中曾開啟過的PDF/Word/Excel/PPT檔案會顯示在這裡';

  @override
  String noFilesFoundForType(int count, String fileType) {
    return '找到 $count 個$fileType檔案';
  }

  @override
  String get selectFolder => '授權資料夾';

  @override
  String get selectFolderSubtitle => '授權本App尋找PDF/Word/Excel/PPT檔案目錄';

  @override
  String get photoToPdfSubtitle => '將簡報投影片或筆記掃描成一個 PDF';

  @override
  String get signatures => '簽名';

  @override
  String get manageSignatures => '管理您的手寫簽名';

  @override
  String get manageSignaturesHint =>
      '在這裡管理您的手寫簽名。點擊+號按鈕新增您的簽名。之後您可以在編輯PDF時使用這些簽名。';

  @override
  String get addSignature => '新增簽名';

  @override
  String get deleteSignature => '刪除簽名';

  @override
  String get confirmDeleteSignature => '確定要刪除這個簽名嗎？';

  @override
  String get createSignature => '建立簽名';

  @override
  String get saveSignature => '儲存簽名';

  @override
  String get strokeWidth => '畫筆粗細';

  @override
  String get color => '顏色';

  @override
  String get eraser => '橡皮擦';

  @override
  String get thin => '細';

  @override
  String get medium => '中';

  @override
  String get thick => '粗';

  @override
  String get signatureCreated => '簽名建立成功';

  @override
  String get signatureDeleted => '簽名已刪除';

  @override
  String get noSignatures => '暫無簽名';

  @override
  String get newSignature => '新簽名';

  @override
  String get signatureUsageHint => '您在編輯PDF時可以使用這些簽名';

  @override
  String get scanningFiles => '正在掃描您手機上的PDF/Word/Excel/PPT檔案';

  @override
  String pageRotated(int page, int degrees) {
    return '已旋轉第$page頁 ($degrees°)';
  }

  @override
  String get rotationFailed => '旋轉失敗';

  @override
  String get closeSearch => '關閉搜尋';

  @override
  String get previous => '上一個';

  @override
  String get next => '下一個';

  @override
  String get cancelSignatureEdit => '取消簽名編輯';

  @override
  String get back => '返回';

  @override
  String get editMode => '編輯模式';

  @override
  String get highlightText => '標記文字';

  @override
  String get draw => '繪製';

  @override
  String get signature => '簽名';

  @override
  String get highlightColor => '高亮顏色:';

  @override
  String get brushColor => '畫筆:';

  @override
  String get thickness => '粗細:';

  @override
  String get confirmPlacement => '確定放置';

  @override
  String get dragToAdjustHint => '拖動簽名調整位置，點擊確認放置';

  @override
  String get selectSignature => '選擇簽名';

  @override
  String get noSignaturesPleaseCreate => '暫無簽名，請先建立簽名';

  @override
  String pageIndicator(int current, int total) {
    return '第$current/$total頁';
  }

  @override
  String get androidDirectoryManagementSubtitle => '管理和添加可存取的資料夾';

  @override
  String get loadingDocument => '正在載入文件...';

  @override
  String get cannotOpenFile => '無法開啟文件';

  @override
  String get galleryAccessError => '無法存取所選圖片';

  @override
  String get permissionDenied => '存取相片權限被拒絕';

  @override
  String get invalidImageSelected => '選擇了無效的圖片';

  @override
  String get gallerySelectionFailed => '從相簿選擇圖片失敗';

  @override
  String get unexpectedError => '發生未預期的錯誤';

  @override
  String get importFailed => '匯入失敗';

  @override
  String get importResults => '匯入結果';

  @override
  String allImportsFailed(int count) {
    return '所有$count張圖片匯入失敗';
  }

  @override
  String partialImportSuccess(int successCount, int failCount) {
    return '$successCount張圖片匯入成功，$failCount張失敗';
  }

  @override
  String successfullyImported(int count) {
    return '✅ 成功匯入：$count張圖片';
  }

  @override
  String failedToImport(int count) {
    return '❌ 匯入失敗：$count張圖片';
  }

  @override
  String get tryAgainOrUseCamera => '請重試或使用相機';

  @override
  String get checkPermissionsInSettings => '請在設定中檢查應用程式權限';

  @override
  String get trySelectingDifferentImages => '請嘗試選擇其他圖片';

  @override
  String get selectedImagesCorruptedOrInaccessible =>
      '所選圖片可能已損壞或無法存取。請嘗試選擇其他圖片。';

  @override
  String get failedFiles => '失敗的檔案：';

  @override
  String andXMore(int count) {
    return '... 以及其他$count個';
  }

  @override
  String get tryAgain => '重試';

  @override
  String get details => '詳情';

  @override
  String get ok => '確定';

  @override
  String get galleryLoadFailed => '相簿載入失敗';

  @override
  String get fileSelectionFailed => '檔案選擇失敗';

  @override
  String get storagePermissionDenied => '儲存權限被拒絕';

  @override
  String get unsupportedFileType => '選擇了不支援的檔案類型';

  @override
  String get searchTitle => '搜尋';

  @override
  String get searchHint => '輸入檔案名稱';

  @override
  String get noResults => '暫無結果';

  @override
  String get waitForDocLoad => '請等待文檔完全加載後再準備PDF';

  @override
  String get generatingPdf => '正在生成PDF...';

  @override
  String get pdfGenerationFailed => 'PDF準備失敗，請重試';

  @override
  String pdfConversionFailed(String error) {
    return 'PDF轉換失敗: $error';
  }

  @override
  String get conversionSuccess => '轉換成功';

  @override
  String pdfSavedMessage(String fileName) {
    return '已另存為PDF檔案 $fileName，是否立即開啟查看？';
  }

  @override
  String get stayOnPage => '留在當前頁面';

  @override
  String get openPdf => '開啟PDF';

  @override
  String get generatePdfVersion => '生成PDF版本';

  @override
  String get generatePdfToEdit => '需要生成PDF版本才能編輯或旋轉。是否繼續？';

  @override
  String get continueAction => '繼續';

  @override
  String get openingCachedPdf => '正在開啟快取的PDF...';

  @override
  String get saveAsPdf => '另存為PDF';

  @override
  String get caseSensitive => '區分大小寫';

  @override
  String notFound(String query) {
    return '未找到 \"$query\"';
  }

  @override
  String get previousMatch => '上一個';

  @override
  String get nextMatch => '下一個';

  @override
  String get webViewOnlyIos => 'WebView文件檢視僅支援iOS';

  @override
  String webViewFailed(String error) {
    return 'WebView載入失敗: $error';
  }

  @override
  String get officeTimeout => 'Office文件載入超時。這可能是由於檔案過大或格式不支援。';

  @override
  String get addSlideshowButton => '新增投影片播放按鈕';

  @override
  String get threshold400px => '閾值：400像素';

  @override
  String get confirmClear => '確認清除';

  @override
  String get confirmClearSignature => '這將整個清除您的簽名圖像。您確定要清除嗎？';

  @override
  String get clear => '清除';

  @override
  String iosNetworkPermissionGuide(String appName) {
    return '請在iPhone設定中找到\"$appName\",確保允許網路存取。\n設定路徑：設定 > 隱私權與安全性 > 本地網路 > $appName';
  }

  @override
  String get checkNetworkConnection => '請檢查您的網路連線';

  @override
  String get networkPermission => '網路權限';

  @override
  String get networkPermissionRequest => '此應用程式需要存取網路才能處理和增強PDF文件中的影像。';

  @override
  String get featuresInclude => '功能包括：';

  @override
  String get imageClarityEnhancement => '影像清晰度增強';

  @override
  String get documentEdgeDetection => '文件邊緣偵測';

  @override
  String get imageOptimization => '影像最佳化';

  @override
  String get requestPermission => '要求權限';

  @override
  String get networkConnectionProblem => '網路連線問題';

  @override
  String get networkStatusDescription => '網路連線不可用，請檢查您的網路設定';

  @override
  String get troubleshootingSuggestions => '疑難排解建議：';

  @override
  String get networkTroubleshootingTips =>
      '檢查Wi-Fi或行動數據是否已啟用\n確認裝置已連線到可用的網路\n檢查是否處於飛航模式\n嘗試存取其他應用程式或網站以確認網路狀態\n重新啟動網路連線或裝置';

  @override
  String get retry => '重試';

  @override
  String get unfavorite => '取消收藏';

  @override
  String get open => '開啟';

  @override
  String get rename => '重新命名';

  @override
  String get cannotAccessFile => '無法存取此檔案。請在「已授權目錄」中重新授權或重新選擇檔案。';

  @override
  String get installOfficeAppTitle => '需要安裝App才能開啟Office檔案';

  @override
  String get installOfficeAppContent => '需要安裝一個應用程式才能開啟此檔案。是否從應用程式商店安裝？';

  @override
  String get install => '安裝';

  @override
  String get renameFile => '重新命名';

  @override
  String get renamed => '已重新命名';

  @override
  String renameFailed(String error) {
    return '重新命名失敗：$error';
  }

  @override
  String confirmDeleteFile(String fileName) {
    return '確認刪除檔案：\n$fileName?';
  }

  @override
  String get deleted => '已刪除';

  @override
  String deleteFailed(String error) {
    return '刪除失敗：$error';
  }

  @override
  String get networkConnected => '網路已連接';

  @override
  String get networkDisconnected => '網路已斷開';

  @override
  String get networkAccessDenied => '網路訪問被拒絕';

  @override
  String get networkOperationFailed => '網路操作失敗';

  @override
  String get connected => '已連線';

  @override
  String get noNetwork => '無網路';

  @override
  String get thumbnailsOnlyIos => '縮圖僅在 iOS 上可用';

  @override
  String get waitDocumentComplete => '等待文件載入完成...';

  @override
  String get thumbnailDescription => '完整文件的縮圖\n方便快速導航';

  @override
  String get generatingThumbnail => '正在生成完整文件縮圖...';

  @override
  String get analyzingDocument => '正在分析文件結構和尺寸';

  @override
  String get thumbnailUnavailable => '縮圖暫時無法顯示';

  @override
  String get clickRefreshRetry => '點擊右上角的重新整理按鈕重試';
}
