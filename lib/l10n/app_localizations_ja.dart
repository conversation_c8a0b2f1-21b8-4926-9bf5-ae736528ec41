// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get appName => 'すべてのPDFエディタ';

  @override
  String get documents => 'ドキュメント';

  @override
  String get tools => 'Tools';

  @override
  String get settings => '設定';

  @override
  String get home => 'Home';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get files => 'Files';

  @override
  String get photoToPdf => '写真からPDFへ';

  @override
  String get imageToPdf => '画像をPDFに変換';

  @override
  String get addImages => 'Add Images';

  @override
  String addImagesFromGallery(int count) {
    return 'ギャラリーから$count枚の画像を追加';
  }

  @override
  String addImagesFromFiles(int count) {
    return 'Add $count images from files';
  }

  @override
  String get convert => '変換';

  @override
  String get undo => '元に戻す';

  @override
  String get discardChangesTitle => '変更を破棄しますか？';

  @override
  String get discardChangesContent => '保存されていない変更があります。今終了すると、変更は失われます。';

  @override
  String get keepEditing => '編集を続ける';

  @override
  String get discard => '破棄';

  @override
  String get takePicture => '写真を撮る';

  @override
  String get reorderImage => '画像を並べ替え';

  @override
  String get cropImage => '画像をトリミング';

  @override
  String get deleteImage => '画像を削除';

  @override
  String get confirmDeleteImage => 'この画像を削除してもよろしいですか？';

  @override
  String get cancel => 'キャンセル';

  @override
  String get delete => '削除';

  @override
  String confirmUndo(String action) {
    return '元に戻す：$action。これにより、最後のアクションが元に戻ります。続行しますか？';
  }

  @override
  String get pdfSavedSuccessfully => 'PDFが正常に保存されました';

  @override
  String get pdfSaveFailed => 'PDFの保存に失敗しました';

  @override
  String get saveAs => '名前を付けて保存';

  @override
  String get fileName => 'ファイル名';

  @override
  String get save => '保存';

  @override
  String get scanPrefix => 'スキャン';

  @override
  String get addMoreImages => 'さらに画像を追加';

  @override
  String get takePhoto => '写真を撮る';

  @override
  String get importFromAlbum => 'アルバムからインポート';

  @override
  String get importFromOtherApps => 'Import from Other Apps';

  @override
  String get aiProcessing => 'AI Processing';

  @override
  String get lowResolution => 'Low Resolution';

  @override
  String imagePreviewTitle(int currentPage, int totalPages) {
    return 'Image $currentPage of $totalPages';
  }

  @override
  String get confirmExit => 'Confirm Exit';

  @override
  String get discardCropChangesContent =>
      'You haven\'t saved. Are you sure you want to discard the manually adjusted coordinates?';

  @override
  String get continueAdjusting => 'Cancel';

  @override
  String get discardChangesConfirmation => 'Confirm';

  @override
  String get restoreDefault => 'Restore Default';

  @override
  String get confirmRestoreDefaultContent =>
      'This action will discard your manually adjusted coordinates. Do you want to discard your recent adjustments?';

  @override
  String get imageNotLoadedError =>
      'Image not loaded yet, please try again later.';

  @override
  String get imageProcessingFailedError =>
      'Image processing failed, please try again.';

  @override
  String get dragCornersHint =>
      'Drag the blue dots to adjust the four corners of the crop area.';

  @override
  String get splashTitle => '写真PDF変換マジックツール';

  @override
  String get splashSubtitle => '会議写真を自動整理、ワンタップでトリミング・PDF化';

  @override
  String get splashOriginalPhoto => '元の写真';

  @override
  String get splashGeneratedPdf => '自動生成PDF';

  @override
  String get splashWorkflow => 'アプリを起動中、お待ちください...';

  @override
  String get recent => '最近';

  @override
  String get favorite => 'お気に入り';

  @override
  String loadedFiles(int count) {
    return '$count個のファイルをスキャンしました';
  }

  @override
  String get cannotLoadDirectoryFiles => 'ディレクトリファイルを読み込めません';

  @override
  String get cannotSelectFiles => 'ファイルを選択できません、権限を確認してください';

  @override
  String get directorySelectionOnlyMobile => 'ディレクトリの選択はiOS/Androidでのみ利用可能です';

  @override
  String get selectingDirectory => 'ディレクトリをスキャン中...';

  @override
  String get cannotSelectDirectory => 'ディレクトリを選択できません、権限を確認してください';

  @override
  String get authorizeFolder => 'フォルダを承認';

  @override
  String get sort => '並べ替え';

  @override
  String get nameSort => '名前';

  @override
  String get lastModified => '最終更新日';

  @override
  String get sizeSort => 'サイズ';

  @override
  String get descending => '降順';

  @override
  String get apply => '適用';

  @override
  String get originalPhotosFolder => 'Original Photos';

  @override
  String get searchInPdf => 'Search in PDF';

  @override
  String get searchText => 'Search text';

  @override
  String get search => 'Search';

  @override
  String get unsavedChanges => 'Unsaved Changes';

  @override
  String get unsavedChangesMessage =>
      'You have unsaved changes. Are you sure you want to exit?';

  @override
  String get discardChanges => 'Discard Changes';

  @override
  String get rotate => 'Rotate';

  @override
  String get edit => 'Edit';

  @override
  String get thumbnails => 'Thumbnails';

  @override
  String get pdfSaved => 'PDF saved successfully';

  @override
  String selectAuthorizedDirectorySubtitle(String appName, String deviceType) {
    return 'iPhone/iPadファイルディレクトリへのアクセスを承認する';
  }

  @override
  String get shareAppSubtitle => 'PDFマジックツールをおすすめします：';

  @override
  String get shareAppSettingsSubtitle => 'Recommend this app to your friends';

  @override
  String get weLikeYouToo => 'ご愛用ありがとうございます！';

  @override
  String get thankYouForFeedback => '満点評価が改善の原動力です！';

  @override
  String get theBestWeCanGet => '素晴らしい、満点評価！';

  @override
  String get maybeLater => '後で';

  @override
  String get rateNow => '今すぐストアで評価';

  @override
  String get languageSettings => 'UI言語';

  @override
  String get selectAuthorizedDirectory => '承認済みディレクトリの管理';

  @override
  String get shareApp => '共有';

  @override
  String get rateApp => '評価';

  @override
  String get rateAppSubtitle => 'App Storeで評価してください';

  @override
  String get aboutApp => '概要';

  @override
  String get aboutAppSubtitle => 'アプリのバージョン、ポリシーなど';

  @override
  String get systemLanguage => 'システム言語';

  @override
  String currentLanguage(String languageName, Object language) {
    return '現在：$language';
  }

  @override
  String get selectLanguage => '言語を選択';

  @override
  String defaultAppName(int number) {
    return 'アプリ$number';
  }

  @override
  String get cannotAddDirectory => 'ディレクトリ認証に失敗しました、再試行してください';

  @override
  String get directoryRemoved => 'ディレクトリ認証が削除されました';

  @override
  String get cancelAuthorization => '承認のキャンセル';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return '$appNameの$directoryNameへのアクセス承認を本当にキャンセルしますか？キャンセル後、このディレクトリ内のファイルは表示されなくなります。';
  }

  @override
  String get noKeepIt => 'いいえ、保持します';

  @override
  String get yesRemoveFolder => 'はい、このフォルダはもう必要ありません';

  @override
  String filesCount(int count) {
    return '$count個のファイル';
  }

  @override
  String directorySize(String size) {
    return 'サイズ：$size';
  }

  @override
  String get editName => '名前を編集';

  @override
  String get removeDirectory => '削除';

  @override
  String get noAuthorizedDirectories => '承認済みのディレクトリがありません';

  @override
  String get noAuthorizedDirectoriesSubtitle =>
      'これらの場所からファイルにアクセスするには、ディレクトリを追加してください';

  @override
  String get addDirectory => 'ディレクトリを追加';

  @override
  String get authorizedDirectories => '承認済みディレクトリの管理';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appNameは次のディレクトリにアクセスできます：';
  }

  @override
  String get nameForNewFolder => '後で簡単に識別できるように、選択したアプリフォルダに名前を付けてください';

  @override
  String get enterCustomName => 'カスタム名を入力';

  @override
  String get customName => 'カスタム名';

  @override
  String get version => 'バージョン';

  @override
  String get appVersion => '1.0.0';

  @override
  String get appDescription => 'スマート文書処理。AI自動トリミング・補正でプロフェッショナルを演出。';

  @override
  String get privacyPolicy => 'プライバシーポリシー';

  @override
  String get termsOfService => '利用規約';

  @override
  String get copyright => '© 2025';

  @override
  String get noFavoriteFiles => 'お気に入りのファイルはまだありません';

  @override
  String get noFavoriteFilesHint =>
      'ドキュメントページのファイルの横にある☆スターアイコンをタップしてお気に入りに登録できます';

  @override
  String get fileNotFound => 'ファイルが見つかりません';

  @override
  String get noRecentFiles => 'このアプリで開いたPDF/Word/Excel/PPTファイルがここに表示されます';

  @override
  String noFilesFoundForType(int count, String fileType) {
    return '$fileTypeファイルが$count件見つかりました';
  }

  @override
  String get selectFolder => 'フォルダを承認';

  @override
  String get selectFolderSubtitle =>
      'このアプリがPDF/Word/Excel/PPTファイルディレクトリを見つけることを承認する';

  @override
  String get photoToPdfSubtitle => 'プレゼンテーションのスライドやメモを1つのPDFにスキャンします';

  @override
  String get mergeImagesToPdf => '画像をPDFにマージ';

  @override
  String get mergeImagesToPdfSubtitle => 'アルバムの写真をPDFにマージ';

  @override
  String get fabTutorial =>
      'ここをクリックして、カメラでドキュメントをスキャンしたり、ギャラリーの画像をPDFに結合したりします。';

  @override
  String get iGotIt => 'わかった';

  @override
  String get signatures => '署名';

  @override
  String get manageSignatures => '手書き署名の管理';

  @override
  String get manageSignaturesHint =>
      'ここで手書き署名を管理します。+ボタンをタップして署名を追加してください。その後PDF編集時にこれらの署名を使用できます。';

  @override
  String get addSignature => '署名を追加';

  @override
  String get deleteSignature => '署名を削除';

  @override
  String get confirmDeleteSignature => 'この署名を削除してもよろしいですか？';

  @override
  String get createSignature => '署名を作成';

  @override
  String get saveSignature => '署名を保存';

  @override
  String get strokeWidth => '線の太さ';

  @override
  String get color => '色';

  @override
  String get eraser => '消しゴム';

  @override
  String get thin => '細い';

  @override
  String get medium => '中';

  @override
  String get thick => '太い';

  @override
  String get signatureCreated => '署名が作成されました';

  @override
  String get editSignature => 'Edit Signature';

  @override
  String get signatureUpdated => 'Signature updated successfully';

  @override
  String get signatureDeleted => '署名が削除されました';

  @override
  String get noSignatures => '署名がありません';

  @override
  String get newSignature => '新しい署名';

  @override
  String get signatureUsageHint => 'PDF編集時にこれらの署名を使用できます';

  @override
  String get scanningFiles => 'デバイス上のPDF/Word/Excel/PPTファイルをスキャン中';

  @override
  String pageRotated(int page, int degrees) {
    return 'ページ $page を $degrees° 回転しました';
  }

  @override
  String get rotationFailed => '回転に失敗しました';

  @override
  String get closeSearch => '検索を閉じる';

  @override
  String get previous => '前へ';

  @override
  String get next => '次へ';

  @override
  String get cancelSignatureEdit => '署名編集をキャンセル';

  @override
  String get back => '戻る';

  @override
  String get editMode => '編集モード';

  @override
  String get highlightText => 'テキストをハイライト';

  @override
  String get draw => '描画';

  @override
  String get signature => '署名';

  @override
  String get highlightColor => 'ハイライト色';

  @override
  String get brushColor => 'ブラシの色';

  @override
  String get thickness => '太さ';

  @override
  String get confirmPlacement => '配置を確認';

  @override
  String get dragToAdjustHint => 'ドラッグして調整';

  @override
  String get selectSignature => '署名を選択';

  @override
  String get noSignaturesPleaseCreate => '署名がありません、作成してください';

  @override
  String pageIndicator(int current, int total) {
    return 'ページ $current/$total';
  }

  @override
  String get androidDirectoryManagementSubtitle => 'アクセス可能なフォルダーを管理して追加する';

  @override
  String get loadingDocument => 'ドキュメントを読み込み中...';

  @override
  String get cannotOpenFile => 'ファイルを開けません';

  @override
  String get galleryAccessError => '選択した画像にアクセスできません';

  @override
  String get permissionDenied => '写真へのアクセス権限が拒否されました';

  @override
  String get invalidImageSelected => '無効な画像が選択されました';

  @override
  String get gallerySelectionFailed => 'ギャラリーから画像の選択に失敗しました';

  @override
  String get unexpectedError => '予期しないエラーが発生しました';

  @override
  String get importFailed => 'インポートに失敗しました';

  @override
  String get importResults => 'インポート結果';

  @override
  String allImportsFailed(int count) {
    return '選択した$count枚の画像すべてのインポートに失敗しました';
  }

  @override
  String partialImportSuccess(int successCount, int failCount) {
    return '$successCount枚の画像のインポートに成功、$failCount枚失敗';
  }

  @override
  String successfullyImported(int count) {
    return '✅ インポート成功：$count枚の画像';
  }

  @override
  String failedToImport(int count) {
    return '❌ インポート失敗：$count枚の画像';
  }

  @override
  String get tryAgainOrUseCamera => 'もう一度試すかカメラを使用してください';

  @override
  String get checkPermissionsInSettings => '設定でアプリの権限を確認してください';

  @override
  String get trySelectingDifferentImages => '別の画像を選択してください';

  @override
  String get selectedImagesCorruptedOrInaccessible =>
      '選択した画像が破損しているかアクセスできない可能性があります。別の画像を選択してください。';

  @override
  String get failedFiles => '失敗したファイル：';

  @override
  String andXMore(int count) {
    return '... および他$count件';
  }

  @override
  String get tryAgain => '再試行';

  @override
  String get details => '詳細';

  @override
  String get ok => 'OK';

  @override
  String get galleryLoadFailed => 'ギャラリーの読み込みに失敗しました';

  @override
  String get fileSelectionFailed => 'ファイルの選択に失敗しました';

  @override
  String get storagePermissionDenied => 'ストレージ権限が拒否されました';

  @override
  String get unsupportedFileType => 'サポートされていないファイル形式が選択されました';

  @override
  String get playSlideshow => 'Play Slideshow';

  @override
  String get slideshowMode => 'Slideshow Mode';

  @override
  String get exitSlideshow => 'Exit Slideshow';

  @override
  String get searchTitle => '検索';

  @override
  String get searchHint => 'ファイル名を入力';

  @override
  String get noResults => '結果がありません';

  @override
  String iosPermissionMessage(String deviceType) {
    return 'iPhoneの権限制限により、PDF、Word、Excel、PPTファイルを含むフォルダへのアクセスを最初に承認する必要があります。';
  }

  @override
  String get waitForDocLoad => 'PDFを準備する前に、ドキュメントが完全に読み込まれるまでお待ちください';

  @override
  String get generatingPdf => 'PDFを生成中...';

  @override
  String get pdfGenerationFailed => 'PDFの準備に失敗しました、もう一度お試しください';

  @override
  String pdfConversionFailed(String error) {
    return 'PDF変換に失敗しました: $error';
  }

  @override
  String get conversionSuccess => '変換に成功しました';

  @override
  String pdfSavedMessage(String fileName) {
    return 'PDFファイル$fileNameとして保存しました、今すぐ開いて表示しますか？';
  }

  @override
  String get stayOnPage => 'ページにとどまる';

  @override
  String get openPdf => 'PDFを開く';

  @override
  String get generatePdfVersion => 'PDFバージョンを生成';

  @override
  String get generatePdfToEdit => '編集または回転するにはPDFバージョンを生成する必要があります。続行しますか？';

  @override
  String get continueAction => '続行';

  @override
  String get openingCachedPdf => 'キャッシュされたPDFを開いています...';

  @override
  String get saveAsPdf => 'PDFとして保存';

  @override
  String get caseSensitive => '大文字と小文字を区別する';

  @override
  String notFound(String query) {
    return '「$query」は見つかりませんでした';
  }

  @override
  String get previousMatch => '前へ';

  @override
  String get nextMatch => '次へ';

  @override
  String get webViewOnlyIos => 'WebViewドキュメントの表示はiOSでのみサポートされています';

  @override
  String webViewFailed(String error) {
    return 'WebViewの読み込みに失敗しました: $error';
  }

  @override
  String get officeTimeout =>
      'Officeドキュメントの読み込みがタイムアウトしました。ファイルサイズが大きいか、サポートされていない形式である可能性があります。';

  @override
  String get addSlideshowButton => 'スライドショーボタンを追加';

  @override
  String get threshold400px => 'しきい値：400px';

  @override
  String get confirmClear => 'クリアを確認';

  @override
  String get confirmClearSignature => 'これにより、署名画像全体がクリアされます。クリアしてもよろしいですか？';

  @override
  String get clear => 'クリア';

  @override
  String iosNetworkPermissionGuide(String appName) {
    return 'iPhoneの設定で「$appName」を見つけ、ネットワークアクセスが許可されていることを確認してください。パス：設定 > プライバシーとセキュリティ > ローカルネットワーク > $appName';
  }

  @override
  String get checkNetworkConnection => 'ネットワーク接続を確認してください';

  @override
  String get networkPermission => 'ネットワーク権限';

  @override
  String get networkPermissionRequest =>
      'このアプリは、PDFドキュメント内の画像を処理および強化するためにネットワークにアクセスする必要があります。';

  @override
  String get featuresInclude => '機能は次のとおりです。';

  @override
  String get imageClarityEnhancement => '画像の鮮明度の向上';

  @override
  String get documentEdgeDetection => 'ドキュメントの端の検出';

  @override
  String get imageOptimization => '画像の最適化';

  @override
  String get requestPermission => '権限を要求';

  @override
  String get networkConnectionProblem => 'ネットワーク接続の問題';

  @override
  String get networkStatusDescription => 'ネットワーク接続が利用できません。ネットワーク設定を確認してください';

  @override
  String get troubleshootingSuggestions => 'トラブルシューティングの提案：';

  @override
  String get networkTroubleshootingTips =>
      'Wi-Fiまたはモバイルデータが有効になっているか確認してください\nデバイスが使用可能なネットワークに接続されていることを確認してください\n機内モードがオンになっていないか確認してください\n他のアプリやウェブサイトにアクセスしてネットワークの状態を確認してください\nネットワーク接続またはデバイスを再起動してください';

  @override
  String get retry => '再試行';

  @override
  String get unfavorite => 'Unfavorite';

  @override
  String get open => 'Open';

  @override
  String get rename => 'Rename';

  @override
  String get cannotAccessFile =>
      'Cannot access this file. Please reauthorize in \"Authorized Directories\" or reselect the file.';

  @override
  String get installOfficeAppTitle => 'App required to open Office files';

  @override
  String get installOfficeAppContent =>
      'An app is required to open this file. Install from the app store?';

  @override
  String get install => 'Install';

  @override
  String get renameFile => 'Rename';

  @override
  String get renamed => 'Renamed';

  @override
  String renameFailed(String error) {
    return 'Rename failed: $error';
  }

  @override
  String confirmDeleteFile(String fileName) {
    return 'Confirm delete file:\n$fileName?';
  }

  @override
  String get deleted => 'Deleted';

  @override
  String deleteFailed(String error) {
    return 'Delete failed: $error';
  }

  @override
  String get networkConnected => 'ネットワークが接続されました';

  @override
  String get networkDisconnected => 'ネットワークが切断されました';

  @override
  String get networkAccessDenied => 'ネットワークアクセスが拒否されました';

  @override
  String get networkOperationFailed => 'ネットワーク操作に失敗しました';

  @override
  String get connected => 'Connected';

  @override
  String get noNetwork => 'No network';

  @override
  String get thumbnailsOnlyIos => 'サムネイルはiOSでのみ利用可能です';

  @override
  String get waitDocumentComplete => 'ドキュメントの読み込み完了を待っています...';

  @override
  String get thumbnailDescription => '完全なドキュメントのサムネイル\n素早いナビゲーションに便利';

  @override
  String get generatingThumbnail => '完全なドキュメントのサムネイルを生成中...';

  @override
  String get analyzingDocument => 'ドキュメントの構造とサイズを分析中';

  @override
  String get thumbnailUnavailable => 'サムネイルは一時的に表示できません';

  @override
  String get clickRefreshRetry => '右上の更新ボタンをクリックして再試行してください';
}
