import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_de.dart';
import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_ja.dart';
import 'app_localizations_ko.dart';
import 'app_localizations_pt.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('de'),
    Locale('en'),
    Locale('es'),
    Locale('fr'),
    Locale('ja'),
    Locale('ko'),
    Locale('pt'),
    Locale('zh'),
    Locale('zh', 'TW'),
  ];

  /// No description provided for @appName.
  ///
  /// In en, this message translates to:
  /// **'All PDF Editor'**
  String get appName;

  /// No description provided for @documents.
  ///
  /// In en, this message translates to:
  /// **'Documents'**
  String get documents;

  /// No description provided for @tools.
  ///
  /// In en, this message translates to:
  /// **'Tools'**
  String get tools;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @camera.
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// No description provided for @gallery.
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// No description provided for @files.
  ///
  /// In en, this message translates to:
  /// **'Files'**
  String get files;

  /// No description provided for @photoToPdf.
  ///
  /// In en, this message translates to:
  /// **'Scan to PDF'**
  String get photoToPdf;

  /// No description provided for @imageToPdf.
  ///
  /// In en, this message translates to:
  /// **'Image to PDF'**
  String get imageToPdf;

  /// No description provided for @addImages.
  ///
  /// In en, this message translates to:
  /// **'Add Images'**
  String get addImages;

  /// No description provided for @addImagesFromGallery.
  ///
  /// In en, this message translates to:
  /// **'Add {count} images from gallery'**
  String addImagesFromGallery(int count);

  /// No description provided for @addImagesFromFiles.
  ///
  /// In en, this message translates to:
  /// **'Add {count} images from files'**
  String addImagesFromFiles(int count);

  /// No description provided for @convert.
  ///
  /// In en, this message translates to:
  /// **'Convert'**
  String get convert;

  /// No description provided for @undo.
  ///
  /// In en, this message translates to:
  /// **'Undo'**
  String get undo;

  /// No description provided for @discardChangesTitle.
  ///
  /// In en, this message translates to:
  /// **'Discard changes?'**
  String get discardChangesTitle;

  /// No description provided for @discardChangesContent.
  ///
  /// In en, this message translates to:
  /// **'You have unsaved changes. Are you sure you want to exit?'**
  String get discardChangesContent;

  /// No description provided for @keepEditing.
  ///
  /// In en, this message translates to:
  /// **'Keep Editing'**
  String get keepEditing;

  /// No description provided for @discard.
  ///
  /// In en, this message translates to:
  /// **'Discard'**
  String get discard;

  /// No description provided for @takePicture.
  ///
  /// In en, this message translates to:
  /// **'Take a picture'**
  String get takePicture;

  /// No description provided for @reorderImage.
  ///
  /// In en, this message translates to:
  /// **'Reorder image'**
  String get reorderImage;

  /// No description provided for @cropImage.
  ///
  /// In en, this message translates to:
  /// **'Crop Image'**
  String get cropImage;

  /// No description provided for @deleteImage.
  ///
  /// In en, this message translates to:
  /// **'Delete Image'**
  String get deleteImage;

  /// No description provided for @confirmDeleteImage.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this image?'**
  String get confirmDeleteImage;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @confirmUndo.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to undo \"{action}\"?'**
  String confirmUndo(String action);

  /// No description provided for @pdfSavedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'PDF saved successfully'**
  String get pdfSavedSuccessfully;

  /// No description provided for @pdfSaveFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to save PDF'**
  String get pdfSaveFailed;

  /// No description provided for @saveAs.
  ///
  /// In en, this message translates to:
  /// **'Save As'**
  String get saveAs;

  /// No description provided for @fileName.
  ///
  /// In en, this message translates to:
  /// **'File Name'**
  String get fileName;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @scanPrefix.
  ///
  /// In en, this message translates to:
  /// **'Scan'**
  String get scanPrefix;

  /// No description provided for @addMoreImages.
  ///
  /// In en, this message translates to:
  /// **'Add More Images'**
  String get addMoreImages;

  /// No description provided for @takePhoto.
  ///
  /// In en, this message translates to:
  /// **'Take Photo'**
  String get takePhoto;

  /// No description provided for @importFromAlbum.
  ///
  /// In en, this message translates to:
  /// **'Import from Album'**
  String get importFromAlbum;

  /// No description provided for @importFromOtherApps.
  ///
  /// In en, this message translates to:
  /// **'Import from Other Apps'**
  String get importFromOtherApps;

  /// No description provided for @aiProcessing.
  ///
  /// In en, this message translates to:
  /// **'AI Processing'**
  String get aiProcessing;

  /// No description provided for @lowResolution.
  ///
  /// In en, this message translates to:
  /// **'Low Resolution'**
  String get lowResolution;

  /// Title for the image preview screen showing the current image number and total count
  ///
  /// In en, this message translates to:
  /// **'Image {currentPage} of {totalPages}'**
  String imagePreviewTitle(int currentPage, int totalPages);

  /// No description provided for @confirmExit.
  ///
  /// In en, this message translates to:
  /// **'Confirm Exit'**
  String get confirmExit;

  /// No description provided for @discardCropChangesContent.
  ///
  /// In en, this message translates to:
  /// **'You haven\'t saved. Are you sure you want to discard the manually adjusted coordinates?'**
  String get discardCropChangesContent;

  /// No description provided for @continueAdjusting.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get continueAdjusting;

  /// No description provided for @discardChangesConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get discardChangesConfirmation;

  /// No description provided for @restoreDefault.
  ///
  /// In en, this message translates to:
  /// **'Restore Default'**
  String get restoreDefault;

  /// No description provided for @confirmRestoreDefaultContent.
  ///
  /// In en, this message translates to:
  /// **'This action will discard your manually adjusted coordinates. Do you want to discard your recent adjustments?'**
  String get confirmRestoreDefaultContent;

  /// No description provided for @imageNotLoadedError.
  ///
  /// In en, this message translates to:
  /// **'Image not loaded yet, please try again later.'**
  String get imageNotLoadedError;

  /// No description provided for @imageProcessingFailedError.
  ///
  /// In en, this message translates to:
  /// **'Image processing failed, please try again.'**
  String get imageProcessingFailedError;

  /// No description provided for @dragCornersHint.
  ///
  /// In en, this message translates to:
  /// **'Drag the blue dots to adjust the four corners of the crop area.'**
  String get dragCornersHint;

  /// No description provided for @splashTitle.
  ///
  /// In en, this message translates to:
  /// **'Photo to PDF Magic Tool'**
  String get splashTitle;

  /// No description provided for @splashSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Auto organize meeting photos, crop and merge into PDF with one tap'**
  String get splashSubtitle;

  /// No description provided for @splashOriginalPhoto.
  ///
  /// In en, this message translates to:
  /// **'Original Photo'**
  String get splashOriginalPhoto;

  /// No description provided for @splashGeneratedPdf.
  ///
  /// In en, this message translates to:
  /// **'Auto Generated PDF'**
  String get splashGeneratedPdf;

  /// No description provided for @splashWorkflow.
  ///
  /// In en, this message translates to:
  /// **'App is starting, please wait...'**
  String get splashWorkflow;

  /// No description provided for @recent.
  ///
  /// In en, this message translates to:
  /// **'Recent'**
  String get recent;

  /// No description provided for @favorite.
  ///
  /// In en, this message translates to:
  /// **'Favorites'**
  String get favorite;

  /// No description provided for @loadedFiles.
  ///
  /// In en, this message translates to:
  /// **'Loaded {count} files'**
  String loadedFiles(int count);

  /// No description provided for @cannotLoadDirectoryFiles.
  ///
  /// In en, this message translates to:
  /// **'Cannot load directory files'**
  String get cannotLoadDirectoryFiles;

  /// No description provided for @cannotSelectFiles.
  ///
  /// In en, this message translates to:
  /// **'Cannot select files'**
  String get cannotSelectFiles;

  /// No description provided for @directorySelectionOnlyMobile.
  ///
  /// In en, this message translates to:
  /// **'Directory selection is only available on mobile'**
  String get directorySelectionOnlyMobile;

  /// No description provided for @selectingDirectory.
  ///
  /// In en, this message translates to:
  /// **'Selecting directory...'**
  String get selectingDirectory;

  /// No description provided for @cannotSelectDirectory.
  ///
  /// In en, this message translates to:
  /// **'Cannot select directory'**
  String get cannotSelectDirectory;

  /// No description provided for @authorizeFolder.
  ///
  /// In en, this message translates to:
  /// **'Authorize Folder'**
  String get authorizeFolder;

  /// No description provided for @sort.
  ///
  /// In en, this message translates to:
  /// **'Sort'**
  String get sort;

  /// No description provided for @nameSort.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get nameSort;

  /// No description provided for @lastModified.
  ///
  /// In en, this message translates to:
  /// **'Last Modified'**
  String get lastModified;

  /// No description provided for @sizeSort.
  ///
  /// In en, this message translates to:
  /// **'Size'**
  String get sizeSort;

  /// No description provided for @descending.
  ///
  /// In en, this message translates to:
  /// **'Descending'**
  String get descending;

  /// No description provided for @apply.
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// No description provided for @originalPhotosFolder.
  ///
  /// In en, this message translates to:
  /// **'Original Photos'**
  String get originalPhotosFolder;

  /// No description provided for @searchInPdf.
  ///
  /// In en, this message translates to:
  /// **'Search in PDF'**
  String get searchInPdf;

  /// No description provided for @searchText.
  ///
  /// In en, this message translates to:
  /// **'Search text'**
  String get searchText;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @unsavedChanges.
  ///
  /// In en, this message translates to:
  /// **'Unsaved Changes'**
  String get unsavedChanges;

  /// No description provided for @unsavedChangesMessage.
  ///
  /// In en, this message translates to:
  /// **'You have unsaved changes. Are you sure you want to exit?'**
  String get unsavedChangesMessage;

  /// No description provided for @discardChanges.
  ///
  /// In en, this message translates to:
  /// **'Discard Changes'**
  String get discardChanges;

  /// No description provided for @rotate.
  ///
  /// In en, this message translates to:
  /// **'Rotate'**
  String get rotate;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @thumbnails.
  ///
  /// In en, this message translates to:
  /// **'Thumbnails'**
  String get thumbnails;

  /// No description provided for @pdfSaved.
  ///
  /// In en, this message translates to:
  /// **'PDF saved successfully'**
  String get pdfSaved;

  /// No description provided for @selectAuthorizedDirectorySubtitle.
  ///
  /// In en, this message translates to:
  /// **'Select a directory for {appName} to access on your {deviceType}'**
  String selectAuthorizedDirectorySubtitle(String appName, String deviceType);

  /// No description provided for @shareAppSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Recommend you a PDF magic tool:'**
  String get shareAppSubtitle;

  /// No description provided for @shareAppSettingsSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Recommend this app to your friends'**
  String get shareAppSettingsSubtitle;

  /// No description provided for @weLikeYouToo.
  ///
  /// In en, this message translates to:
  /// **'Thank you for your love!'**
  String get weLikeYouToo;

  /// No description provided for @thankYouForFeedback.
  ///
  /// In en, this message translates to:
  /// **'Your perfect score motivates us to improve!'**
  String get thankYouForFeedback;

  /// No description provided for @theBestWeCanGet.
  ///
  /// In en, this message translates to:
  /// **'Your feedback is the best we can get!'**
  String get theBestWeCanGet;

  /// No description provided for @maybeLater.
  ///
  /// In en, this message translates to:
  /// **'Maybe Later'**
  String get maybeLater;

  /// No description provided for @rateNow.
  ///
  /// In en, this message translates to:
  /// **'Rate Now'**
  String get rateNow;

  /// No description provided for @languageSettings.
  ///
  /// In en, this message translates to:
  /// **'Language Settings'**
  String get languageSettings;

  /// No description provided for @selectAuthorizedDirectory.
  ///
  /// In en, this message translates to:
  /// **'Select Authorized Directory'**
  String get selectAuthorizedDirectory;

  /// No description provided for @shareApp.
  ///
  /// In en, this message translates to:
  /// **'Share App'**
  String get shareApp;

  /// No description provided for @rateApp.
  ///
  /// In en, this message translates to:
  /// **'Rate App'**
  String get rateApp;

  /// No description provided for @rateAppSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Rate us in the App Store'**
  String get rateAppSubtitle;

  /// No description provided for @aboutApp.
  ///
  /// In en, this message translates to:
  /// **'About App'**
  String get aboutApp;

  /// No description provided for @aboutAppSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Version, legal information'**
  String get aboutAppSubtitle;

  /// No description provided for @systemLanguage.
  ///
  /// In en, this message translates to:
  /// **'System Language'**
  String get systemLanguage;

  /// No description provided for @currentLanguage.
  ///
  /// In en, this message translates to:
  /// **'{languageName} (Current)'**
  String currentLanguage(String languageName, Object language);

  /// No description provided for @selectLanguage.
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// No description provided for @defaultAppName.
  ///
  /// In en, this message translates to:
  /// **'App {number}'**
  String defaultAppName(int number);

  /// No description provided for @cannotAddDirectory.
  ///
  /// In en, this message translates to:
  /// **'Directory authorization failed, please try again'**
  String get cannotAddDirectory;

  /// No description provided for @directoryRemoved.
  ///
  /// In en, this message translates to:
  /// **'Directory authorization removed'**
  String get directoryRemoved;

  /// No description provided for @cancelAuthorization.
  ///
  /// In en, this message translates to:
  /// **'Cancel Authorization'**
  String get cancelAuthorization;

  /// No description provided for @confirmCancelAuthorization.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to remove access to \"{directoryName}\" for {appName}?'**
  String confirmCancelAuthorization(String appName, String directoryName);

  /// No description provided for @noKeepIt.
  ///
  /// In en, this message translates to:
  /// **'No, Keep It'**
  String get noKeepIt;

  /// No description provided for @yesRemoveFolder.
  ///
  /// In en, this message translates to:
  /// **'Yes, Remove Folder'**
  String get yesRemoveFolder;

  /// No description provided for @filesCount.
  ///
  /// In en, this message translates to:
  /// **'{count} files'**
  String filesCount(int count);

  /// No description provided for @directorySize.
  ///
  /// In en, this message translates to:
  /// **'{size}'**
  String directorySize(String size);

  /// No description provided for @editName.
  ///
  /// In en, this message translates to:
  /// **'Edit Name'**
  String get editName;

  /// No description provided for @removeDirectory.
  ///
  /// In en, this message translates to:
  /// **'Remove Directory'**
  String get removeDirectory;

  /// No description provided for @noAuthorizedDirectories.
  ///
  /// In en, this message translates to:
  /// **'No Authorized Directories'**
  String get noAuthorizedDirectories;

  /// No description provided for @noAuthorizedDirectoriesSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Add directories to access files from different locations'**
  String get noAuthorizedDirectoriesSubtitle;

  /// No description provided for @addDirectory.
  ///
  /// In en, this message translates to:
  /// **'Add Directory'**
  String get addDirectory;

  /// No description provided for @authorizedDirectories.
  ///
  /// In en, this message translates to:
  /// **'Authorized Directories'**
  String get authorizedDirectories;

  /// No description provided for @appCanAccessDirectories.
  ///
  /// In en, this message translates to:
  /// **'{appName} can access the following directories:'**
  String appCanAccessDirectories(String appName);

  /// No description provided for @nameForNewFolder.
  ///
  /// In en, this message translates to:
  /// **'Name for New Folder'**
  String get nameForNewFolder;

  /// No description provided for @enterCustomName.
  ///
  /// In en, this message translates to:
  /// **'Enter Custom Name'**
  String get enterCustomName;

  /// No description provided for @customName.
  ///
  /// In en, this message translates to:
  /// **'Custom Name'**
  String get customName;

  /// No description provided for @version.
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get version;

  /// No description provided for @appVersion.
  ///
  /// In en, this message translates to:
  /// **'1.0.0'**
  String get appVersion;

  /// No description provided for @appDescription.
  ///
  /// In en, this message translates to:
  /// **'Smart document processing. AI auto crop and correct, showcase professionalism.'**
  String get appDescription;

  /// No description provided for @privacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// No description provided for @termsOfService.
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get termsOfService;

  /// No description provided for @copyright.
  ///
  /// In en, this message translates to:
  /// **'© 2025 All PDF Editor. All rights reserved.'**
  String get copyright;

  /// No description provided for @noFavoriteFiles.
  ///
  /// In en, this message translates to:
  /// **'No Favorite Files'**
  String get noFavoriteFiles;

  /// No description provided for @noFavoriteFilesHint.
  ///
  /// In en, this message translates to:
  /// **'Star files to add them to favorites for quick access'**
  String get noFavoriteFilesHint;

  /// No description provided for @fileNotFound.
  ///
  /// In en, this message translates to:
  /// **'File not found'**
  String get fileNotFound;

  /// No description provided for @noRecentFiles.
  ///
  /// In en, this message translates to:
  /// **'PDF/Word/Excel/PPT files you\'ve opened in this app will appear here'**
  String get noRecentFiles;

  /// No description provided for @noFilesFoundForType.
  ///
  /// In en, this message translates to:
  /// **'Found {count} {fileType} files'**
  String noFilesFoundForType(int count, String fileType);

  /// No description provided for @selectFolder.
  ///
  /// In en, this message translates to:
  /// **'Authorize Folder'**
  String get selectFolder;

  /// No description provided for @selectFolderSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Grant permission to find PDF/Word/Excel/PPT file directories'**
  String get selectFolderSubtitle;

  /// No description provided for @photoToPdfSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Scan presentation slides or notes into one PDF'**
  String get photoToPdfSubtitle;

  /// No description provided for @mergeImagesToPdf.
  ///
  /// In en, this message translates to:
  /// **'Merge Images to PDF'**
  String get mergeImagesToPdf;

  /// No description provided for @mergeImagesToPdfSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Combine multiple images into one PDF'**
  String get mergeImagesToPdfSubtitle;

  /// No description provided for @fabTutorial.
  ///
  /// In en, this message translates to:
  /// **'Tap the + button to start creating PDFs from images'**
  String get fabTutorial;

  /// No description provided for @iGotIt.
  ///
  /// In en, this message translates to:
  /// **'I Got It'**
  String get iGotIt;

  /// No description provided for @signatures.
  ///
  /// In en, this message translates to:
  /// **'Signatures'**
  String get signatures;

  /// No description provided for @manageSignatures.
  ///
  /// In en, this message translates to:
  /// **'Manage Your Handwritten Signatures'**
  String get manageSignatures;

  /// No description provided for @manageSignaturesHint.
  ///
  /// In en, this message translates to:
  /// **'Manage your handwritten signatures here. Tap the + button to add your signature. You can then use these signatures when editing PDFs.'**
  String get manageSignaturesHint;

  /// No description provided for @addSignature.
  ///
  /// In en, this message translates to:
  /// **'Add Signature'**
  String get addSignature;

  /// No description provided for @deleteSignature.
  ///
  /// In en, this message translates to:
  /// **'Delete Signature'**
  String get deleteSignature;

  /// No description provided for @confirmDeleteSignature.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this signature?'**
  String get confirmDeleteSignature;

  /// No description provided for @createSignature.
  ///
  /// In en, this message translates to:
  /// **'Create Signature'**
  String get createSignature;

  /// No description provided for @saveSignature.
  ///
  /// In en, this message translates to:
  /// **'Save Signature'**
  String get saveSignature;

  /// No description provided for @strokeWidth.
  ///
  /// In en, this message translates to:
  /// **'Stroke Width'**
  String get strokeWidth;

  /// No description provided for @color.
  ///
  /// In en, this message translates to:
  /// **'Color'**
  String get color;

  /// No description provided for @eraser.
  ///
  /// In en, this message translates to:
  /// **'Eraser'**
  String get eraser;

  /// No description provided for @thin.
  ///
  /// In en, this message translates to:
  /// **'Thin'**
  String get thin;

  /// No description provided for @medium.
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get medium;

  /// No description provided for @thick.
  ///
  /// In en, this message translates to:
  /// **'Thick'**
  String get thick;

  /// No description provided for @signatureCreated.
  ///
  /// In en, this message translates to:
  /// **'Signature created successfully'**
  String get signatureCreated;

  /// No description provided for @editSignature.
  ///
  /// In en, this message translates to:
  /// **'Edit Signature'**
  String get editSignature;

  /// No description provided for @signatureUpdated.
  ///
  /// In en, this message translates to:
  /// **'Signature updated successfully'**
  String get signatureUpdated;

  /// No description provided for @signatureDeleted.
  ///
  /// In en, this message translates to:
  /// **'Signature deleted'**
  String get signatureDeleted;

  /// No description provided for @noSignatures.
  ///
  /// In en, this message translates to:
  /// **'No Signatures'**
  String get noSignatures;

  /// No description provided for @newSignature.
  ///
  /// In en, this message translates to:
  /// **'New Signature'**
  String get newSignature;

  /// No description provided for @signatureUsageHint.
  ///
  /// In en, this message translates to:
  /// **'You can use these signatures when editing PDFs'**
  String get signatureUsageHint;

  /// No description provided for @scanningFiles.
  ///
  /// In en, this message translates to:
  /// **'Scanning for PDF/Word/Excel/PPT files on your device'**
  String get scanningFiles;

  /// No description provided for @pageRotated.
  ///
  /// In en, this message translates to:
  /// **'Rotated page {page} ({degrees}°)'**
  String pageRotated(int page, int degrees);

  /// No description provided for @rotationFailed.
  ///
  /// In en, this message translates to:
  /// **'Rotation failed'**
  String get rotationFailed;

  /// No description provided for @closeSearch.
  ///
  /// In en, this message translates to:
  /// **'Close search'**
  String get closeSearch;

  /// No description provided for @previous.
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previous;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @cancelSignatureEdit.
  ///
  /// In en, this message translates to:
  /// **'Cancel signature edit'**
  String get cancelSignatureEdit;

  /// No description provided for @back.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// No description provided for @editMode.
  ///
  /// In en, this message translates to:
  /// **'Edit Mode'**
  String get editMode;

  /// No description provided for @highlightText.
  ///
  /// In en, this message translates to:
  /// **'Highlight Text'**
  String get highlightText;

  /// No description provided for @draw.
  ///
  /// In en, this message translates to:
  /// **'Draw'**
  String get draw;

  /// No description provided for @signature.
  ///
  /// In en, this message translates to:
  /// **'Signature'**
  String get signature;

  /// No description provided for @highlightColor.
  ///
  /// In en, this message translates to:
  /// **'Highlight Color:'**
  String get highlightColor;

  /// No description provided for @brushColor.
  ///
  /// In en, this message translates to:
  /// **'Brush:'**
  String get brushColor;

  /// No description provided for @thickness.
  ///
  /// In en, this message translates to:
  /// **'Thickness:'**
  String get thickness;

  /// No description provided for @confirmPlacement.
  ///
  /// In en, this message translates to:
  /// **'Confirm Placement'**
  String get confirmPlacement;

  /// No description provided for @dragToAdjustHint.
  ///
  /// In en, this message translates to:
  /// **'Drag signature to adjust position, then tap to confirm placement'**
  String get dragToAdjustHint;

  /// No description provided for @selectSignature.
  ///
  /// In en, this message translates to:
  /// **'Select Signature'**
  String get selectSignature;

  /// No description provided for @noSignaturesPleaseCreate.
  ///
  /// In en, this message translates to:
  /// **'No signatures, please create one first'**
  String get noSignaturesPleaseCreate;

  /// No description provided for @pageIndicator.
  ///
  /// In en, this message translates to:
  /// **'Page {current}/{total}'**
  String pageIndicator(int current, int total);

  /// No description provided for @androidDirectoryManagementSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Manage and add accessible folders'**
  String get androidDirectoryManagementSubtitle;

  /// No description provided for @loadingDocument.
  ///
  /// In en, this message translates to:
  /// **'Loading document...'**
  String get loadingDocument;

  /// No description provided for @cannotOpenFile.
  ///
  /// In en, this message translates to:
  /// **'Cannot open file'**
  String get cannotOpenFile;

  /// No description provided for @galleryAccessError.
  ///
  /// In en, this message translates to:
  /// **'Cannot access the selected images'**
  String get galleryAccessError;

  /// No description provided for @permissionDenied.
  ///
  /// In en, this message translates to:
  /// **'Permission denied to access photos'**
  String get permissionDenied;

  /// No description provided for @invalidImageSelected.
  ///
  /// In en, this message translates to:
  /// **'Invalid image selected'**
  String get invalidImageSelected;

  /// No description provided for @gallerySelectionFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to select images from gallery'**
  String get gallerySelectionFailed;

  /// No description provided for @unexpectedError.
  ///
  /// In en, this message translates to:
  /// **'An unexpected error occurred'**
  String get unexpectedError;

  /// No description provided for @importFailed.
  ///
  /// In en, this message translates to:
  /// **'Import Failed'**
  String get importFailed;

  /// No description provided for @importResults.
  ///
  /// In en, this message translates to:
  /// **'Import Results'**
  String get importResults;

  /// No description provided for @allImportsFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to import all {count} selected images'**
  String allImportsFailed(int count);

  /// No description provided for @partialImportSuccess.
  ///
  /// In en, this message translates to:
  /// **'Imported {successCount} images successfully, {failCount} failed'**
  String partialImportSuccess(int successCount, int failCount);

  /// No description provided for @successfullyImported.
  ///
  /// In en, this message translates to:
  /// **'✅ Successfully imported: {count} images'**
  String successfullyImported(int count);

  /// No description provided for @failedToImport.
  ///
  /// In en, this message translates to:
  /// **'❌ Failed to import: {count} images'**
  String failedToImport(int count);

  /// No description provided for @tryAgainOrUseCamera.
  ///
  /// In en, this message translates to:
  /// **'Please try again or use camera instead'**
  String get tryAgainOrUseCamera;

  /// No description provided for @checkPermissionsInSettings.
  ///
  /// In en, this message translates to:
  /// **'Please check app permissions in Settings'**
  String get checkPermissionsInSettings;

  /// No description provided for @trySelectingDifferentImages.
  ///
  /// In en, this message translates to:
  /// **'Please try selecting different images'**
  String get trySelectingDifferentImages;

  /// No description provided for @selectedImagesCorruptedOrInaccessible.
  ///
  /// In en, this message translates to:
  /// **'The selected images may be corrupted or inaccessible. Please try selecting different images.'**
  String get selectedImagesCorruptedOrInaccessible;

  /// No description provided for @failedFiles.
  ///
  /// In en, this message translates to:
  /// **'Failed files:'**
  String get failedFiles;

  /// No description provided for @andXMore.
  ///
  /// In en, this message translates to:
  /// **'... and {count} more'**
  String andXMore(int count);

  /// No description provided for @tryAgain.
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// No description provided for @details.
  ///
  /// In en, this message translates to:
  /// **'Details'**
  String get details;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @galleryLoadFailed.
  ///
  /// In en, this message translates to:
  /// **'Gallery Loading Failed'**
  String get galleryLoadFailed;

  /// No description provided for @fileSelectionFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to select files'**
  String get fileSelectionFailed;

  /// No description provided for @storagePermissionDenied.
  ///
  /// In en, this message translates to:
  /// **'Storage permission denied'**
  String get storagePermissionDenied;

  /// No description provided for @unsupportedFileType.
  ///
  /// In en, this message translates to:
  /// **'Unsupported file type selected'**
  String get unsupportedFileType;

  /// No description provided for @playSlideshow.
  ///
  /// In en, this message translates to:
  /// **'Play Slideshow'**
  String get playSlideshow;

  /// No description provided for @slideshowMode.
  ///
  /// In en, this message translates to:
  /// **'Slideshow Mode'**
  String get slideshowMode;

  /// No description provided for @exitSlideshow.
  ///
  /// In en, this message translates to:
  /// **'Exit Slideshow'**
  String get exitSlideshow;

  /// No description provided for @searchTitle.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get searchTitle;

  /// No description provided for @searchHint.
  ///
  /// In en, this message translates to:
  /// **'Enter file name'**
  String get searchHint;

  /// No description provided for @noResults.
  ///
  /// In en, this message translates to:
  /// **'No results'**
  String get noResults;

  /// No description provided for @iosPermissionMessage.
  ///
  /// In en, this message translates to:
  /// **'Here are your existing PDF, Word, Excel, PPT files on your phone. Due to {deviceType} permission restrictions, you need to authorize access to the folders containing your files first.'**
  String iosPermissionMessage(String deviceType);

  /// No description provided for @waitForDocLoad.
  ///
  /// In en, this message translates to:
  /// **'Please wait for the document to load completely before preparing the PDF'**
  String get waitForDocLoad;

  /// No description provided for @generatingPdf.
  ///
  /// In en, this message translates to:
  /// **'Generating PDF...'**
  String get generatingPdf;

  /// No description provided for @pdfGenerationFailed.
  ///
  /// In en, this message translates to:
  /// **'PDF preparation failed, please try again'**
  String get pdfGenerationFailed;

  /// No description provided for @pdfConversionFailed.
  ///
  /// In en, this message translates to:
  /// **'PDF conversion failed: {error}'**
  String pdfConversionFailed(String error);

  /// No description provided for @conversionSuccess.
  ///
  /// In en, this message translates to:
  /// **'Conversion successful'**
  String get conversionSuccess;

  /// No description provided for @pdfSavedMessage.
  ///
  /// In en, this message translates to:
  /// **'Saved as PDF file {fileName}, open to view now?'**
  String pdfSavedMessage(String fileName);

  /// No description provided for @stayOnPage.
  ///
  /// In en, this message translates to:
  /// **'Stay on page'**
  String get stayOnPage;

  /// No description provided for @openPdf.
  ///
  /// In en, this message translates to:
  /// **'Open PDF'**
  String get openPdf;

  /// No description provided for @generatePdfVersion.
  ///
  /// In en, this message translates to:
  /// **'Generate PDF version'**
  String get generatePdfVersion;

  /// No description provided for @generatePdfToEdit.
  ///
  /// In en, this message translates to:
  /// **'A PDF version needs to be generated to edit or rotate. Continue?'**
  String get generatePdfToEdit;

  /// No description provided for @continueAction.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueAction;

  /// No description provided for @openingCachedPdf.
  ///
  /// In en, this message translates to:
  /// **'Opening cached PDF...'**
  String get openingCachedPdf;

  /// No description provided for @saveAsPdf.
  ///
  /// In en, this message translates to:
  /// **'Save as PDF'**
  String get saveAsPdf;

  /// No description provided for @caseSensitive.
  ///
  /// In en, this message translates to:
  /// **'Case sensitive'**
  String get caseSensitive;

  /// No description provided for @notFound.
  ///
  /// In en, this message translates to:
  /// **'Not found \"{query}\"'**
  String notFound(String query);

  /// No description provided for @previousMatch.
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previousMatch;

  /// No description provided for @nextMatch.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get nextMatch;

  /// No description provided for @webViewOnlyIos.
  ///
  /// In en, this message translates to:
  /// **'WebView document viewing is only supported on iOS'**
  String get webViewOnlyIos;

  /// No description provided for @webViewFailed.
  ///
  /// In en, this message translates to:
  /// **'WebView failed to load: {error}'**
  String webViewFailed(String error);

  /// No description provided for @officeTimeout.
  ///
  /// In en, this message translates to:
  /// **'Office document loading timed out. This may be due to a large file or unsupported format.'**
  String get officeTimeout;

  /// No description provided for @addSlideshowButton.
  ///
  /// In en, this message translates to:
  /// **'Add slideshow button'**
  String get addSlideshowButton;

  /// No description provided for @threshold400px.
  ///
  /// In en, this message translates to:
  /// **'Threshold: 400px'**
  String get threshold400px;

  /// No description provided for @confirmClear.
  ///
  /// In en, this message translates to:
  /// **'Confirm Clear'**
  String get confirmClear;

  /// No description provided for @confirmClearSignature.
  ///
  /// In en, this message translates to:
  /// **'This will clear your entire signature image. Are you sure you want to clear it?'**
  String get confirmClearSignature;

  /// No description provided for @clear.
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// No description provided for @iosNetworkPermissionGuide.
  ///
  /// In en, this message translates to:
  /// **'Please find \"{appName}\" in your iPhone\'s Settings and ensure network access is allowed.\nPath: Settings > Privacy & Security > Local Network > {appName}'**
  String iosNetworkPermissionGuide(String appName);

  /// No description provided for @checkNetworkConnection.
  ///
  /// In en, this message translates to:
  /// **'Please check your network connection'**
  String get checkNetworkConnection;

  /// No description provided for @networkPermission.
  ///
  /// In en, this message translates to:
  /// **'Network Permission'**
  String get networkPermission;

  /// No description provided for @networkPermissionRequest.
  ///
  /// In en, this message translates to:
  /// **'This app needs to access the network to process and enhance images in PDF documents.'**
  String get networkPermissionRequest;

  /// No description provided for @featuresInclude.
  ///
  /// In en, this message translates to:
  /// **'Features include:'**
  String get featuresInclude;

  /// No description provided for @imageClarityEnhancement.
  ///
  /// In en, this message translates to:
  /// **'Image clarity enhancement'**
  String get imageClarityEnhancement;

  /// No description provided for @documentEdgeDetection.
  ///
  /// In en, this message translates to:
  /// **'Document edge detection'**
  String get documentEdgeDetection;

  /// No description provided for @imageOptimization.
  ///
  /// In en, this message translates to:
  /// **'Image optimization'**
  String get imageOptimization;

  /// No description provided for @requestPermission.
  ///
  /// In en, this message translates to:
  /// **'Request Permission'**
  String get requestPermission;

  /// No description provided for @networkConnectionProblem.
  ///
  /// In en, this message translates to:
  /// **'Network Connection Problem'**
  String get networkConnectionProblem;

  /// No description provided for @networkStatusDescription.
  ///
  /// In en, this message translates to:
  /// **'Network connection is unavailable, please check your network settings'**
  String get networkStatusDescription;

  /// No description provided for @troubleshootingSuggestions.
  ///
  /// In en, this message translates to:
  /// **'Troubleshooting suggestions:'**
  String get troubleshootingSuggestions;

  /// No description provided for @networkTroubleshootingTips.
  ///
  /// In en, this message translates to:
  /// **'Check if Wi-Fi or mobile data is enabled\nConfirm the device is connected to a usable network\nCheck if airplane mode is on\nTry to access other apps or websites to confirm network status\nRestart the network connection or device'**
  String get networkTroubleshootingTips;

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// No description provided for @unfavorite.
  ///
  /// In en, this message translates to:
  /// **'Unfavorite'**
  String get unfavorite;

  /// No description provided for @open.
  ///
  /// In en, this message translates to:
  /// **'Open'**
  String get open;

  /// No description provided for @rename.
  ///
  /// In en, this message translates to:
  /// **'Rename'**
  String get rename;

  /// No description provided for @cannotAccessFile.
  ///
  /// In en, this message translates to:
  /// **'Cannot access this file. Please reauthorize in \"Authorized Directories\" or reselect the file.'**
  String get cannotAccessFile;

  /// No description provided for @installOfficeAppTitle.
  ///
  /// In en, this message translates to:
  /// **'App required to open Office files'**
  String get installOfficeAppTitle;

  /// No description provided for @installOfficeAppContent.
  ///
  /// In en, this message translates to:
  /// **'An app is required to open this file. Install from the app store?'**
  String get installOfficeAppContent;

  /// No description provided for @install.
  ///
  /// In en, this message translates to:
  /// **'Install'**
  String get install;

  /// No description provided for @renameFile.
  ///
  /// In en, this message translates to:
  /// **'Rename'**
  String get renameFile;

  /// No description provided for @renamed.
  ///
  /// In en, this message translates to:
  /// **'Renamed'**
  String get renamed;

  /// No description provided for @renameFailed.
  ///
  /// In en, this message translates to:
  /// **'Rename failed: {error}'**
  String renameFailed(String error);

  /// No description provided for @confirmDeleteFile.
  ///
  /// In en, this message translates to:
  /// **'Confirm delete file:\n{fileName}?'**
  String confirmDeleteFile(String fileName);

  /// No description provided for @deleted.
  ///
  /// In en, this message translates to:
  /// **'Deleted'**
  String get deleted;

  /// No description provided for @deleteFailed.
  ///
  /// In en, this message translates to:
  /// **'Delete failed: {error}'**
  String deleteFailed(String error);

  /// No description provided for @networkConnected.
  ///
  /// In en, this message translates to:
  /// **'Network connected'**
  String get networkConnected;

  /// No description provided for @networkDisconnected.
  ///
  /// In en, this message translates to:
  /// **'Network disconnected'**
  String get networkDisconnected;

  /// No description provided for @networkAccessDenied.
  ///
  /// In en, this message translates to:
  /// **'Network access denied'**
  String get networkAccessDenied;

  /// No description provided for @networkOperationFailed.
  ///
  /// In en, this message translates to:
  /// **'Network operation failed'**
  String get networkOperationFailed;

  /// No description provided for @connected.
  ///
  /// In en, this message translates to:
  /// **'Connected'**
  String get connected;

  /// No description provided for @noNetwork.
  ///
  /// In en, this message translates to:
  /// **'No network'**
  String get noNetwork;

  /// No description provided for @thumbnailsOnlyIos.
  ///
  /// In en, this message translates to:
  /// **'Thumbnails are only available on iOS'**
  String get thumbnailsOnlyIos;

  /// No description provided for @waitDocumentComplete.
  ///
  /// In en, this message translates to:
  /// **'Waiting for document to load completely...'**
  String get waitDocumentComplete;

  /// No description provided for @thumbnailDescription.
  ///
  /// In en, this message translates to:
  /// **'Full document thumbnail\nfor easy navigation'**
  String get thumbnailDescription;

  /// No description provided for @generatingThumbnail.
  ///
  /// In en, this message translates to:
  /// **'Generating complete document thumbnail...'**
  String get generatingThumbnail;

  /// No description provided for @analyzingDocument.
  ///
  /// In en, this message translates to:
  /// **'Analyzing document structure and dimensions'**
  String get analyzingDocument;

  /// No description provided for @thumbnailUnavailable.
  ///
  /// In en, this message translates to:
  /// **'Thumbnail temporarily unavailable'**
  String get thumbnailUnavailable;

  /// No description provided for @clickRefreshRetry.
  ///
  /// In en, this message translates to:
  /// **'Click the refresh button in the top right to retry'**
  String get clickRefreshRetry;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>[
    'de',
    'en',
    'es',
    'fr',
    'ja',
    'ko',
    'pt',
    'zh',
  ].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when language+country codes are specified.
  switch (locale.languageCode) {
    case 'zh':
      {
        switch (locale.countryCode) {
          case 'TW':
            return AppLocalizationsZhTw();
        }
        break;
      }
  }

  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'de':
      return AppLocalizationsDe();
    case 'en':
      return AppLocalizationsEn();
    case 'es':
      return AppLocalizationsEs();
    case 'fr':
      return AppLocalizationsFr();
    case 'ja':
      return AppLocalizationsJa();
    case 'ko':
      return AppLocalizationsKo();
    case 'pt':
      return AppLocalizationsPt();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
