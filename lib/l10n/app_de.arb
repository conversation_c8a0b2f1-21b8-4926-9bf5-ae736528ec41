{"@@locale": "de", "appName": "Alle PDF-Editor", "documents": "Dokumente", "recent": "<PERSON><PERSON><PERSON><PERSON>", "favorite": "<PERSON><PERSON>", "settings": "Einstellungen", "addFiles": "<PERSON><PERSON>", "addFilesSubtitle": "<PERSON><PERSON><PERSON><PERSON> Sie weitere Dateien von Ihrem Gerät zur Verwaltung", "followDocumentTheme": "Farbschema nach Dokumenttyp folgen", "followDocumentThemeSubtitle": "PDF/Word/Excel/PPT-Stile", "languageSettings": "UI-Sprache", "aboutApp": "<PERSON><PERSON>", "version": "Version", "appVersion": "1.0.0", "copyright": "© 2025", "filesAdded": "{count} <PERSON><PERSON>", "cannotSelectFiles": "<PERSON><PERSON> können nicht ausgewählt werden, bitte Berechtigungen überprüfen", "selectLanguage": "Sprache auswählen", "systemLanguage": "Systemsprache", "english": "English", "spanish": "Español", "portuguese": "Português", "french": "Français", "german": "De<PERSON>ch", "chineseSimplified": "简体中文", "chineseTraditional": "繁體中文", "japanese": "日本語", "korean": "한국어", "currentLanguage": "Aktuell: {language}", "selectAuthorizedDirectory": "Autorisiertes Verzeichnis verwalten", "selectAuthorizedDirectorySubtitle": "Zugriff auf iPhone/iPad-Dateiverzeichnisse autorisieren", "shareApp": "Teilen", "shareAppSubtitle": "Empfehlung eines PDF Magic Tools:", "rateApp": "Bewerten", "rateAppSubtitle": "Bewerten Sie un<PERSON> im App Store", "aboutAppSubtitle": "App-Version, Richtlinien und mehr", "privacyPolicy": "Datenschutzrichtlinie", "termsOfService": "Nutzungsbedingungen", "appDescription": "Intelligente Dokumentenverarbeitung. KI-automatisches Zuschneiden und Korrigieren für professionelle Ergebnisse.", "authorizedDirectories": "Autorisierte Verzeichnisse verwalten", "addDirectory": "Verzeichnis hinzufügen", "removeDirectory": "Entfernen", "noAuthorizedDirectories": "Keine autorisierten Verzeichnisse", "noAuthorizedDirectoriesSubtitle": "Verzeichnisse hinzufügen, um auf Dateien von diesen Standorten zuzugreifen", "directoryRemoved": "Verzeichnis-Autorisierung entfernt", "cannotAddDirectory": "Verzeichnis-Autorisierung fehlgeschlagen, bitte erneut versuchen", "appCanAccessDirectories": "{appName} kann auf die folgenden Verzeichnisse zugreifen:", "filesCount": "{count} <PERSON><PERSON>", "directorySize": "Größe: {size}", "cancelAuthorization": "Autorisierung abbrechen", "confirmCancelAuthorization": "Möchten Sie die Zugriffsberechtigung für {appName}’s {directoryName} wirklich aufheben? Nach der Aufhebung sind die Dateien in diesem Verzeichnis nicht mehr sichtbar.", "@confirmCancelAuthorization": {"placeholders": {"appName": {"type": "String"}, "directoryName": {"type": "String"}}}, "noKeepIt": "<PERSON>ein, behalten", "yesRemoveFolder": "<PERSON><PERSON>, ich brauche diesen Ordner nicht mehr", "editName": "Namen bearbeiten", "enterCustomName": "Benutzerdefinierten Namen eingeben", "customName": "Benutzerdefinierter Name", "save": "Speichern", "cancel": "Abbrechen", "nameForNewFolder": "Geben Sie zur späteren einfachen Identifizierung einen Namen für den gerade ausgewählten App-Ordner ein", "defaultAppName": "App {number}", "@defaultAppName": {"placeholders": {"number": {"type": "int"}}}, "selectFiles": "<PERSON><PERSON> au<PERSON>wählen", "authorizeFolder": "Ordner autorisieren", "selectingDirectory": "Verzeichnis wird gescannt...", "directorySelectionOnlyMobile": "Verzeichnisauswahl ist nur auf iOS/Android verfügbar", "cannotSelectDirectory": "Verzeichnis kann nicht ausgewählt werden, bitte Berechtigungen überprüfen", "loadedFiles": "{count} <PERSON><PERSON> g<PERSON>", "@loadedFiles": {"placeholders": {"count": {"type": "int"}}}, "cannotLoadDirectoryFiles": "Verzeichnisdateien können nicht geladen werden", "iosPermissionMessage": "Aufgrund der iPhone-Berechtigungsbeschränkungen müssen Si<PERSON> zu<PERSON>t den Zugriff auf die Ordner autorisieren, die Ihre PDF-, Word-, Excel- und PPT-Dateien enthalten.", "noFilesFoundForType": "{count} {fileType}-<PERSON><PERSON> gefunden", "@noFilesFoundForType": {"placeholders": {"count": {"type": "int"}, "fileType": {"type": "String"}}}, "importFilesFromPhone": "Dateien vom Telefon importieren", "selectFolder": "Ordner autorisieren", "selectFolderSubtitle": "Diese App berechtigen, PDF/Word/Excel/PPT-Dateiverzeichnisse zu finden", "photoToPdf": "<PERSON>annen als PDF", "imageToPdf": "<PERSON><PERSON><PERSON> zu <PERSON>", "photoToPdfSubtitle": "Präsentationsfolien oder Notizen in eine PDF-Date<PERSON> scannen", "mergeImagesToPdf": "Bilder zu <PERSON> zu<PERSON>mmenführen", "mergeImagesToPdfSubtitle": "Album-Bilder zu <PERSON> zu<PERSON>mmenführen", "convert": "Konvertieren", "pdfSavedSuccessfully": "PDF erfolgreich gespeichert", "pdfSaveFailed": "PDF-Speicherung fehlgeschlagen", "undo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteImage": "Bild löschen", "confirmDeleteImage": "Sind Si<PERSON> sicher, dass Sie dieses Bild löschen möchten?", "delete": "Löschen", "reorderImage": "Bilder neu ordnen", "takePicture": "Foto aufnehmen", "addImagesFromGallery": "{count} Bilder aus der Galerie hinzufügen", "cropImage": "Bild <PERSON>", "confirmUndo": "Rückgängig: {action}. Dies macht Ihre letzte Aktion rückgängig. Fortfahren?", "cropPlaceholderMessage": "Zuschneidefunktion wird hier implementiert", "cropFeatureComingSoon": "<PERSON>rweiterte Zuschneidefunktionen kommen bald!", "addMoreImages": "Mehr Bilder hinzufügen", "takePhoto": "Foto aufnehmen", "importFromAlbum": "Aus Album importieren", "sort": "<PERSON><PERSON><PERSON><PERSON>", "nameSort": "Name", "lastModified": "Zuletzt geändert", "sizeSort": "Größe", "descending": "Absteigend", "apply": "<PERSON><PERSON><PERSON>", "noRecentFiles": "PDF/Word/Excel/PPT-<PERSON><PERSON>, die Si<PERSON> in dieser App geöffnet haben, werden hier angezeigt", "noFavoriteFiles": "Sie haben noch keine <PERSON>ien favorisiert", "noFavoriteFilesHint": "Sie können Dateien favorisieren, indem Sie das ☆ Stern-Symbol neben den Dateien auf der Dokumentenseite antippen", "fileNotFound": "Datei nicht gefunden", "weLikeYouToo": "Danke für Ihre Wertschätzung!", "thankYouForFeedback": "Ihre 5-<PERSON><PERSON>-Bewertung motiviert uns zur Verbesserung!", "theBestWeCanGet": "<PERSON><PERSON><PERSON><PERSON>, 5-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>!", "maybeLater": "Vielleicht später", "rateNow": "Jetzt im Store bewerten", "saveAs": "Speichern als", "fileName": "Dateiname", "discardChangesTitle": "Änderungen verwerfen?", "discardChangesContent": "Sie haben nicht gespeicherte Änderungen. Wenn Si<PERSON> jetzt beenden, gehen diese verloren.", "discard": "Verwerfen", "keepEditing": "<PERSON><PERSON> bearbeiten", "splashTitle": "Foto-zu-PDF Magic Tool", "splashSubtitle": "Meeting-Fotos automatisch organisieren, mit einem Tap zusch<PERSON>iden und zu PDF zusammenfügen", "splashWorkflow": "App wird gestartet, bitte warten...", "splashOriginalPhoto": "Original Foto", "splashGeneratedPdf": "Automatisch erstelltes PDF", "fabTutorial": "<PERSON><PERSON><PERSON> hier, um Dokumente mit Ihrer Kamera zu scannen oder Bilder aus Ihrer Galerie zu einem PDF zusammenzufügen.", "iGotIt": "Ich hab's", "scanPrefix": "scan", "signatures": "Unterschriften", "manageSignatures": "Handschriftliche Unterschriften verwalten", "manageSignaturesHint": "Verwalten Sie hier Ihre handschriftlichen Unterschriften. Tippen Si<PERSON> auf die +-Schaltfläche, um Ihre Unterschrift hinzuzufügen. Sie können diese Unterschriften dann beim Bearbeiten von PDFs verwenden.", "addSignature": "Unterschrift hinzufügen", "deleteSignature": "Unterschrift löschen", "confirmDeleteSignature": "Sind <PERSON> sicher, dass Sie diese Unterschrift löschen möchten?", "createSignature": "Unterschrift erstellen", "saveSignature": "Unterschrift speichern", "strokeWidth": "Strichstärke", "color": "Farbe", "eraser": "<PERSON><PERSON><PERSON><PERSON>", "thin": "<PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "thick": "<PERSON>", "signatureCreated": "Unterschrift erfolgreich erstellt", "signatureDeleted": "Unterschrift gelöscht", "noSignatures": "<PERSON><PERSON>", "newSignature": "Neue Unterschrift", "signatureUsageHint": "Sie können diese Unterschriften beim Bearbeiten von PDFs verwenden", "scanningFiles": "Suche nach PDF/Word/Excel/PPT-Dateien auf Ihrem Gerät", "pageRotated": "Seite {page} um {degrees}° gedreht", "@pageRotated": {"placeholders": {"page": {"type": "int"}, "degrees": {"type": "int"}}}, "rotationFailed": "Drehung fehlgeschlagen", "closeSearch": "<PERSON><PERSON> sch<PERSON>ßen", "previous": "<PERSON><PERSON><PERSON><PERSON>", "next": "Nächste", "cancelSignatureEdit": "Signatur-Bearbeitung abbrechen", "back": "Zurück", "editMode": "Bearbeitungsmo<PERSON>", "highlightText": "Text markieren", "draw": "<PERSON><PERSON><PERSON><PERSON>", "signature": "Signatur", "highlightColor": "Hervorhebungsfarbe", "brushColor": "Pinselfarbe", "thickness": "<PERSON><PERSON>", "confirmPlacement": "Platzierung bestätigen", "dragToAdjustHint": "<PERSON><PERSON><PERSON>, um anzu<PERSON>en", "selectSignature": "Signatur auswählen", "noSignaturesPleaseCreate": "<PERSON><PERSON>, bitte erstellen Si<PERSON> eine", "pageIndicator": "Seite {current}/{total}", "@pageIndicator": {"placeholders": {"current": {"type": "int"}, "total": {"type": "int"}}}, "androidDirectoryManagementSubtitle": "Verwalten und hinzufügen zugänglicher Ordner", "loadingDocument": "Dokument wird geladen...", "cannotOpenFile": "<PERSON>i kann nicht geöffnet werden", "galleryAccessError": "Zugriff auf ausgewählte Bilder nicht möglich", "permissionDenied": "Berechtigung für Foto-Zugriff verweigert", "invalidImageSelected": "Ungültiges Bild ausgewählt", "gallerySelectionFailed": "Auswahl von Bildern aus Galerie fehlgeschlagen", "unexpectedError": "Ein unerwarteter Fehler ist aufgetreten", "importFailed": "Import fehlgeschlagen", "importResults": "Import-Ergebnisse", "allImportsFailed": "Import aller {count} ausgewählten Bilder fehlgeschlagen", "@allImportsFailed": {"placeholders": {"count": {"type": "int"}}}, "partialImportSuccess": "{successCount} Bilder er<PERSON><PERSON><PERSON><PERSON><PERSON> importiert, {failCount} fehlgeschlagen", "@partialImportSuccess": {"placeholders": {"successCount": {"type": "int"}, "failCount": {"type": "int"}}}, "successfullyImported": "✅ Erfolgreich importiert: {count} Bilder", "@successfullyImported": {"placeholders": {"count": {"type": "int"}}}, "failedToImport": "❌ Import fehlgeschlagen: {count} Bilder", "@failedToImport": {"placeholders": {"count": {"type": "int"}}}, "tryAgainOrUseCamera": "Bitte erneut versuchen oder Kamera verwenden", "checkPermissionsInSettings": "Bitte App-Berechtigungen in Einstellungen überprüfen", "trySelectingDifferentImages": "Bitte andere Bilder auswählen", "selectedImagesCorruptedOrInaccessible": "Die ausgewählten Bilder könnten beschädigt oder nicht zugänglich sein. Bitte andere Bilder auswählen.", "failedFiles": "Fehlgeschlagene Dateien:", "andXMore": "... und {count} weitere", "@andXMore": {"placeholders": {"count": {"type": "int"}}}, "tryAgain": "<PERSON><PERSON><PERSON> versuchen", "details": "Details", "ok": "OK", "galleryLoadFailed": "Galerie-Laden fehlgeschlagen", "fileSelectionFailed": "Dateiauswahl fehlgeschlagen", "storagePermissionDenied": "Speicher-Berechtigung verweigert", "unsupportedFileType": "Nicht unterstützter Dateityp ausgewählt", "searchTitle": "<PERSON><PERSON>", "searchHint": "Dateinamen eingeben", "noResults": "<PERSON><PERSON>", "waitForDocLoad": "<PERSON>te warten Si<PERSON>, bis das Dokument vollständig geladen ist, bevor <PERSON> das PDF vorbereiten", "generatingPdf": "PDF wird generiert...", "pdfGenerationFailed": "PDF-Vorbereitung fehlgeschlagen, bitte versuchen Sie es erneut", "pdfConversionFailed": "PDF-Konvertierung fehlgeschlagen: {error}", "@pdfConversionFailed": {"placeholders": {"error": {"type": "String"}}}, "conversionSuccess": "Konvertierung erfolgreich", "pdfSavedMessage": "Als PDF-Datei {fileName} gespe<PERSON>rt, jetzt zum Anzeigen öffnen?", "@pdfSavedMessage": {"placeholders": {"fileName": {"type": "String"}}}, "stayOnPage": "<PERSON><PERSON> der Seite bleiben", "openPdf": "PDF öffnen", "generatePdfVersion": "PDF-Version erstellen", "generatePdfToEdit": "Zum Bearbeiten oder Drehen muss eine PDF-Version erstellt werden. Fortfahren?", "continueAction": "Fortfahren", "openingCachedPdf": "Zwischengespeichertes PDF wird geöffnet...", "saveAsPdf": "Als PDF speichern", "caseSensitive": "Groß-/Kleinschreibung beachten", "notFound": "\"{query}\" nicht gefunden", "@notFound": {"placeholders": {"query": {"type": "String"}}}, "previousMatch": "<PERSON><PERSON><PERSON><PERSON>", "nextMatch": "Nächste", "webViewOnlyIos": "Die WebView-Dokumentenanzeige wird nur unter iOS unterstützt", "webViewFailed": "WebView konnte nicht geladen werden: {error}", "@webViewFailed": {"placeholders": {"error": {"type": "String"}}}, "officeTimeout": "Zeitüberschreitung beim Laden des Office-Dokuments. Dies kann an einer großen Datei oder einem nicht unterstützten Format liegen.", "addSlideshowButton": "Diashow-Schaltfläche hinzufügen", "threshold400px": "Schwellenwert: 400px", "confirmClear": "Löschen bestätigen", "confirmClearSignature": "<PERSON><PERSON><PERSON> wird Ihr gesamtes Signaturbild gelöscht. Sind Si<PERSON> sic<PERSON>, dass Si<PERSON> es löschen möchten?", "clear": "Löschen", "iosNetworkPermissionGuide": "Bitte finden Sie \"{appName}\" in den Einstellungen Ihres iPhones und stellen Sie sicher, dass der Netzwerkzugriff erlaubt ist.\nPfad: Einstellungen > Datenschutz & Sicherheit > Lokales Netzwerk > {appName}", "@iosNetworkPermissionGuide": {"placeholders": {"appName": {"type": "String"}}}, "checkNetworkConnection": "Bitte überprüfen Sie Ihre Netzwerkverbindung", "networkPermission": "Netzwerkberechtigung", "networkPermissionRequest": "<PERSON><PERSON> App benötigt <PERSON>zwerkzugriff, um Bilder in PDF-Dokumenten zu verarbeiten und zu verbessern.", "featuresInclude": "Funktionen umfassen:", "imageClarityEnhancement": "Verbesserung der Bildklarheit", "documentEdgeDetection": "Dokumentenkantenerkennung", "imageOptimization": "Bildoptimierung", "requestPermission": "Berechtigung anfordern", "networkConnectionProblem": "Netzwerkverbindungsproblem", "networkStatusDescription": "Netzwerkverbindung ist nicht verfügbar, bitte überprüfen Sie Ihre Netzwerkeinstellungen", "troubleshootingSuggestions": "Fehlerbehebungsvorschläge:", "networkTroubleshootingTips": "Überprüfen Si<PERSON>, ob WLAN oder mobile Daten aktiviert sind\nBestätigen Sie, dass das Gerät mit einem nutzbaren Netzwerk verbunden ist\nÜberprüfen Sie, ob der Flugmodus aktiviert ist\nVersuchen Sie, auf andere Apps oder Websites zuzugreifen, um den Netzwerkstatus zu bestätigen\nStarten Sie die Netzwerkverbindung oder das Gerät neu", "retry": "Retry", "unfavorite": "Unfavorite", "open": "Open", "rename": "<PERSON><PERSON>", "cannotAccessFile": "Cannot access this file. Please reauthorize in \"Authorized Directories\" or reselect the file.", "installOfficeAppTitle": "App required to open Office files", "installOfficeAppContent": "An app is required to open this file. Install from the app store?", "install": "Install", "renameFile": "<PERSON><PERSON>", "renamed": "<PERSON>amed", "renameFailed": "<PERSON><PERSON> failed: {error}", "@renameFailed": {"placeholders": {"error": {"type": "String"}}}, "confirmDeleteFile": "Confirm delete file:\n{fileName}?", "@confirmDeleteFile": {"placeholders": {"fileName": {"type": "String"}}}, "deleted": "Deleted", "deleteFailed": "Delete failed: {error}", "@deleteFailed": {"placeholders": {"error": {"type": "String"}}}, "networkConnected": "Netzwerk verbunden", "networkDisconnected": "Netzwerk getrennt", "networkAccessDenied": "Netzwerkzugriff verweigert", "networkOperationFailed": "Netzwerkvorgang fehlgeschlagen", "thumbnailsOnlyIos": "Miniaturansichten nur auf iOS verfügbar", "waitDocumentComplete": "<PERSON>ten auf Dokumentladevorgang...", "thumbnailDescription": "Miniaturansicht des vollständigen Dokuments\nPraktisch für schnelle Navigation", "generatingThumbnail": "Erstelle Miniaturansicht des vollständigen Dokuments...", "analyzingDocument": "Analysiere Dokumentstruktur und -abmessungen", "thumbnailUnavailable": "Miniaturansicht vorübergehend nicht verfügbar", "clickRefreshRetry": "Klicken Sie auf die Aktualisieren-Schaltfläche oben rechts, um es erneut zu versuchen"}