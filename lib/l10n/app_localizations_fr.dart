// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appName => 'Tout éditeur de PDF';

  @override
  String get documents => 'Documents';

  @override
  String get tools => 'Tools';

  @override
  String get settings => 'Paramètres';

  @override
  String get home => 'Home';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get files => 'Files';

  @override
  String get photoToPdf => 'Photo en PDF';

  @override
  String get imageToPdf => 'Image en PDF';

  @override
  String get addImages => 'Add Images';

  @override
  String addImagesFromGallery(int count) {
    return 'Ajouter $count images de la galerie';
  }

  @override
  String addImagesFromFiles(int count) {
    return 'Add $count images from files';
  }

  @override
  String get convert => 'Convertir';

  @override
  String get undo => 'Annuler';

  @override
  String get discardChangesTitle => 'Annuler les modifications ?';

  @override
  String get discardChangesContent =>
      'Vous avez des modifications non enregistrées. Si vous quittez maintenant, elles seront perdues.';

  @override
  String get keepEditing => 'Continuer l\'édition';

  @override
  String get discard => 'Annuler';

  @override
  String get takePicture => 'Prendre une photo';

  @override
  String get reorderImage => 'Réorganiser les images';

  @override
  String get cropImage => 'Recadrer l\'image';

  @override
  String get deleteImage => 'Supprimer l\'image';

  @override
  String get confirmDeleteImage =>
      'Êtes-vous sûr de vouloir supprimer cette image ?';

  @override
  String get cancel => 'Annuler';

  @override
  String get delete => 'Supprimer';

  @override
  String confirmUndo(String action) {
    return 'Annuler : $action. Cela annulera votre dernière action. Continuer ?';
  }

  @override
  String get pdfSavedSuccessfully => 'PDF enregistré avec succès';

  @override
  String get pdfSaveFailed => 'Échec de l\'enregistrement du PDF';

  @override
  String get saveAs => 'Enregistrer sous';

  @override
  String get fileName => 'Nom de fichier';

  @override
  String get save => 'Enregistrer';

  @override
  String get scanPrefix => 'scanner';

  @override
  String get addMoreImages => 'Ajouter plus d\'images';

  @override
  String get takePhoto => 'Prendre une photo';

  @override
  String get importFromAlbum => 'Importer de l\'album';

  @override
  String get importFromOtherApps => 'Import from Other Apps';

  @override
  String get aiProcessing => 'AI Processing';

  @override
  String get lowResolution => 'Low Resolution';

  @override
  String imagePreviewTitle(int currentPage, int totalPages) {
    return 'Image $currentPage of $totalPages';
  }

  @override
  String get confirmExit => 'Confirm Exit';

  @override
  String get discardCropChangesContent =>
      'You haven\'t saved. Are you sure you want to discard the manually adjusted coordinates?';

  @override
  String get continueAdjusting => 'Cancel';

  @override
  String get discardChangesConfirmation => 'Confirm';

  @override
  String get restoreDefault => 'Restore Default';

  @override
  String get confirmRestoreDefaultContent =>
      'This action will discard your manually adjusted coordinates. Do you want to discard your recent adjustments?';

  @override
  String get imageNotLoadedError =>
      'Image not loaded yet, please try again later.';

  @override
  String get imageProcessingFailedError =>
      'Image processing failed, please try again.';

  @override
  String get dragCornersHint =>
      'Drag the blue dots to adjust the four corners of the crop area.';

  @override
  String get splashTitle => 'Outil magique Photo vers PDF';

  @override
  String get splashSubtitle =>
      'Organisation automatique des photos de réunion, recadrage et fusion PDF en un tap';

  @override
  String get splashOriginalPhoto => 'Photo originale';

  @override
  String get splashGeneratedPdf => 'PDF auto-généré';

  @override
  String get splashWorkflow => 'Démarrage de l\'app, veuillez patienter...';

  @override
  String get recent => 'Récents';

  @override
  String get favorite => 'Favoris';

  @override
  String loadedFiles(int count) {
    return '$count fichiers analysés';
  }

  @override
  String get cannotLoadDirectoryFiles =>
      'Impossible de charger les fichiers du répertoire';

  @override
  String get cannotSelectFiles =>
      'Impossible de sélectionner les fichiers, veuillez vérifier les autorisations';

  @override
  String get directorySelectionOnlyMobile =>
      'La sélection de répertoire n\'est disponible que sur iOS/Android';

  @override
  String get selectingDirectory => 'Analyse du répertoire...';

  @override
  String get cannotSelectDirectory =>
      'Impossible de sélectionner le répertoire, veuillez vérifier les autorisations';

  @override
  String get authorizeFolder => 'Autoriser le dossier';

  @override
  String get sort => 'Trier';

  @override
  String get nameSort => 'Nom';

  @override
  String get lastModified => 'Dernière modification';

  @override
  String get sizeSort => 'Taille';

  @override
  String get descending => 'Décroissant';

  @override
  String get apply => 'Appliquer';

  @override
  String get originalPhotosFolder => 'Original Photos';

  @override
  String get searchInPdf => 'Search in PDF';

  @override
  String get searchText => 'Search text';

  @override
  String get search => 'Search';

  @override
  String get unsavedChanges => 'Unsaved Changes';

  @override
  String get unsavedChangesMessage =>
      'You have unsaved changes. Are you sure you want to exit?';

  @override
  String get discardChanges => 'Discard Changes';

  @override
  String get rotate => 'Rotate';

  @override
  String get edit => 'Edit';

  @override
  String get thumbnails => 'Thumbnails';

  @override
  String get pdfSaved => 'PDF saved successfully';

  @override
  String selectAuthorizedDirectorySubtitle(String appName, String deviceType) {
    return 'Autoriser l\'accès aux répertoires de fichiers iPhone/iPad';
  }

  @override
  String get shareAppSubtitle => 'Je vous recommande un outil PDF magique :';

  @override
  String get shareAppSettingsSubtitle => 'Recommend this app to your friends';

  @override
  String get weLikeYouToo => 'Merci pour votre affection !';

  @override
  String get thankYouForFeedback =>
      'Votre note parfaite nous motive à nous améliorer !';

  @override
  String get theBestWeCanGet => 'Fantastique, note parfaite 5 étoiles !';

  @override
  String get maybeLater => 'Peut-être plus tard';

  @override
  String get rateNow => 'Évaluer maintenant dans le store';

  @override
  String get languageSettings => 'Langue de l\'interface utilisateur';

  @override
  String get selectAuthorizedDirectory => 'Gérer le répertoire autorisé';

  @override
  String get shareApp => 'Partager';

  @override
  String get rateApp => 'Évaluer';

  @override
  String get rateAppSubtitle => 'Évaluez-nous sur l\'App Store';

  @override
  String get aboutApp => 'À propos';

  @override
  String get aboutAppSubtitle =>
      'Version de l\'application, politiques et plus';

  @override
  String get systemLanguage => 'Langue du système';

  @override
  String currentLanguage(String languageName, Object language) {
    return 'Actuel : $language';
  }

  @override
  String get selectLanguage => 'Sélectionner la langue';

  @override
  String defaultAppName(int number) {
    return 'App $number';
  }

  @override
  String get cannotAddDirectory =>
      'L\'autorisation du répertoire a échoué, veuillez réessayer';

  @override
  String get directoryRemoved => 'Autorisation du répertoire supprimée';

  @override
  String get cancelAuthorization => 'Annuler l\'autorisation';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return 'Voulez-vous vraiment annuler l\'autorisation d\'accès pour $directoryName de $appName ? Après l\'annulation, les fichiers de ce répertoire ne seront plus visibles.';
  }

  @override
  String get noKeepIt => 'Non, garder';

  @override
  String get yesRemoveFolder => 'Oui, je n\'ai plus besoin de ce dossier';

  @override
  String filesCount(int count) {
    return '$count fichiers';
  }

  @override
  String directorySize(String size) {
    return 'Taille : $size';
  }

  @override
  String get editName => 'Modifier le nom';

  @override
  String get removeDirectory => 'Supprimer';

  @override
  String get noAuthorizedDirectories => 'Aucun répertoire autorisé';

  @override
  String get noAuthorizedDirectoriesSubtitle =>
      'Ajoutez des répertoires pour accéder aux fichiers de ces emplacements';

  @override
  String get addDirectory => 'Ajouter un répertoire';

  @override
  String get authorizedDirectories => 'Gérer les répertoires autorisés';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appName peut accéder aux répertoires suivants :';
  }

  @override
  String get nameForNewFolder =>
      'Pour une identification facile plus tard, veuillez donner un nom au dossier d\'application que vous venez de sélectionner';

  @override
  String get enterCustomName => 'Saisir un nom personnalisé';

  @override
  String get customName => 'Nom personnalisé';

  @override
  String get version => 'Version';

  @override
  String get appVersion => '1.0.0';

  @override
  String get appDescription =>
      'Traitement intelligent de documents. Recadrage et correction automatiques par IA pour un rendu professionnel.';

  @override
  String get privacyPolicy => 'Politique de confidentialité';

  @override
  String get termsOfService => 'Conditions d\'utilisation';

  @override
  String get copyright => '© 2025';

  @override
  String get noFavoriteFiles =>
      'Vous n\'avez encore mis aucun fichier en favori';

  @override
  String get noFavoriteFilesHint =>
      'Vous pouvez mettre des fichiers en favori en appuyant sur l\'icône étoile ☆ à côté des fichiers sur la page des documents';

  @override
  String get fileNotFound => 'Fichier non trouvé';

  @override
  String get noRecentFiles =>
      'Les fichiers PDF/Word/Excel/PPT que vous avez ouverts dans cette app s\'afficheront ici';

  @override
  String noFilesFoundForType(int count, String fileType) {
    return 'Trouvé $count fichier(s) $fileType';
  }

  @override
  String get selectFolder => 'Autoriser le dossier';

  @override
  String get selectFolderSubtitle =>
      'Autoriser cette application à trouver les répertoires de fichiers PDF/Word/Excel/PPT';

  @override
  String get photoToPdfSubtitle =>
      'Numérisez des diapositives de présentation ou des notes dans un seul PDF';

  @override
  String get mergeImagesToPdf => 'Fusionner des images en PDF';

  @override
  String get mergeImagesToPdfSubtitle =>
      'Fusionner les photos de l\'album en PDF';

  @override
  String get fabTutorial =>
      'Cliquez ici pour numériser des documents avec votre appareil photo ou fusionner des images de votre galerie en un PDF.';

  @override
  String get iGotIt => 'J\'ai compris';

  @override
  String get signatures => 'Signatures';

  @override
  String get manageSignatures => 'Gérer vos signatures manuscrites';

  @override
  String get manageSignaturesHint =>
      'Gérez vos signatures manuscrites ici. Appuyez sur le bouton + pour ajouter votre signature. Vous pourrez ensuite utiliser ces signatures lors de l\'édition de PDF.';

  @override
  String get addSignature => 'Ajouter une signature';

  @override
  String get deleteSignature => 'Supprimer la signature';

  @override
  String get confirmDeleteSignature =>
      'Êtes-vous sûr de vouloir supprimer cette signature?';

  @override
  String get createSignature => 'Créer une signature';

  @override
  String get saveSignature => 'Enregistrer la signature';

  @override
  String get strokeWidth => 'Épaisseur du trait';

  @override
  String get color => 'Couleur';

  @override
  String get eraser => 'Gomme';

  @override
  String get thin => 'Fin';

  @override
  String get medium => 'Moyen';

  @override
  String get thick => 'Épais';

  @override
  String get signatureCreated => 'Signature créée avec succès';

  @override
  String get editSignature => 'Edit Signature';

  @override
  String get signatureUpdated => 'Signature updated successfully';

  @override
  String get signatureDeleted => 'Signature supprimée';

  @override
  String get noSignatures => 'Aucune signature';

  @override
  String get newSignature => 'Nouvelle signature';

  @override
  String get signatureUsageHint =>
      'Vous pouvez utiliser ces signatures lors de l\'édition de PDF';

  @override
  String get scanningFiles =>
      'Recherche de fichiers PDF/Word/Excel/PPT sur votre appareil';

  @override
  String pageRotated(int page, int degrees) {
    return 'Page $page pivotée de $degrees°';
  }

  @override
  String get rotationFailed => 'Échec de la rotation';

  @override
  String get closeSearch => 'Fermer la recherche';

  @override
  String get previous => 'Précédent';

  @override
  String get next => 'Suivant';

  @override
  String get cancelSignatureEdit => 'Annuler la modification de signature';

  @override
  String get back => 'Retour';

  @override
  String get editMode => 'Mode d\'édition';

  @override
  String get highlightText => 'Surligner le texte';

  @override
  String get draw => 'Dessiner';

  @override
  String get signature => 'Signature';

  @override
  String get highlightColor => 'Couleur de surlignage';

  @override
  String get brushColor => 'Couleur du pinceau';

  @override
  String get thickness => 'Épaisseur';

  @override
  String get confirmPlacement => 'Confirmer le placement';

  @override
  String get dragToAdjustHint => 'Faites glisser pour ajuster';

  @override
  String get selectSignature => 'Sélectionner une signature';

  @override
  String get noSignaturesPleaseCreate =>
      'Aucune signature, veuillez en créer une';

  @override
  String pageIndicator(int current, int total) {
    return 'Page $current/$total';
  }

  @override
  String get androidDirectoryManagementSubtitle =>
      'Gérer et ajouter des dossiers accessibles';

  @override
  String get loadingDocument => 'Chargement du document...';

  @override
  String get cannotOpenFile => 'Impossible d\'ouvrir le fichier';

  @override
  String get galleryAccessError =>
      'Impossible d\'accéder aux images sélectionnées';

  @override
  String get permissionDenied => 'Permission d\'accès aux photos refusée';

  @override
  String get invalidImageSelected => 'Image invalide sélectionnée';

  @override
  String get gallerySelectionFailed =>
      'Échec de la sélection d\'images depuis la galerie';

  @override
  String get unexpectedError => 'Une erreur inattendue s\'est produite';

  @override
  String get importFailed => 'Échec de l\'importation';

  @override
  String get importResults => 'Résultats de l\'importation';

  @override
  String allImportsFailed(int count) {
    return 'Échec de l\'importation des $count images sélectionnées';
  }

  @override
  String partialImportSuccess(int successCount, int failCount) {
    return '$successCount images importées avec succès, $failCount échecs';
  }

  @override
  String successfullyImported(int count) {
    return '✅ Importation réussie : $count images';
  }

  @override
  String failedToImport(int count) {
    return '❌ Échec de l\'importation : $count images';
  }

  @override
  String get tryAgainOrUseCamera =>
      'Veuillez réessayer ou utiliser l\'appareil photo';

  @override
  String get checkPermissionsInSettings =>
      'Veuillez vérifier les permissions de l\'app dans les Paramètres';

  @override
  String get trySelectingDifferentImages =>
      'Veuillez essayer de sélectionner d\'autres images';

  @override
  String get selectedImagesCorruptedOrInaccessible =>
      'Les images sélectionnées peuvent être corrompues ou inaccessibles. Veuillez essayer de sélectionner d\'autres images.';

  @override
  String get failedFiles => 'Fichiers échoués :';

  @override
  String andXMore(int count) {
    return '... et $count de plus';
  }

  @override
  String get tryAgain => 'Réessayer';

  @override
  String get details => 'Détails';

  @override
  String get ok => 'OK';

  @override
  String get galleryLoadFailed => 'Échec du chargement de la galerie';

  @override
  String get fileSelectionFailed => 'Échec de la sélection de fichier';

  @override
  String get storagePermissionDenied => 'Permission de stockage refusée';

  @override
  String get unsupportedFileType => 'Type de fichier non supporté sélectionné';

  @override
  String get playSlideshow => 'Play Slideshow';

  @override
  String get slideshowMode => 'Slideshow Mode';

  @override
  String get exitSlideshow => 'Exit Slideshow';

  @override
  String get searchTitle => 'Rechercher';

  @override
  String get searchHint => 'Entrez le nom du fichier';

  @override
  String get noResults => 'Aucun résultat';

  @override
  String iosPermissionMessage(String deviceType) {
    return 'En raison des restrictions d\'autorisation de l\'iPhone, vous devez d\'abord autoriser l\'accès aux dossiers contenant vos fichiers PDF, Word, Excel et PPT.';
  }

  @override
  String get waitForDocLoad =>
      'Veuillez attendre que le document soit entièrement chargé avant de préparer le PDF';

  @override
  String get generatingPdf => 'Génération de PDF...';

  @override
  String get pdfGenerationFailed =>
      'La préparation du PDF a échoué, veuillez réessayer';

  @override
  String pdfConversionFailed(String error) {
    return 'La conversion en PDF a échoué : $error';
  }

  @override
  String get conversionSuccess => 'Conversion réussie';

  @override
  String pdfSavedMessage(String fileName) {
    return 'Enregistré en tant que fichier PDF $fileName, ouvrir pour voir maintenant ?';
  }

  @override
  String get stayOnPage => 'Rester sur la page';

  @override
  String get openPdf => 'Ouvrir le PDF';

  @override
  String get generatePdfVersion => 'Générer la version PDF';

  @override
  String get generatePdfToEdit =>
      'Une version PDF doit être générée pour modifier ou faire pivoter. Continuer ?';

  @override
  String get continueAction => 'Continuer';

  @override
  String get openingCachedPdf => 'Ouverture du PDF mis en cache...';

  @override
  String get saveAsPdf => 'Enregistrer en tant que PDF';

  @override
  String get caseSensitive => 'Sensible à la casse';

  @override
  String notFound(String query) {
    return '\"$query\" non trouvé';
  }

  @override
  String get previousMatch => 'Précédent';

  @override
  String get nextMatch => 'Suivant';

  @override
  String get webViewOnlyIos =>
      'La visualisation de documents WebView n\'est prise en charge que sur iOS';

  @override
  String webViewFailed(String error) {
    return 'Le chargement de WebView a échoué : $error';
  }

  @override
  String get officeTimeout =>
      'Le chargement du document Office a expiré. Cela peut être dû à un fichier volumineux ou à un format non pris en charge.';

  @override
  String get addSlideshowButton => 'Ajouter un bouton de diaporama';

  @override
  String get threshold400px => 'Seuil : 400px';

  @override
  String get confirmClear => 'Confirmer la suppression';

  @override
  String get confirmClearSignature =>
      'Cela effacera entièrement votre image de signature. Êtes-vous sûr de vouloir l\'effacer ?';

  @override
  String get clear => 'Effacer';

  @override
  String iosNetworkPermissionGuide(String appName) {
    return 'Veuillez trouver \"$appName\" dans les réglages de votre iPhone et vous assurer que l\'accès au réseau est autorisé.\nChemin : Réglages > Confidentialité et sécurité > Réseau local > $appName';
  }

  @override
  String get checkNetworkConnection =>
      'Veuillez vérifier votre connexion réseau';

  @override
  String get networkPermission => 'Autorisation réseau';

  @override
  String get networkPermissionRequest =>
      'Cette application a besoin d\'accéder au réseau pour traiter et améliorer les images dans les documents PDF.';

  @override
  String get featuresInclude => 'Les fonctionnalités incluent :';

  @override
  String get imageClarityEnhancement => 'Amélioration de la clarté de l\'image';

  @override
  String get documentEdgeDetection => 'Détection des bords du document';

  @override
  String get imageOptimization => 'Optimisation de l\'image';

  @override
  String get requestPermission => 'Demander l\'autorisation';

  @override
  String get networkConnectionProblem => 'Problème de connexion réseau';

  @override
  String get networkStatusDescription =>
      'La connexion réseau n\'est pas disponible, veuillez vérifier vos paramètres réseau';

  @override
  String get troubleshootingSuggestions => 'Suggestions de dépannage :';

  @override
  String get networkTroubleshootingTips =>
      'Vérifiez si le Wi-Fi ou les données mobiles sont activés\nConfirmez que l\'appareil est connecté à un réseau utilisable\nVérifiez si le mode avion est activé\nEssayez d\'accéder à d\'autres applications ou sites Web pour confirmer l\'état du réseau\nRedémarrez la connexion réseau ou l\'appareil';

  @override
  String get retry => 'Réessayer';

  @override
  String get unfavorite => 'Unfavorite';

  @override
  String get open => 'Open';

  @override
  String get rename => 'Rename';

  @override
  String get cannotAccessFile =>
      'Cannot access this file. Please reauthorize in \"Authorized Directories\" or reselect the file.';

  @override
  String get installOfficeAppTitle => 'App required to open Office files';

  @override
  String get installOfficeAppContent =>
      'An app is required to open this file. Install from the app store?';

  @override
  String get install => 'Install';

  @override
  String get renameFile => 'Rename';

  @override
  String get renamed => 'Renamed';

  @override
  String renameFailed(String error) {
    return 'Rename failed: $error';
  }

  @override
  String confirmDeleteFile(String fileName) {
    return 'Confirm delete file:\n$fileName?';
  }

  @override
  String get deleted => 'Deleted';

  @override
  String deleteFailed(String error) {
    return 'Delete failed: $error';
  }

  @override
  String get networkConnected => 'Réseau connecté';

  @override
  String get networkDisconnected => 'Réseau déconnecté';

  @override
  String get networkAccessDenied => 'Accès au réseau refusé';

  @override
  String get networkOperationFailed => 'Échec de l\'opération réseau';

  @override
  String get connected => 'Connected';

  @override
  String get noNetwork => 'No network';

  @override
  String get thumbnailsOnlyIos =>
      'Les miniatures ne sont disponibles que sur iOS';

  @override
  String get waitDocumentComplete =>
      'En attente de la fin du chargement du document...';

  @override
  String get thumbnailDescription =>
      'Miniature du document complet\nPratique pour une navigation rapide';

  @override
  String get generatingThumbnail =>
      'Génération de la miniature du document complet...';

  @override
  String get analyzingDocument =>
      'Analyse de la structure et des dimensions du document';

  @override
  String get thumbnailUnavailable =>
      'La miniature est temporairement indisponible';

  @override
  String get clickRefreshRetry =>
      'Cliquez sur le bouton actualiser en haut à droite pour réessayer';
}
