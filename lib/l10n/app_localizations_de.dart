// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get appName => 'Alle PDF-Editor';

  @override
  String get documents => 'Dokumente';

  @override
  String get tools => 'Tools';

  @override
  String get settings => 'Einstellungen';

  @override
  String get home => 'Home';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get files => 'Files';

  @override
  String get photoToPdf => 'Scannen als PDF';

  @override
  String get imageToPdf => 'Bild zu PDF';

  @override
  String get addImages => 'Add Images';

  @override
  String addImagesFromGallery(int count) {
    return '$count Bilder aus der Galerie hinzufügen';
  }

  @override
  String addImagesFromFiles(int count) {
    return 'Add $count images from files';
  }

  @override
  String get convert => 'Konvertieren';

  @override
  String get undo => 'Rückgängig';

  @override
  String get discardChangesTitle => 'Änderungen verwerfen?';

  @override
  String get discardChangesContent =>
      'Sie haben nicht gespeicherte Änderungen. Wenn Sie jetzt beenden, gehen diese verloren.';

  @override
  String get keepEditing => 'Weiter bearbeiten';

  @override
  String get discard => 'Verwerfen';

  @override
  String get takePicture => 'Foto aufnehmen';

  @override
  String get reorderImage => 'Bilder neu ordnen';

  @override
  String get cropImage => 'Bild zuschneiden';

  @override
  String get deleteImage => 'Bild löschen';

  @override
  String get confirmDeleteImage =>
      'Sind Sie sicher, dass Sie dieses Bild löschen möchten?';

  @override
  String get cancel => 'Abbrechen';

  @override
  String get delete => 'Löschen';

  @override
  String confirmUndo(String action) {
    return 'Rückgängig: $action. Dies macht Ihre letzte Aktion rückgängig. Fortfahren?';
  }

  @override
  String get pdfSavedSuccessfully => 'PDF erfolgreich gespeichert';

  @override
  String get pdfSaveFailed => 'PDF-Speicherung fehlgeschlagen';

  @override
  String get saveAs => 'Speichern als';

  @override
  String get fileName => 'Dateiname';

  @override
  String get save => 'Speichern';

  @override
  String get scanPrefix => 'scan';

  @override
  String get addMoreImages => 'Mehr Bilder hinzufügen';

  @override
  String get takePhoto => 'Foto aufnehmen';

  @override
  String get importFromAlbum => 'Aus Album importieren';

  @override
  String get importFromOtherApps => 'Import from Other Apps';

  @override
  String get aiProcessing => 'AI Processing';

  @override
  String get lowResolution => 'Low Resolution';

  @override
  String imagePreviewTitle(int currentPage, int totalPages) {
    return 'Image $currentPage of $totalPages';
  }

  @override
  String get confirmExit => 'Confirm Exit';

  @override
  String get discardCropChangesContent =>
      'You haven\'t saved. Are you sure you want to discard the manually adjusted coordinates?';

  @override
  String get continueAdjusting => 'Cancel';

  @override
  String get discardChangesConfirmation => 'Confirm';

  @override
  String get restoreDefault => 'Restore Default';

  @override
  String get confirmRestoreDefaultContent =>
      'This action will discard your manually adjusted coordinates. Do you want to discard your recent adjustments?';

  @override
  String get imageNotLoadedError =>
      'Image not loaded yet, please try again later.';

  @override
  String get imageProcessingFailedError =>
      'Image processing failed, please try again.';

  @override
  String get dragCornersHint =>
      'Drag the blue dots to adjust the four corners of the crop area.';

  @override
  String get splashTitle => 'Foto-zu-PDF Magic Tool';

  @override
  String get splashSubtitle =>
      'Meeting-Fotos automatisch organisieren, mit einem Tap zuschneiden und zu PDF zusammenfügen';

  @override
  String get splashOriginalPhoto => 'Original Foto';

  @override
  String get splashGeneratedPdf => 'Automatisch erstelltes PDF';

  @override
  String get splashWorkflow => 'App wird gestartet, bitte warten...';

  @override
  String get recent => 'Kürzlich';

  @override
  String get favorite => 'Favoriten';

  @override
  String loadedFiles(int count) {
    return '$count Dateien gescannt';
  }

  @override
  String get cannotLoadDirectoryFiles =>
      'Verzeichnisdateien können nicht geladen werden';

  @override
  String get cannotSelectFiles =>
      'Dateien können nicht ausgewählt werden, bitte Berechtigungen überprüfen';

  @override
  String get directorySelectionOnlyMobile =>
      'Verzeichnisauswahl ist nur auf iOS/Android verfügbar';

  @override
  String get selectingDirectory => 'Verzeichnis wird gescannt...';

  @override
  String get cannotSelectDirectory =>
      'Verzeichnis kann nicht ausgewählt werden, bitte Berechtigungen überprüfen';

  @override
  String get authorizeFolder => 'Ordner autorisieren';

  @override
  String get sort => 'Sortieren';

  @override
  String get nameSort => 'Name';

  @override
  String get lastModified => 'Zuletzt geändert';

  @override
  String get sizeSort => 'Größe';

  @override
  String get descending => 'Absteigend';

  @override
  String get apply => 'Anwenden';

  @override
  String get originalPhotosFolder => 'Original Photos';

  @override
  String get searchInPdf => 'Search in PDF';

  @override
  String get searchText => 'Search text';

  @override
  String get search => 'Search';

  @override
  String get unsavedChanges => 'Unsaved Changes';

  @override
  String get unsavedChangesMessage =>
      'You have unsaved changes. Are you sure you want to exit?';

  @override
  String get discardChanges => 'Discard Changes';

  @override
  String get rotate => 'Rotate';

  @override
  String get edit => 'Edit';

  @override
  String get thumbnails => 'Thumbnails';

  @override
  String get pdfSaved => 'PDF saved successfully';

  @override
  String selectAuthorizedDirectorySubtitle(String appName, String deviceType) {
    return 'Zugriff auf iPhone/iPad-Dateiverzeichnisse autorisieren';
  }

  @override
  String get shareAppSubtitle => 'Empfehlung eines PDF Magic Tools:';

  @override
  String get shareAppSettingsSubtitle => 'Recommend this app to your friends';

  @override
  String get weLikeYouToo => 'Danke für Ihre Wertschätzung!';

  @override
  String get thankYouForFeedback =>
      'Ihre 5-Sterne-Bewertung motiviert uns zur Verbesserung!';

  @override
  String get theBestWeCanGet => 'Fantastisch, 5-Sterne-Bewertung!';

  @override
  String get maybeLater => 'Vielleicht später';

  @override
  String get rateNow => 'Jetzt im Store bewerten';

  @override
  String get languageSettings => 'UI-Sprache';

  @override
  String get selectAuthorizedDirectory => 'Autorisiertes Verzeichnis verwalten';

  @override
  String get shareApp => 'Teilen';

  @override
  String get rateApp => 'Bewerten';

  @override
  String get rateAppSubtitle => 'Bewerten Sie uns im App Store';

  @override
  String get aboutApp => 'Über';

  @override
  String get aboutAppSubtitle => 'App-Version, Richtlinien und mehr';

  @override
  String get systemLanguage => 'Systemsprache';

  @override
  String currentLanguage(String languageName, Object language) {
    return 'Aktuell: $language';
  }

  @override
  String get selectLanguage => 'Sprache auswählen';

  @override
  String defaultAppName(int number) {
    return 'App $number';
  }

  @override
  String get cannotAddDirectory =>
      'Verzeichnis-Autorisierung fehlgeschlagen, bitte erneut versuchen';

  @override
  String get directoryRemoved => 'Verzeichnis-Autorisierung entfernt';

  @override
  String get cancelAuthorization => 'Autorisierung abbrechen';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return 'Möchten Sie die Zugriffsberechtigung für $appName’s $directoryName wirklich aufheben? Nach der Aufhebung sind die Dateien in diesem Verzeichnis nicht mehr sichtbar.';
  }

  @override
  String get noKeepIt => 'Nein, behalten';

  @override
  String get yesRemoveFolder => 'Ja, ich brauche diesen Ordner nicht mehr';

  @override
  String filesCount(int count) {
    return '$count Dateien';
  }

  @override
  String directorySize(String size) {
    return 'Größe: $size';
  }

  @override
  String get editName => 'Namen bearbeiten';

  @override
  String get removeDirectory => 'Entfernen';

  @override
  String get noAuthorizedDirectories => 'Keine autorisierten Verzeichnisse';

  @override
  String get noAuthorizedDirectoriesSubtitle =>
      'Verzeichnisse hinzufügen, um auf Dateien von diesen Standorten zuzugreifen';

  @override
  String get addDirectory => 'Verzeichnis hinzufügen';

  @override
  String get authorizedDirectories => 'Autorisierte Verzeichnisse verwalten';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appName kann auf die folgenden Verzeichnisse zugreifen:';
  }

  @override
  String get nameForNewFolder =>
      'Geben Sie zur späteren einfachen Identifizierung einen Namen für den gerade ausgewählten App-Ordner ein';

  @override
  String get enterCustomName => 'Benutzerdefinierten Namen eingeben';

  @override
  String get customName => 'Benutzerdefinierter Name';

  @override
  String get version => 'Version';

  @override
  String get appVersion => '1.0.0';

  @override
  String get appDescription =>
      'Intelligente Dokumentenverarbeitung. KI-automatisches Zuschneiden und Korrigieren für professionelle Ergebnisse.';

  @override
  String get privacyPolicy => 'Datenschutzrichtlinie';

  @override
  String get termsOfService => 'Nutzungsbedingungen';

  @override
  String get copyright => '© 2025';

  @override
  String get noFavoriteFiles => 'Sie haben noch keine Dateien favorisiert';

  @override
  String get noFavoriteFilesHint =>
      'Sie können Dateien favorisieren, indem Sie das ☆ Stern-Symbol neben den Dateien auf der Dokumentenseite antippen';

  @override
  String get fileNotFound => 'Datei nicht gefunden';

  @override
  String get noRecentFiles =>
      'PDF/Word/Excel/PPT-Dateien, die Sie in dieser App geöffnet haben, werden hier angezeigt';

  @override
  String noFilesFoundForType(int count, String fileType) {
    return '$count $fileType-Dateien gefunden';
  }

  @override
  String get selectFolder => 'Ordner autorisieren';

  @override
  String get selectFolderSubtitle =>
      'Diese App berechtigen, PDF/Word/Excel/PPT-Dateiverzeichnisse zu finden';

  @override
  String get photoToPdfSubtitle =>
      'Präsentationsfolien oder Notizen in eine PDF-Datei scannen';

  @override
  String get mergeImagesToPdf => 'Bilder zu PDF zusammenführen';

  @override
  String get mergeImagesToPdfSubtitle => 'Album-Bilder zu PDF zusammenführen';

  @override
  String get fabTutorial =>
      'Klicken Sie hier, um Dokumente mit Ihrer Kamera zu scannen oder Bilder aus Ihrer Galerie zu einem PDF zusammenzufügen.';

  @override
  String get iGotIt => 'Ich hab\'s';

  @override
  String get signatures => 'Unterschriften';

  @override
  String get manageSignatures => 'Handschriftliche Unterschriften verwalten';

  @override
  String get manageSignaturesHint =>
      'Verwalten Sie hier Ihre handschriftlichen Unterschriften. Tippen Sie auf die +-Schaltfläche, um Ihre Unterschrift hinzuzufügen. Sie können diese Unterschriften dann beim Bearbeiten von PDFs verwenden.';

  @override
  String get addSignature => 'Unterschrift hinzufügen';

  @override
  String get deleteSignature => 'Unterschrift löschen';

  @override
  String get confirmDeleteSignature =>
      'Sind Sie sicher, dass Sie diese Unterschrift löschen möchten?';

  @override
  String get createSignature => 'Unterschrift erstellen';

  @override
  String get saveSignature => 'Unterschrift speichern';

  @override
  String get strokeWidth => 'Strichstärke';

  @override
  String get color => 'Farbe';

  @override
  String get eraser => 'Radiergummi';

  @override
  String get thin => 'Dünn';

  @override
  String get medium => 'Mittel';

  @override
  String get thick => 'Dick';

  @override
  String get signatureCreated => 'Unterschrift erfolgreich erstellt';

  @override
  String get editSignature => 'Edit Signature';

  @override
  String get signatureUpdated => 'Signature updated successfully';

  @override
  String get signatureDeleted => 'Unterschrift gelöscht';

  @override
  String get noSignatures => 'Keine Unterschriften';

  @override
  String get newSignature => 'Neue Unterschrift';

  @override
  String get signatureUsageHint =>
      'Sie können diese Unterschriften beim Bearbeiten von PDFs verwenden';

  @override
  String get scanningFiles =>
      'Suche nach PDF/Word/Excel/PPT-Dateien auf Ihrem Gerät';

  @override
  String pageRotated(int page, int degrees) {
    return 'Seite $page um $degrees° gedreht';
  }

  @override
  String get rotationFailed => 'Drehung fehlgeschlagen';

  @override
  String get closeSearch => 'Suche schließen';

  @override
  String get previous => 'Vorherige';

  @override
  String get next => 'Nächste';

  @override
  String get cancelSignatureEdit => 'Signatur-Bearbeitung abbrechen';

  @override
  String get back => 'Zurück';

  @override
  String get editMode => 'Bearbeitungsmodus';

  @override
  String get highlightText => 'Text markieren';

  @override
  String get draw => 'Zeichnen';

  @override
  String get signature => 'Signatur';

  @override
  String get highlightColor => 'Hervorhebungsfarbe';

  @override
  String get brushColor => 'Pinselfarbe';

  @override
  String get thickness => 'Dicke';

  @override
  String get confirmPlacement => 'Platzierung bestätigen';

  @override
  String get dragToAdjustHint => 'Ziehen Sie, um anzupassen';

  @override
  String get selectSignature => 'Signatur auswählen';

  @override
  String get noSignaturesPleaseCreate =>
      'Keine Signaturen, bitte erstellen Sie eine';

  @override
  String pageIndicator(int current, int total) {
    return 'Seite $current/$total';
  }

  @override
  String get androidDirectoryManagementSubtitle =>
      'Verwalten und hinzufügen zugänglicher Ordner';

  @override
  String get loadingDocument => 'Dokument wird geladen...';

  @override
  String get cannotOpenFile => 'Datei kann nicht geöffnet werden';

  @override
  String get galleryAccessError =>
      'Zugriff auf ausgewählte Bilder nicht möglich';

  @override
  String get permissionDenied => 'Berechtigung für Foto-Zugriff verweigert';

  @override
  String get invalidImageSelected => 'Ungültiges Bild ausgewählt';

  @override
  String get gallerySelectionFailed =>
      'Auswahl von Bildern aus Galerie fehlgeschlagen';

  @override
  String get unexpectedError => 'Ein unerwarteter Fehler ist aufgetreten';

  @override
  String get importFailed => 'Import fehlgeschlagen';

  @override
  String get importResults => 'Import-Ergebnisse';

  @override
  String allImportsFailed(int count) {
    return 'Import aller $count ausgewählten Bilder fehlgeschlagen';
  }

  @override
  String partialImportSuccess(int successCount, int failCount) {
    return '$successCount Bilder erfolgreich importiert, $failCount fehlgeschlagen';
  }

  @override
  String successfullyImported(int count) {
    return '✅ Erfolgreich importiert: $count Bilder';
  }

  @override
  String failedToImport(int count) {
    return '❌ Import fehlgeschlagen: $count Bilder';
  }

  @override
  String get tryAgainOrUseCamera =>
      'Bitte erneut versuchen oder Kamera verwenden';

  @override
  String get checkPermissionsInSettings =>
      'Bitte App-Berechtigungen in Einstellungen überprüfen';

  @override
  String get trySelectingDifferentImages => 'Bitte andere Bilder auswählen';

  @override
  String get selectedImagesCorruptedOrInaccessible =>
      'Die ausgewählten Bilder könnten beschädigt oder nicht zugänglich sein. Bitte andere Bilder auswählen.';

  @override
  String get failedFiles => 'Fehlgeschlagene Dateien:';

  @override
  String andXMore(int count) {
    return '... und $count weitere';
  }

  @override
  String get tryAgain => 'Erneut versuchen';

  @override
  String get details => 'Details';

  @override
  String get ok => 'OK';

  @override
  String get galleryLoadFailed => 'Galerie-Laden fehlgeschlagen';

  @override
  String get fileSelectionFailed => 'Dateiauswahl fehlgeschlagen';

  @override
  String get storagePermissionDenied => 'Speicher-Berechtigung verweigert';

  @override
  String get unsupportedFileType => 'Nicht unterstützter Dateityp ausgewählt';

  @override
  String get playSlideshow => 'Play Slideshow';

  @override
  String get slideshowMode => 'Slideshow Mode';

  @override
  String get exitSlideshow => 'Exit Slideshow';

  @override
  String get searchTitle => 'Suchen';

  @override
  String get searchHint => 'Dateinamen eingeben';

  @override
  String get noResults => 'Keine Ergebnisse';

  @override
  String iosPermissionMessage(String deviceType) {
    return 'Aufgrund der iPhone-Berechtigungsbeschränkungen müssen Sie zuerst den Zugriff auf die Ordner autorisieren, die Ihre PDF-, Word-, Excel- und PPT-Dateien enthalten.';
  }

  @override
  String get waitForDocLoad =>
      'Bitte warten Sie, bis das Dokument vollständig geladen ist, bevor Sie das PDF vorbereiten';

  @override
  String get generatingPdf => 'PDF wird generiert...';

  @override
  String get pdfGenerationFailed =>
      'PDF-Vorbereitung fehlgeschlagen, bitte versuchen Sie es erneut';

  @override
  String pdfConversionFailed(String error) {
    return 'PDF-Konvertierung fehlgeschlagen: $error';
  }

  @override
  String get conversionSuccess => 'Konvertierung erfolgreich';

  @override
  String pdfSavedMessage(String fileName) {
    return 'Als PDF-Datei $fileName gespeichert, jetzt zum Anzeigen öffnen?';
  }

  @override
  String get stayOnPage => 'Auf der Seite bleiben';

  @override
  String get openPdf => 'PDF öffnen';

  @override
  String get generatePdfVersion => 'PDF-Version erstellen';

  @override
  String get generatePdfToEdit =>
      'Zum Bearbeiten oder Drehen muss eine PDF-Version erstellt werden. Fortfahren?';

  @override
  String get continueAction => 'Fortfahren';

  @override
  String get openingCachedPdf => 'Zwischengespeichertes PDF wird geöffnet...';

  @override
  String get saveAsPdf => 'Als PDF speichern';

  @override
  String get caseSensitive => 'Groß-/Kleinschreibung beachten';

  @override
  String notFound(String query) {
    return '\"$query\" nicht gefunden';
  }

  @override
  String get previousMatch => 'Vorherige';

  @override
  String get nextMatch => 'Nächste';

  @override
  String get webViewOnlyIos =>
      'Die WebView-Dokumentenanzeige wird nur unter iOS unterstützt';

  @override
  String webViewFailed(String error) {
    return 'WebView konnte nicht geladen werden: $error';
  }

  @override
  String get officeTimeout =>
      'Zeitüberschreitung beim Laden des Office-Dokuments. Dies kann an einer großen Datei oder einem nicht unterstützten Format liegen.';

  @override
  String get addSlideshowButton => 'Diashow-Schaltfläche hinzufügen';

  @override
  String get threshold400px => 'Schwellenwert: 400px';

  @override
  String get confirmClear => 'Löschen bestätigen';

  @override
  String get confirmClearSignature =>
      'Dadurch wird Ihr gesamtes Signaturbild gelöscht. Sind Sie sicher, dass Sie es löschen möchten?';

  @override
  String get clear => 'Löschen';

  @override
  String iosNetworkPermissionGuide(String appName) {
    return 'Bitte finden Sie \"$appName\" in den Einstellungen Ihres iPhones und stellen Sie sicher, dass der Netzwerkzugriff erlaubt ist.\nPfad: Einstellungen > Datenschutz & Sicherheit > Lokales Netzwerk > $appName';
  }

  @override
  String get checkNetworkConnection =>
      'Bitte überprüfen Sie Ihre Netzwerkverbindung';

  @override
  String get networkPermission => 'Netzwerkberechtigung';

  @override
  String get networkPermissionRequest =>
      'Diese App benötigt Netzwerkzugriff, um Bilder in PDF-Dokumenten zu verarbeiten und zu verbessern.';

  @override
  String get featuresInclude => 'Funktionen umfassen:';

  @override
  String get imageClarityEnhancement => 'Verbesserung der Bildklarheit';

  @override
  String get documentEdgeDetection => 'Dokumentenkantenerkennung';

  @override
  String get imageOptimization => 'Bildoptimierung';

  @override
  String get requestPermission => 'Berechtigung anfordern';

  @override
  String get networkConnectionProblem => 'Netzwerkverbindungsproblem';

  @override
  String get networkStatusDescription =>
      'Netzwerkverbindung ist nicht verfügbar, bitte überprüfen Sie Ihre Netzwerkeinstellungen';

  @override
  String get troubleshootingSuggestions => 'Fehlerbehebungsvorschläge:';

  @override
  String get networkTroubleshootingTips =>
      'Überprüfen Sie, ob WLAN oder mobile Daten aktiviert sind\nBestätigen Sie, dass das Gerät mit einem nutzbaren Netzwerk verbunden ist\nÜberprüfen Sie, ob der Flugmodus aktiviert ist\nVersuchen Sie, auf andere Apps oder Websites zuzugreifen, um den Netzwerkstatus zu bestätigen\nStarten Sie die Netzwerkverbindung oder das Gerät neu';

  @override
  String get retry => 'Retry';

  @override
  String get unfavorite => 'Unfavorite';

  @override
  String get open => 'Open';

  @override
  String get rename => 'Rename';

  @override
  String get cannotAccessFile =>
      'Cannot access this file. Please reauthorize in \"Authorized Directories\" or reselect the file.';

  @override
  String get installOfficeAppTitle => 'App required to open Office files';

  @override
  String get installOfficeAppContent =>
      'An app is required to open this file. Install from the app store?';

  @override
  String get install => 'Install';

  @override
  String get renameFile => 'Rename';

  @override
  String get renamed => 'Renamed';

  @override
  String renameFailed(String error) {
    return 'Rename failed: $error';
  }

  @override
  String confirmDeleteFile(String fileName) {
    return 'Confirm delete file:\n$fileName?';
  }

  @override
  String get deleted => 'Deleted';

  @override
  String deleteFailed(String error) {
    return 'Delete failed: $error';
  }

  @override
  String get networkConnected => 'Netzwerk verbunden';

  @override
  String get networkDisconnected => 'Netzwerk getrennt';

  @override
  String get networkAccessDenied => 'Netzwerkzugriff verweigert';

  @override
  String get networkOperationFailed => 'Netzwerkvorgang fehlgeschlagen';

  @override
  String get connected => 'Connected';

  @override
  String get noNetwork => 'No network';

  @override
  String get thumbnailsOnlyIos => 'Miniaturansichten nur auf iOS verfügbar';

  @override
  String get waitDocumentComplete => 'Warten auf Dokumentladevorgang...';

  @override
  String get thumbnailDescription =>
      'Miniaturansicht des vollständigen Dokuments\nPraktisch für schnelle Navigation';

  @override
  String get generatingThumbnail =>
      'Erstelle Miniaturansicht des vollständigen Dokuments...';

  @override
  String get analyzingDocument =>
      'Analysiere Dokumentstruktur und -abmessungen';

  @override
  String get thumbnailUnavailable =>
      'Miniaturansicht vorübergehend nicht verfügbar';

  @override
  String get clickRefreshRetry =>
      'Klicken Sie auf die Aktualisieren-Schaltfläche oben rechts, um es erneut zu versuchen';
}
