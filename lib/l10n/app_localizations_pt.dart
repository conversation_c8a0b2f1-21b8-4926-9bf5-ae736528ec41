// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Portuguese (`pt`).
class AppLocalizationsPt extends AppLocalizations {
  AppLocalizationsPt([String locale = 'pt']) : super(locale);

  @override
  String get appName => 'Todo Editor de PDF';

  @override
  String get documents => 'Documentos';

  @override
  String get tools => 'Tools';

  @override
  String get settings => 'Configurações';

  @override
  String get home => 'Home';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get files => 'Files';

  @override
  String get photoToPdf => 'Foto para PDF';

  @override
  String get imageToPdf => 'Imagem para PDF';

  @override
  String get addImages => 'Add Images';

  @override
  String addImagesFromGallery(int count) {
    return 'Adicionar $count imagens da galeria';
  }

  @override
  String addImagesFromFiles(int count) {
    return 'Add $count images from files';
  }

  @override
  String get convert => 'Converter';

  @override
  String get undo => 'Desfazer';

  @override
  String get discardChangesTitle => 'Descartar alterações?';

  @override
  String get discardChangesContent =>
      'Você tem alterações não salvas. Se sair agora, elas serão perdidas.';

  @override
  String get keepEditing => 'Continuar editando';

  @override
  String get discard => 'Descartar';

  @override
  String get takePicture => 'Tirar foto';

  @override
  String get reorderImage => 'Reordenar imagens';

  @override
  String get cropImage => 'Cortar Imagem';

  @override
  String get deleteImage => 'Excluir Imagem';

  @override
  String get confirmDeleteImage =>
      'Tem certeza de que deseja excluir esta imagem?';

  @override
  String get cancel => 'Cancelar';

  @override
  String get delete => 'Excluir';

  @override
  String confirmUndo(String action) {
    return 'Desfazer: $action. Isso reverterá sua última ação. Continuar?';
  }

  @override
  String get pdfSavedSuccessfully => 'PDF salvo com sucesso';

  @override
  String get pdfSaveFailed => 'Falha ao salvar o PDF';

  @override
  String get saveAs => 'Salvar como';

  @override
  String get fileName => 'Nome do arquivo';

  @override
  String get save => 'Salvar';

  @override
  String get scanPrefix => 'digitalizar';

  @override
  String get addMoreImages => 'Adicionar mais imagens';

  @override
  String get takePhoto => 'Tirar Foto';

  @override
  String get importFromAlbum => 'Importar do Álbum';

  @override
  String get importFromOtherApps => 'Import from Other Apps';

  @override
  String get aiProcessing => 'AI Processing';

  @override
  String get lowResolution => 'Low Resolution';

  @override
  String imagePreviewTitle(int currentPage, int totalPages) {
    return 'Image $currentPage of $totalPages';
  }

  @override
  String get confirmExit => 'Confirm Exit';

  @override
  String get discardCropChangesContent =>
      'You haven\'t saved. Are you sure you want to discard the manually adjusted coordinates?';

  @override
  String get continueAdjusting => 'Cancel';

  @override
  String get discardChangesConfirmation => 'Confirm';

  @override
  String get restoreDefault => 'Restore Default';

  @override
  String get confirmRestoreDefaultContent =>
      'This action will discard your manually adjusted coordinates. Do you want to discard your recent adjustments?';

  @override
  String get imageNotLoadedError =>
      'Image not loaded yet, please try again later.';

  @override
  String get imageProcessingFailedError =>
      'Image processing failed, please try again.';

  @override
  String get dragCornersHint =>
      'Drag the blue dots to adjust the four corners of the crop area.';

  @override
  String get splashTitle => 'Ferramenta Mágica Foto para PDF';

  @override
  String get splashSubtitle =>
      'Organize automaticamente fotos de reuniões, recorte e mescle em PDF com um toque';

  @override
  String get splashOriginalPhoto => 'Foto Original';

  @override
  String get splashGeneratedPdf => 'PDF auto-gerado';

  @override
  String get splashWorkflow => 'Iniciando app, aguarde...';

  @override
  String get recent => 'Recentes';

  @override
  String get favorite => 'Favoritos';

  @override
  String loadedFiles(int count) {
    return '$count arquivos verificados';
  }

  @override
  String get cannotLoadDirectoryFiles =>
      'Não é possível carregar os arquivos do diretório';

  @override
  String get cannotSelectFiles =>
      'Não é possível selecionar arquivos, verifique as permissões';

  @override
  String get directorySelectionOnlyMobile =>
      'A seleção de diretório está disponível apenas no iOS/Android';

  @override
  String get selectingDirectory => 'Verificando diretório...';

  @override
  String get cannotSelectDirectory =>
      'Não é possível selecionar o diretório, verifique as permissões';

  @override
  String get authorizeFolder => 'Autorizar Pasta';

  @override
  String get sort => 'Classificar';

  @override
  String get nameSort => 'Nome';

  @override
  String get lastModified => 'Última Modificação';

  @override
  String get sizeSort => 'Tamanho';

  @override
  String get descending => 'Descendente';

  @override
  String get apply => 'Aplicar';

  @override
  String get originalPhotosFolder => 'Original Photos';

  @override
  String get searchInPdf => 'Search in PDF';

  @override
  String get searchText => 'Search text';

  @override
  String get search => 'Search';

  @override
  String get unsavedChanges => 'Unsaved Changes';

  @override
  String get unsavedChangesMessage =>
      'You have unsaved changes. Are you sure you want to exit?';

  @override
  String get discardChanges => 'Discard Changes';

  @override
  String get rotate => 'Rotate';

  @override
  String get edit => 'Edit';

  @override
  String get thumbnails => 'Thumbnails';

  @override
  String get pdfSaved => 'PDF saved successfully';

  @override
  String selectAuthorizedDirectorySubtitle(String appName, String deviceType) {
    return 'Autorizar acesso a diretórios de arquivos do iPhone/iPad';
  }

  @override
  String get shareAppSubtitle => 'Recomendo uma ferramenta PDF mágica:';

  @override
  String get shareAppSettingsSubtitle => 'Recommend this app to your friends';

  @override
  String get weLikeYouToo => 'Obrigado pelo seu carinho!';

  @override
  String get thankYouForFeedback =>
      'Sua avaliação perfeita é nossa motivação para melhorar!';

  @override
  String get theBestWeCanGet => 'Fantástico, avaliação perfeita de 5 estrelas!';

  @override
  String get maybeLater => 'Talvez mais tarde';

  @override
  String get rateNow => 'Avaliar agora na loja';

  @override
  String get languageSettings => 'Idioma da interface do usuário';

  @override
  String get selectAuthorizedDirectory => 'Gerenciar Diretório Autorizado';

  @override
  String get shareApp => 'Compartilhar';

  @override
  String get rateApp => 'Avaliar';

  @override
  String get rateAppSubtitle => 'Avalie-nos na App Store';

  @override
  String get aboutApp => 'Sobre';

  @override
  String get aboutAppSubtitle => 'Versão do aplicativo, políticas e mais';

  @override
  String get systemLanguage => 'Idioma do Sistema';

  @override
  String currentLanguage(String languageName, Object language) {
    return 'Atual: $language';
  }

  @override
  String get selectLanguage => 'Selecionar Idioma';

  @override
  String defaultAppName(int number) {
    return 'App $number';
  }

  @override
  String get cannotAddDirectory =>
      'Falha na autorização do diretório, tente novamente';

  @override
  String get directoryRemoved => 'Autorização do diretório removida';

  @override
  String get cancelAuthorization => 'Cancelar Autorização';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return 'Você realmente deseja cancelar a autorização de acesso para $directoryName de $appName? Após o cancelamento, os arquivos neste diretório não serão mais visíveis.';
  }

  @override
  String get noKeepIt => 'Não, mantê-lo';

  @override
  String get yesRemoveFolder => 'Sim, eu não preciso mais desta pasta';

  @override
  String filesCount(int count) {
    return '$count arquivos';
  }

  @override
  String directorySize(String size) {
    return 'Tamanho: $size';
  }

  @override
  String get editName => 'Editar Nome';

  @override
  String get removeDirectory => 'Remover';

  @override
  String get noAuthorizedDirectories => 'Nenhum diretório autorizado';

  @override
  String get noAuthorizedDirectoriesSubtitle =>
      'Adicione diretórios para acessar arquivos desses locais';

  @override
  String get addDirectory => 'Adicionar Diretório';

  @override
  String get authorizedDirectories => 'Gerenciar Diretórios Autorizados';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appName pode acessar os seguintes diretórios:';
  }

  @override
  String get nameForNewFolder =>
      'Para facilitar a identificação posterior, dê um nome à pasta do aplicativo que você acabou de selecionar';

  @override
  String get enterCustomName => 'Digite o nome personalizado';

  @override
  String get customName => 'Nome Personalizado';

  @override
  String get version => 'Versão';

  @override
  String get appVersion => '1.0.0';

  @override
  String get appDescription =>
      'Processamento inteligente de documentos. Recorte e correção automáticos com IA para resultados profissionais.';

  @override
  String get privacyPolicy => 'Política de Privacidade';

  @override
  String get termsOfService => 'Termos de Serviço';

  @override
  String get copyright => '© 2025';

  @override
  String get noFavoriteFiles => 'Você ainda não favoritou nenhum arquivo';

  @override
  String get noFavoriteFilesHint =>
      'Você pode favoritar arquivos tocando no ícone de estrela ☆ ao lado dos arquivos na página de documentos';

  @override
  String get fileNotFound => 'Arquivo não encontrado';

  @override
  String get noRecentFiles =>
      'Arquivos PDF/Word/Excel/PPT que você abriu neste app aparecerão aqui';

  @override
  String noFilesFoundForType(int count, String fileType) {
    return 'Encontrados $count arquivos $fileType';
  }

  @override
  String get selectFolder => 'Autorizar Pasta';

  @override
  String get selectFolderSubtitle =>
      'Autorizar este aplicativo a encontrar diretórios de arquivos PDF/Word/Excel/PPT';

  @override
  String get photoToPdfSubtitle =>
      'Digitalize slides de apresentação ou notas em um único PDF';

  @override
  String get mergeImagesToPdf => 'Mesclar Imagens em PDF';

  @override
  String get mergeImagesToPdfSubtitle => 'Mesclar fotos do álbum em PDF';

  @override
  String get fabTutorial =>
      'Clique aqui para digitalizar documentos com sua câmera ou mesclar imagens de sua galeria em um PDF.';

  @override
  String get iGotIt => 'Entendi';

  @override
  String get signatures => 'Assinaturas';

  @override
  String get manageSignatures => 'Gerenciar suas assinaturas manuscritas';

  @override
  String get manageSignaturesHint =>
      'Gerencie suas assinaturas manuscritas aqui. Toque no botão + para adicionar sua assinatura. Em seguida, você pode usar essas assinaturas ao editar PDFs.';

  @override
  String get addSignature => 'Adicionar assinatura';

  @override
  String get deleteSignature => 'Excluir assinatura';

  @override
  String get confirmDeleteSignature =>
      'Tem certeza de que deseja excluir esta assinatura?';

  @override
  String get createSignature => 'Criar assinatura';

  @override
  String get saveSignature => 'Salvar assinatura';

  @override
  String get strokeWidth => 'Espessura do traço';

  @override
  String get color => 'Cor';

  @override
  String get eraser => 'Borracha';

  @override
  String get thin => 'Fino';

  @override
  String get medium => 'Médio';

  @override
  String get thick => 'Grosso';

  @override
  String get signatureCreated => 'Assinatura criada com sucesso';

  @override
  String get editSignature => 'Edit Signature';

  @override
  String get signatureUpdated => 'Signature updated successfully';

  @override
  String get signatureDeleted => 'Assinatura excluída';

  @override
  String get noSignatures => 'Sem assinaturas';

  @override
  String get newSignature => 'Nova assinatura';

  @override
  String get signatureUsageHint =>
      'Você pode usar essas assinaturas ao editar PDFs';

  @override
  String get scanningFiles =>
      'Procurando arquivos PDF/Word/Excel/PPT em seu dispositivo';

  @override
  String pageRotated(int page, int degrees) {
    return 'Página $page rotacionada $degrees°';
  }

  @override
  String get rotationFailed => 'Falha na rotação';

  @override
  String get closeSearch => 'Fechar busca';

  @override
  String get previous => 'Anterior';

  @override
  String get next => 'Próximo';

  @override
  String get cancelSignatureEdit => 'Cancelar edição de assinatura';

  @override
  String get back => 'Voltar';

  @override
  String get editMode => 'Modo de edição';

  @override
  String get highlightText => 'Destacar texto';

  @override
  String get draw => 'Desenhar';

  @override
  String get signature => 'Assinatura';

  @override
  String get highlightColor => 'Cor de destaque';

  @override
  String get brushColor => 'Cor do pincel';

  @override
  String get thickness => 'Espessura';

  @override
  String get confirmPlacement => 'Confirmar colocação';

  @override
  String get dragToAdjustHint => 'Arraste para ajustar';

  @override
  String get selectSignature => 'Selecionar assinatura';

  @override
  String get noSignaturesPleaseCreate => 'Sem assinaturas, por favor crie uma';

  @override
  String pageIndicator(int current, int total) {
    return 'Página $current/$total';
  }

  @override
  String get androidDirectoryManagementSubtitle =>
      'Gerenciar e adicionar pastas acessíveis';

  @override
  String get loadingDocument => 'Carregando documento...';

  @override
  String get cannotOpenFile => 'Não é possível abrir o arquivo';

  @override
  String get galleryAccessError =>
      'Não é possível acessar as imagens selecionadas';

  @override
  String get permissionDenied => 'Permissão negada para acessar fotos';

  @override
  String get invalidImageSelected => 'Imagem inválida selecionada';

  @override
  String get gallerySelectionFailed => 'Falha ao selecionar imagens da galeria';

  @override
  String get unexpectedError => 'Ocorreu um erro inesperado';

  @override
  String get importFailed => 'Importação falhou';

  @override
  String get importResults => 'Resultados da Importação';

  @override
  String allImportsFailed(int count) {
    return 'Falhou a importação de todas as $count imagens selecionadas';
  }

  @override
  String partialImportSuccess(int successCount, int failCount) {
    return '$successCount imagens importadas com sucesso, $failCount falharam';
  }

  @override
  String successfullyImported(int count) {
    return '✅ Importação bem-sucedida: $count imagens';
  }

  @override
  String failedToImport(int count) {
    return '❌ Falha na importação: $count imagens';
  }

  @override
  String get tryAgainOrUseCamera => 'Por favor tente novamente ou use a câmera';

  @override
  String get checkPermissionsInSettings =>
      'Por favor verifique as permissões do aplicativo nas Configurações';

  @override
  String get trySelectingDifferentImages =>
      'Por favor tente selecionar imagens diferentes';

  @override
  String get selectedImagesCorruptedOrInaccessible =>
      'As imagens selecionadas podem estar corrompidas ou inacessíveis. Por favor tente selecionar imagens diferentes.';

  @override
  String get failedFiles => 'Arquivos que falharam:';

  @override
  String andXMore(int count) {
    return '... e mais $count';
  }

  @override
  String get tryAgain => 'Tentar Novamente';

  @override
  String get details => 'Detalhes';

  @override
  String get ok => 'OK';

  @override
  String get galleryLoadFailed => 'Falha ao carregar galeria';

  @override
  String get fileSelectionFailed => 'Falha na seleção de arquivo';

  @override
  String get storagePermissionDenied => 'Permissão de armazenamento negada';

  @override
  String get unsupportedFileType => 'Tipo de arquivo não suportado selecionado';

  @override
  String get playSlideshow => 'Play Slideshow';

  @override
  String get slideshowMode => 'Slideshow Mode';

  @override
  String get exitSlideshow => 'Exit Slideshow';

  @override
  String get searchTitle => 'Pesquisar';

  @override
  String get searchHint => 'Digite o nome do arquivo';

  @override
  String get noResults => 'Nenhum resultado';

  @override
  String iosPermissionMessage(String deviceType) {
    return 'Devido às restrições de permissão do iPhone, você precisa primeiro autorizar o acesso às pastas que contêm seus arquivos PDF, Word, Excel e PPT.';
  }

  @override
  String get waitForDocLoad =>
      'Aguarde o documento carregar completamente antes de preparar o PDF';

  @override
  String get generatingPdf => 'Gerando PDF...';

  @override
  String get pdfGenerationFailed =>
      'A preparação do PDF falhou, tente novamente';

  @override
  String pdfConversionFailed(String error) {
    return 'A conversão para PDF falhou: $error';
  }

  @override
  String get conversionSuccess => 'Conversão bem-sucedida';

  @override
  String pdfSavedMessage(String fileName) {
    return 'Salvo como arquivo PDF $fileName, abrir para ver agora?';
  }

  @override
  String get stayOnPage => 'Permanecer na página';

  @override
  String get openPdf => 'Abrir PDF';

  @override
  String get generatePdfVersion => 'Gerar versão em PDF';

  @override
  String get generatePdfToEdit =>
      'É necessário gerar uma versão em PDF para editar ou girar. Continuar?';

  @override
  String get continueAction => 'Continuar';

  @override
  String get openingCachedPdf => 'Abrindo PDF em cache...';

  @override
  String get saveAsPdf => 'Salvar como PDF';

  @override
  String get caseSensitive => 'Diferenciar maiúsculas de minúsculas';

  @override
  String notFound(String query) {
    return '\"$query\" não encontrado';
  }

  @override
  String get previousMatch => 'Anterior';

  @override
  String get nextMatch => 'Próximo';

  @override
  String get webViewOnlyIos =>
      'A visualização de documentos WebView é suportada apenas no iOS';

  @override
  String webViewFailed(String error) {
    return 'Falha ao carregar o WebView: $error';
  }

  @override
  String get officeTimeout =>
      'O tempo de carregamento do documento do Office esgotou. Isso pode ser devido a um arquivo grande ou formato não suportado.';

  @override
  String get addSlideshowButton => 'Adicionar botão de apresentação de slides';

  @override
  String get threshold400px => 'Limite: 400px';

  @override
  String get confirmClear => 'Confirmar Limpar';

  @override
  String get confirmClearSignature =>
      'Isso limpará toda a sua imagem de assinatura. Tem certeza de que deseja limpá-la?';

  @override
  String get clear => 'Limpar';

  @override
  String iosNetworkPermissionGuide(String appName) {
    return 'Encontre \"$appName\" nas configurações do seu iPhone e verifique se o acesso à rede é permitido.\nCaminho: Ajustes > Privacidade e Segurança > Rede Local > $appName';
  }

  @override
  String get checkNetworkConnection => 'Verifique sua conexão de rede';

  @override
  String get networkPermission => 'Permissão de Rede';

  @override
  String get networkPermissionRequest =>
      'Este aplicativo precisa de acesso à rede para processar e aprimorar imagens em documentos PDF.';

  @override
  String get featuresInclude => 'Os recursos incluem:';

  @override
  String get imageClarityEnhancement => 'Melhora da nitidez da imagem';

  @override
  String get documentEdgeDetection => 'Detecção de borda de documento';

  @override
  String get imageOptimization => 'Otimização de imagem';

  @override
  String get requestPermission => 'Solicitar permissão';

  @override
  String get networkConnectionProblem => 'Problema de conexão de rede';

  @override
  String get networkStatusDescription =>
      'A conexão de rede não está disponível, verifique suas configurações de rede';

  @override
  String get troubleshootingSuggestions =>
      'Sugestões para solução de problemas:';

  @override
  String get networkTroubleshootingTips =>
      'Verifique se o Wi-Fi ou os dados móveis estão ativados\nConfirme se o dispositivo está conectado a uma rede utilizável\nVerifique se o modo avião está ativado\nTente acessar outros aplicativos ou sites para confirmar o status da rede\nReinicie a conexão de rede ou o dispositivo';

  @override
  String get retry => 'Tentar novamente';

  @override
  String get unfavorite => 'Unfavorite';

  @override
  String get open => 'Open';

  @override
  String get rename => 'Rename';

  @override
  String get cannotAccessFile =>
      'Cannot access this file. Please reauthorize in \"Authorized Directories\" or reselect the file.';

  @override
  String get installOfficeAppTitle => 'App required to open Office files';

  @override
  String get installOfficeAppContent =>
      'An app is required to open this file. Install from the app store?';

  @override
  String get install => 'Install';

  @override
  String get renameFile => 'Rename';

  @override
  String get renamed => 'Renamed';

  @override
  String renameFailed(String error) {
    return 'Rename failed: $error';
  }

  @override
  String confirmDeleteFile(String fileName) {
    return 'Confirm delete file:\n$fileName?';
  }

  @override
  String get deleted => 'Deleted';

  @override
  String deleteFailed(String error) {
    return 'Delete failed: $error';
  }

  @override
  String get networkConnected => 'Rede conectada';

  @override
  String get networkDisconnected => 'Rede desconectada';

  @override
  String get networkAccessDenied => 'Acesso à rede negado';

  @override
  String get networkOperationFailed => 'Operação de rede falhou';

  @override
  String get connected => 'Connected';

  @override
  String get noNetwork => 'No network';

  @override
  String get thumbnailsOnlyIos => 'Miniaturas disponíveis apenas no iOS';

  @override
  String get waitDocumentComplete =>
      'Aguardando conclusão do carregamento do documento...';

  @override
  String get thumbnailDescription =>
      'Miniatura do documento completo\nConveniente para navegação rápida';

  @override
  String get generatingThumbnail =>
      'Gerando miniatura do documento completo...';

  @override
  String get analyzingDocument =>
      'Analisando estrutura e dimensões do documento';

  @override
  String get thumbnailUnavailable => 'Miniatura temporariamente indisponível';

  @override
  String get clickRefreshRetry =>
      'Clique no botão atualizar no canto superior direito para tentar novamente';
}
