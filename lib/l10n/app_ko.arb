{"@@locale": "ko", "appName": "모든 PDF 편집기", "documents": "문서", "recent": "최근", "favorite": "즐겨찾기", "settings": "설정", "addFiles": "파일 추가", "addFilesSubtitle": "장치에서 더 많은 파일을 선택하여 관리하세요", "followDocumentTheme": "문서 유형 색상 구성표 따르기", "followDocumentThemeSubtitle": "PDF/Word/Excel/PPT 스타일", "languageSettings": "UI 언어", "aboutApp": "정보", "version": "버전", "appVersion": "1.0.0", "copyright": "© 2025", "filesAdded": "{count}개의 파일이 추가되었습니다", "cannotSelectFiles": "파일을 선택할 수 없습니다. 권한을 확인하세요", "selectLanguage": "언어 선택", "systemLanguage": "시스템 언어", "english": "English", "spanish": "Español", "portuguese": "Português", "french": "Français", "german": "De<PERSON>ch", "chineseSimplified": "简体中文", "chineseTraditional": "繁體中文", "japanese": "日本語", "korean": "한국어", "currentLanguage": "현재: {language}", "selectAuthorizedDirectory": "승인된 디렉토리 관리", "selectAuthorizedDirectorySubtitle": "iPhone/iPad 파일 디렉토리 접근 승인", "shareApp": "공유", "shareAppSubtitle": "PDF 매직툴을 추천드립니다:", "rateApp": "평가", "rateAppSubtitle": "App Store에서 평가해주세요", "aboutAppSubtitle": "앱 버전, 정책 등", "privacyPolicy": "개인정보 처리방침", "termsOfService": "서비스 약관", "appDescription": "스마트 문서 처리. AI 자동 자르기와 보정으로 전문성 구현.", "authorizedDirectories": "승인된 디렉토리 관리", "addDirectory": "디렉토리 추가", "removeDirectory": "제거", "noAuthorizedDirectories": "승인된 디렉토리가 없습니다", "noAuthorizedDirectoriesSubtitle": "해당 위치의 파일에 접근하려면 디렉토리를 추가하세요", "directoryRemoved": "디렉토리 인증이 제거되었습니다", "cannotAddDirectory": "디렉토리 인증에 실패했습니다. 다시 시도해주세요", "appCanAccessDirectories": "{appName}은(는) 다음 디렉토리에 접근할 수 있습니다:", "filesCount": "{count}개 파일", "directorySize": "크기: {size}", "cancelAuthorization": "승인 취소", "confirmCancelAuthorization": "{appName}의 {directoryName}에 대한 접근 승인을 정말로 취소하시겠습니까? 취소 후에는 이 디렉토리의 파일이 더 이상 표시되지 않습니다.", "noKeepIt": "아니요, 유지합니다", "yesRemoveFolder": "예, 이 폴더는 더 이상 필요하지 않습니다", "editName": "이름 편집", "enterCustomName": "사용자 정의 이름 입력", "customName": "사용자 정의 이름", "save": "저장", "cancel": "취소", "nameForNewFolder": "나중에 쉽게 식별할 수 있도록 방금 선택한 앱 폴더에 이름을 지정해주세요", "defaultAppName": "앱 {number}", "selectFiles": "파일 선택", "authorizeFolder": "폴더 승인", "selectingDirectory": "디렉토리 스캔 중...", "directorySelectionOnlyMobile": "디렉토리 선택은 iOS/Android에서만 가능합니다", "cannotSelectDirectory": "디렉토리를 선택할 수 없습니다. 권한을 확인하세요", "loadedFiles": "{count}개의 파일을 스캔했습니다", "cannotLoadDirectoryFiles": "디렉토리 파일을 로드할 수 없습니다", "iosPermissionMessage": "iPhone 권한 제한으로 인해 PDF, Word, Excel 및 PPT 파일이 포함된 폴더에 대한 접근을 먼저 승인해야 합니다.", "noFilesFoundForType": "{fileType} 파일 {count}개를 찾았습니다", "@noFilesFoundForType": {"placeholders": {"count": {"type": "int"}, "fileType": {"type": "String"}}}, "importFilesFromPhone": "휴대폰에서 파일 가져오기", "selectFolder": "폴더 승인", "selectFolderSubtitle": "이 앱이 PDF/Word/Excel/PPT 파일 디렉토리를 찾을 수 있도록 승인", "photoToPdf": "사진을 PDF로", "imageToPdf": "이미지를 PDF로", "photoToPdfSubtitle": "프레젠테이션 슬라이드 또는 노트를 하나의 PDF로 스캔", "mergeImagesToPdf": "이미지를 PDF로 병합", "mergeImagesToPdfSubtitle": "앨범 사진을 PDF로 병합", "convert": "변환", "pdfSavedSuccessfully": "PDF가 성공적으로 저장되었습니다", "pdfSaveFailed": "PDF 저장 실패", "undo": "실행 취소", "deleteImage": "이미지 삭제", "confirmDeleteImage": "이 이미지를 삭제하시겠습니까?", "delete": "삭제", "reorderImage": "이미지 순서 변경", "takePicture": "사진 찍기", "addImagesFromGallery": "갤러리에서 {count}개의 이미지 추가", "cropImage": "이미지 자르기", "confirmUndo": "실행 취소: {action}. 마지막 작업을 되돌립니다. 계속하시겠습니까?", "cropPlaceholderMessage": "자르기 기능은 여기에 구현될 예정입니다", "cropFeatureComingSoon": "고급 자르기 기능이 곧 제공될 예정입니다!", "addMoreImages": "더 많은 이미지 추가", "takePhoto": "사진 찍기", "importFromAlbum": "앨범에서 가져오기", "sort": "정렬", "nameSort": "이름", "lastModified": "최종 수정일", "sizeSort": "크기", "descending": "내림차순", "apply": "적용", "noRecentFiles": "이 앱에서 열었던 PDF/Word/Excel/PPT 파일이 여기에 표시됩니다", "noFavoriteFiles": "아직 즐겨찾기한 파일이 없습니다", "noFavoriteFilesHint": "문서 페이지의 파일 옆에 있는 ☆ 별 아이콘을 탭하여 파일을 즐겨찾기에 추가할 수 있습니다", "fileNotFound": "파일을 찾을 수 없음", "weLikeYouToo": "애정 주셔서 감사합니다!", "thankYouForFeedback": "만점 평가가 개선의 원동력입니다!", "theBestWeCanGet": "멋져요, 만점 평가!", "maybeLater": "나중에", "rateNow": "지금 스토어에서 평가하기", "saveAs": "다른 이름으로 저장", "fileName": "파일 이름", "discardChangesTitle": "변경사항을 취소하시겠습니까?", "discardChangesContent": "저장되지 않은 변경사항이 있습니다. 지금 종료하면 변경사항이 손실됩니다.", "discard": "취소", "keepEditing": "계속 편집", "splashTitle": "사진PDF변환 매직툴", "splashSubtitle": "회의 사진 자동 정리, 원터치로 자르기와 PDF 병합", "splashWorkflow": "앱 시작 중, 잠시만 기다려주세요...", "splashOriginalPhoto": "원본 사진", "splashGeneratedPdf": "자동 생성 PDF", "fabTutorial": "여기를 클릭하여 카메라로 문서를 스캔하거나 갤러리의 이미지를 PDF로 병합하십시오.", "iGotIt": "알았어요", "scanPrefix": "스캔", "signatures": "서명", "manageSignatures": "손글씨 서명 관리", "manageSignaturesHint": "여기서 손글씨 서명을 관리합니다. +버튼을 눌러 서명을 추가하세요. 그러면 PDF 편집 시 이 서명들을 사용할 수 있습니다.", "addSignature": "서명 추가", "deleteSignature": "서명 삭제", "confirmDeleteSignature": "이 서명을 삭제하시겠습니까?", "createSignature": "서명 생성", "saveSignature": "서명 저장", "strokeWidth": "획 두께", "color": "색상", "eraser": "지우개", "thin": "얇게", "medium": "중간", "thick": "두껍게", "signatureCreated": "서명이 생성되었습니다", "signatureDeleted": "서명이 삭제되었습니다", "noSignatures": "서명이 없습니다", "newSignature": "새 서명", "signatureUsageHint": "PDF 편집 시 이 서명들을 사용할 수 있습니다", "scanningFiles": "기기의 PDF/Word/Excel/PPT 파일을 스캔 중", "pageRotated": "페이지 {page}를 {degrees}° 회전했습니다", "@pageRotated": {"placeholders": {"page": {"type": "int"}, "degrees": {"type": "int"}}}, "rotationFailed": "회전 실패", "closeSearch": "검색 닫기", "previous": "이전", "next": "다음", "cancelSignatureEdit": "서명 편집 취소", "back": "뒤로", "editMode": "편집 모드", "highlightText": "텍스트 강조", "draw": "그리기", "signature": "서명", "highlightColor": "강조 색상", "brushColor": "브러시 색상", "thickness": "두께", "confirmPlacement": "배치 확인", "dragToAdjustHint": "드래그하여 조정", "selectSignature": "서명 선택", "noSignaturesPleaseCreate": "서명이 없습니다, 하나 만들어 주세요", "pageIndicator": "페이지 {current}/{total}", "@pageIndicator": {"placeholders": {"current": {"type": "int"}, "total": {"type": "int"}}}, "androidDirectoryManagementSubtitle": "액세스 가능한 폴더 관리 및 추가", "loadingDocument": "문서 로딩 중...", "cannotOpenFile": "파일을 열 수 없습니다", "galleryAccessError": "선택한 이미지에 액세스할 수 없습니다", "permissionDenied": "사진 액세스 권한이 거부되었습니다", "invalidImageSelected": "유효하지 않은 이미지가 선택되었습니다", "gallerySelectionFailed": "갤러리에서 이미지 선택에 실패했습니다", "unexpectedError": "예상치 못한 오류가 발생했습니다", "importFailed": "가져오기 실패", "importResults": "가져오기 결과", "allImportsFailed": "선택한 {count}개 이미지 가져오기가 모두 실패했습니다", "@allImportsFailed": {"placeholders": {"count": {"type": "int"}}}, "partialImportSuccess": "{successCount}개 이미지 가져오기 성공, {failCount}개 실패", "@partialImportSuccess": {"placeholders": {"successCount": {"type": "int"}, "failCount": {"type": "int"}}}, "successfullyImported": "✅ 가져오기 성공: {count}개 이미지", "@successfullyImported": {"placeholders": {"count": {"type": "int"}}}, "failedToImport": "❌ 가져오기 실패: {count}개 이미지", "@failedToImport": {"placeholders": {"count": {"type": "int"}}}, "tryAgainOrUseCamera": "다시 시도하거나 카메라를 사용해보세요", "checkPermissionsInSettings": "설정에서 앱 권한을 확인해주세요", "trySelectingDifferentImages": "다른 이미지를 선택해보세요", "selectedImagesCorruptedOrInaccessible": "선택한 이미지가 손상되었거나 접근할 수 없습니다. 다른 이미지를 선택해보세요.", "failedFiles": "실패한 파일:", "andXMore": "... 및 {count}개 더", "@andXMore": {"placeholders": {"count": {"type": "int"}}}, "tryAgain": "다시 시도", "details": "세부사항", "ok": "확인", "galleryLoadFailed": "갤러리 로딩 실패", "fileSelectionFailed": "파일 선택에 실패했습니다", "storagePermissionDenied": "저장소 권한이 거부되었습니다", "unsupportedFileType": "지원되지 않는 파일 형식이 선택되었습니다", "searchTitle": "검색", "searchHint": "파일 이름 입력", "noResults": "결과 없음", "waitForDocLoad": "PDF를 준비하기 전에 문서가 완전히 로드될 때까지 기다리십시오", "generatingPdf": "PDF 생성 중...", "pdfGenerationFailed": "PDF 준비에 실패했습니다. 다시 시도하십시오.", "pdfConversionFailed": "PDF 변환 실패: {error}", "@pdfConversionFailed": {"placeholders": {"error": {"type": "String"}}}, "conversionSuccess": "변환 성공", "pdfSavedMessage": "PDF 파일 {fileName}(으)로 저장되었습니다. 지금 열어 보시겠습니까?", "@pdfSavedMessage": {"placeholders": {"fileName": {"type": "String"}}}, "stayOnPage": "페이지에 머물기", "openPdf": "PDF 열기", "generatePdfVersion": "PDF 버전 생성", "generatePdfToEdit": "편집하거나 회전하려면 PDF 버전을 생성해야 합니다. 계속하시겠습니까?", "continueAction": "계속", "openingCachedPdf": "캐시된 PDF 여는 중...", "saveAsPdf": "PDF로 저장", "caseSensitive": "대소문자 구분", "notFound": "\"{query}\"을(를) 찾을 수 없습니다.", "@notFound": {"placeholders": {"query": {"type": "String"}}}, "previousMatch": "이전", "nextMatch": "다음", "webViewOnlyIos": "WebView 문서 보기는 iOS에서만 지원됩니다.", "webViewFailed": "WebView를 로드하지 못했습니다: {error}", "@webViewFailed": {"placeholders": {"error": {"type": "String"}}}, "officeTimeout": "Office 문서 로드 시간이 초과되었습니다. 파일이 너무 크거나 지원되지 않는 형식일 수 있습니다.", "addSlideshowButton": "슬라이드쇼 버튼 추가", "threshold400px": "임계값: 400px", "confirmClear": "지우기 확인", "confirmClearSignature": "이렇게 하면 전체 서명 이미지가 지워집니다. 지우시겠습니까?", "clear": "지우기", "iosNetworkPermissionGuide": "iPhone 설정에서 \"{appName}\"을(를) 찾아 네트워크 액세스가 허용되었는지 확인하십시오.\n경로: 설정 > 개인 정보 보호 및 보안 > 로컬 네트워크 > {appName}", "@iosNetworkPermissionGuide": {"placeholders": {"appName": {"type": "String"}}}, "checkNetworkConnection": "네트워크 연결을 확인하십시오", "networkPermission": "네트워크 권한", "networkPermissionRequest": "이 앱은 PDF 문서의 이미지를 처리하고 향상시키기 위해 네트워크에 액세스해야 합니다.", "featuresInclude": "기능은 다음과 같습니다.", "imageClarityEnhancement": "이미지 선명도 향상", "documentEdgeDetection": "문서 가장자리 감지", "imageOptimization": "이미지 최적화", "requestPermission": "권한 요청", "networkConnectionProblem": "네트워크 연결 문제", "networkStatusDescription": "네트워크 연결을 사용할 수 없습니다. 네트워크 설정을 확인하십시오.", "troubleshootingSuggestions": "문제 해결 제안:", "networkTroubleshootingTips": "Wi-Fi 또는 모바일 데이터가 활성화되어 있는지 확인하십시오.\n장치가 사용 가능한 네트워크에 연결되어 있는지 확인하십시오.\n비행기 모드가 켜져 있는지 확인하십시오.\n다른 앱이나 웹사이트에 액세스하여 네트워크 상태를 확인하십시오.\n네트워크 연결 또는 장치를 다시 시작하십시오.", "retry": "재시도", "networkConnected": "네트워크가 연결되었습니다", "networkDisconnected": "네트워크가 끊어졌습니다", "networkAccessDenied": "네트워크 액세스가 거부되었습니다", "networkOperationFailed": "네트워크 작업에 실패했습니다", "thumbnailsOnlyIos": "썸네일은 iOS에서만 사용할 수 있습니다", "waitDocumentComplete": "문서 로드 완료를 기다리는 중...", "thumbnailDescription": "전체 문서의 썸네일\n빠른 탐색에 편리합니다", "generatingThumbnail": "전체 문서 썸네일 생성 중...", "analyzingDocument": "문서 구조와 크기 분석 중", "thumbnailUnavailable": "썸네일을 일시적으로 표시할 수 없습니다", "clickRefreshRetry": "우상단의 새로고침 버튼을 클릭하여 다시 시도하세요"}