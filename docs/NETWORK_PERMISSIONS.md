# iOS 18 网络权限配置指南

## 问题背景

在iOS 18上，苹果加强了对应用网络访问的控制。应用首次尝试网络连接时，系统会提示用户是否允许该应用访问网络。如果用户拒绝，应用将无法进行任何网络操作。

## 解决方案

本项目已经实现了完整的iOS 18网络权限处理方案，包括：

### 1. Info.plist配置

在`ios/Runner/Info.plist`中添加了以下配置：

```xml
<key>NSNetworkUsageDescription</key>
<string>该应用需要访问网络来上传和处理PDF文档中的图片，提供文档增强和优化功能</string>
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
    <key>NSAllowsLocalNetworking</key>
    <true/>
</dict>
```

### 2. 权限管理服务

- **NetworkPermissionService**: 检查和申请网络权限
- **NetworkStatusService**: 监控网络连接状态
- **NetworkPermissionDialog**: 用户友好的权限申请界面

### 3. 使用示例

#### 在需要网络的功能中检查权限

```dart
import 'package:your_app/src/utils/network_helper.dart';

// 在图片处理等需要网络的操作前
Future<void> processImages() async {
  final result = await NetworkHelper.performNetworkOperation(
    context,
    () async {
      // 你的网络操作代码
      return await imageProcessingService.processImages(images);
    },
  );
  
  if (result != null) {
    // 处理成功结果
  }
}
```

#### 显示网络状态指示器

```dart
// 在AppBar或状态栏显示网络连接状态
AppBar(
  title: Text('标题'),
  actions: [
    NetworkHelper.buildNetworkIndicator(),
  ],
)
```

#### 手动检查权限

```dart
// 检查并请求网络权限
final hasPermission = await NetworkPermissionHelper.checkAndRequestPermission(context);

if (hasPermission) {
  // 执行网络操作
} else {
  // 处理权限被拒绝的情况
}
```

### 4. 自动初始化

应用启动时会自动初始化网络服务：

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化网络服务
  await NetworkPermissionService.instance.initialize();
  NetworkStatusService.instance.startMonitoring();
  
  // ... 其他初始化代码
}
```

### 5. 用户指引

当网络权限被拒绝时，应用会显示指导用户如何在设置中启用网络权限：

设置路径：**设置 > 隐私与安全性 > 本地网络 > All PDF Editor**

## 测试建议

1. 在iOS 18设备上全新安装应用
2. 首次使用需要网络的功能时观察权限提示
3. 测试拒绝权限后的用户体验
4. 验证网络状态变化时的应用响应

## 注意事项

- 网络权限只在iOS 18+上需要特殊处理
- Android设备默认具有网络权限
- 权限被拒绝后需要用户手动在系统设置中启用
- 应用应该提供良好的降级体验，即使在无网络权限时也能使用本地功能

## 相关文件

- `lib/src/services/network_permission_service.dart` - 网络权限管理
- `lib/src/services/network_status_service.dart` - 网络状态监控  
- `lib/src/widgets/network_permission_dialog.dart` - 权限对话框
- `lib/src/utils/network_helper.dart` - 便捷工具类
- `ios/Runner/Info.plist` - iOS权限配置