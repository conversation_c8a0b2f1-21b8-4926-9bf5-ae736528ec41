# QA 技术参考文档

本文档为QA团队提供项目技术层面的参考信息，旨在帮助理解功能实现、定位问题和进行深度测试。

## 1. 项目概述

本项目是一款功能强大的文档处理中心App，使用 Flutter 开发，支持跨平台（iOS/Android）。核心功能包括PDF的阅读、编辑、创建、搜索和管理。

## 2. 技术栈

- **核心框架**: Flutter
- **状态管理**: `provider`
- **语言**: Dart

## 3. 核心依赖库说明

了解这些核心库有助于定位特定功能的问题。例如，如果PDF渲染出错，很可能与 `pdfx` 或 `syncfusion_flutter_pdfviewer` 有关。

| 功能分类         | 库名称                          | 主要用途                                       |
| ---------------- | ------------------------------- | ---------------------------------------------- |
| **PDF 处理**     | `pdf`                           | 在客户端创建PDF文档。                          |
|                  | `printing`                      | 提供打印PDF的功能。                            |
|                  | `pdfx`                          | 用于渲染和显示PDF文档的主要视图器。            |
|                  | `syncfusion_flutter_pdfviewer`  | 另一个PDF视图器，可能用于需要精确文本搜索高亮等高级功能的场景。 |
|                  | `read_pdf_text`                 | 从PDF文件中提取文本内容，用于搜索等功能。      |
| **文件与权限**   | `permission_handler`            | 请求和管理设备权限（如存储、相机）。           |
|                  | `path_provider`                 | 获取设备上的标准目录路径（如文档、临时目录）。 |
|                  | `media_store_plus`              | (Android) 发现和访问媒体存储中的文档。         |
|                  | `open_filex`                    | 调用外部应用打开文件。                         |
|                  | `file_picker`                   | 从设备存储中选择文件。                         |
|                  | `share_plus`                    | 分享文件到其他应用。                           |
| **平台与硬件**   | `image_picker`                  | 从相册或相机获取图片（用于添加到PDF）。        |
|                  | `device_info_plus`              | 获取设备信息。                                 |
|                  | `url_launcher`                  | 在浏览器中打开URL。                            |
| **数据持久化**   | `shared_preferences`            | 轻量级本地存储，用于保存收藏、最近使用等信息。 |
| **网络通信**     | `http`                          | 用于与后端服务进行API交互。                    |

## 4. 项目代码结构

项目的核心业务逻辑位于 `lib/src/` 目录下：

- `screens/`: 存放所有UI页面（视图）。每个 `.dart` 文件代表一个用户界面。
- `widgets/`: 存放通用的、可复用的UI组件。
- `services/`: 存放业务逻辑服务，如文件处理、API请求等。
- `models/`: 存放数据模型类。
- `state/`: 存放与 `provider` 相关的状态管理类。
- `navigation/`: 管理应用的路由和页面导航。
- `utils/`: 存放工具类和辅助函数。

## 5. 关键功能与代码位置

下表将主要功能与实现它的代码文件关联起来，方便快速定位问题。

| 功能模块           | 主要实现文件 (`lib/src/screens/`)      |
| ------------------ | -------------------------------------- |
| 启动页             | `splash_screen.dart`                   |
| 文档主页（列表）   | `documents_screen.dart`                |
| PDF 阅读器         | `pdf_viewer_screen.dart`               |
| PDF 编辑器         | `editor_screen.dart`                   |
| 页面裁剪           | `crop_screen.dart`                     |
| 签名功能           | `signature_creation_screen.dart`       |
|                    | `signature_management_screen.dart`     |
| 搜索               | `search_screen.dart`                   |
| 设置               | `settings_screen.dart`                 |
| 语言选择           | `language_selection_screen.dart`       |
| 授权目录管理       | `authorized_directories_screen.dart`   |

## 6. 构建与测试

1.  **安装依赖**:
    ```bash
    flutter pub get
    ```
2.  **运行应用** (在连接的设备或模拟器上):
    ```bash
    flutter run
    ```
