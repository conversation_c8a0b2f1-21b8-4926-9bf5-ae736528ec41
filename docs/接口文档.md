### **All PDF Editor - 客户端/服务端接口文档**

本文档旨在说明 "All PDF Editor" 移动客户端 (Client) 与图像处理服务器 (Server) 之间的网络通信协议、认证流程和接口规范，以便QA团队进行接口相关的测试。

#### **1. 核心交互流程图 (时序图)**

下图描述了从App启动到完成一次图像处理的完整流程：

```mermaid
sequenceDiagram
    participant Client as 移动客户端
    participant Server as 后端服务器

    rect rgb(240, 240, 240)
        note over Client, Server: 阶段一: 会话建立 (Authentication)
        Client->>Server: POST /api/native/session <br> (携带客户端密钥)
        Server-->>Client: 返回加密的会话数据 (Encrypted Session Data)
        Client->>Client: 解密数据，获取会话ID(Session ID)和票据(Ticket)
    end

    rect rgb(230, 255, 230)
        note over Client, Server: 阶段二: 图像上传与处理 (Image Processing)
        Client->>Client: 用户选择图片
        Client->>Client: 本地预检查 (检查图片分辨率)
        
        alt 图片分辨率过低或超范围
            Client->>Client: 直接标记为不适用，不请求服务器
        else 图片分辨率有效
            Client->>Server: POST /api/images/upload (Multipart) <br> - Header携带认证信息 (HMAC签名) <br> - Body包含图片文件
            Server-->>Server: 处理图片 (裁剪、增强等)
            Server-->>Client: 返回处理结果 (JSON格式，含Base64图片)
        end
    end

    rect rgb(255, 255, 220)
        note over Client, Server: 其他接口
        Client->>Server: GET /api/health <br> (检查服务健康状态)
        Server-->>Client: 200 OK
    end

```

#### **2. 认证机制 (Authentication)**

所有需要授权的接口都采用基于 `HMAC-SHA256` 的签名机制进行保护。

1.  **获取票据 (Ticket)**：
    *   客户端通过调用 `POST /api/native/session` 接口，使用一个固定的 `clientSecret` (客户端密钥) 换取一个临时的 `ticket`。
    *   这个 `ticket` 是后续所有请求签名的基础。
    *   服务器返回的数据是使用 `AES-GCM` 加密的，客户端需要用 `clientSecret` 派生出的密钥进行解密。

2.  **生成签名 (Signature)**：
    *   对于每一个需要授权的请求，客户端会根据以下参数生成一个签名字符串：
        *   请求方法 (e.g., `POST`)
        *   请求路径 (e.g., `/api/images/upload`)
        *   当前时间戳
        *   一个随机字符串 (Nonce)
        *   请求的业务参数 (Body/Query Parameters)
    *   将上述参数按固定规则拼接。
    *   使用从服务器获取的 `ticket` 作为HMAC的密钥，对拼接好的字符串进行 `HMAC-SHA256` 计算，得到最终的签名。

3.  **发送请求**：
    *   客户端在请求头 (Header) 中附带以下信息：
        *   `X-Client-ID`: 会话ID (`sid`)。
        *   `X-Timestamp`: 时间戳。
        *   `X-Trace-ID`: 随机字符串 (Nonce)，用于追踪和防重放。
        *   `X-Auth-Token`: 计算出的签名。

QA在测试时，如果遇到 `401 Unauthorized` 或 `403 Forbidden` 错误，大概率是签名计算错误或 `ticket` 已过期。

#### **3. API 接口详解**

服务器基地址 (Base URL): `https://all-pdf-editor.apusai.com`

---

##### **3.1 创建会话**

*   **Endpoint**: `POST /api/native/session`
*   **描述**: App初始化时调用，用于建立一个安全的通信会话，并获取用于签名的 `ticket`。
*   **请求 (Request)**:
    *   **Headers**:
        *   `Content-Type: application/json`
    *   **Body (JSON)**:
        ```json
        {
          "credential": "YOUR_CLIENT_SECRET" 
        }
        ```
*   **响应 (Response)**:
    *   **成功 (200 OK)**:
        *   **Body (JSON)**:
            ```json
            {
              "success": true,
              "data": "<ENCRYPTED_STRING>" 
            }
            ```
        *   `data` 字段是一个经过 `AES-GCM` 加密的字符串，解密后包含 `sid` (会话ID), `ticket` (票据), 和 `ttl` (有效时间)。
    *   **失败**: 返回非200状态码，Body中可能包含错误信息。

---

##### **3.2 上传并处理图像**

*   **Endpoint**: `POST /api/images/upload`
*   **描述**: 核心功能接口。上传一张或多张图片，服务器会自动进行边缘检测、裁剪和画质增强等处理。
*   **请求 (Request)**:
    *   **类型**: `multipart/form-data`
    *   **Headers**:
        *   `X-Client-ID`: (必需) 会话ID
        *   `X-Timestamp`: (必需) 请求时间戳
        *   `X-Trace-ID`: (必需) 随机字符串 (Nonce)
        *   `X-Auth-Token`: (必需) HMAC签名
    *   **Body (Form Data)**:
        *   `files`: 图片文件数组。字段名就是 `files`。
*   **响应 (Response)**:
    *   **成功 (200 OK)**:
        *   **Body (JSON)**:
            ```json
            {
              "success": true,
              "data": {
                "images": [
                  {
                    "id": "server-generated-id-1",
                    "cropped": "<BASE64_ENCODED_CROPPED_IMAGE_DATA>",
                    "original": "<BASE64_ENCODED_ORIGINAL_IMAGE_DATA>",
                    "enhancement": "document",
                    "confidence": 0.95,
                    "corners": [[x1, y1], [x2, y2], [x3, y3], [x4, y4]]
                  }
                ]
              }
            }
            ```
        *   `cropped`: 经过裁剪和增强后的图片（Base64编码）。
        *   `original`: 原始图片（Base64编码），作为备用。
        *   `confidence`: 服务器对文档识别的置信度。
        *   `corners`: 检测到的文档四个角点坐标。
    *   **失败**: 返回非200状态码，Body中可能包含错误信息，如 `Image processing failed`。

---

##### **3.3 健康检查**

*   **Endpoint**: `GET /api/health`
*   **描述**: 一个无需认证的简单接口，用于检查服务器是否在线且可正常访问。
*   **请求 (Request)**:
    *   无特殊Headers或Body。
*   **响应 (Response)**:
    *   **成功 (200 OK)**:
        *   Body内容不重要，仅状态码`200`即表示服务健康。
    *   **失败**: 返回非200状态码或请求超时。

---

##### **3.4 [已废弃] 生成PDF**

*   **Endpoint**: `POST /api/pdf/generate`
*   **描述**: 此接口在代码中已被标记为`@Deprecated`。PDF的生成逻辑已迁移到客户端本地实现，**QA团队可以忽略此接口**。

---
