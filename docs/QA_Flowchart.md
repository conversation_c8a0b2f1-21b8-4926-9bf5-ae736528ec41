# 产品功能使用流程图 (QA)

本文档旨在为QA团队提供清晰、直观的产品核心功能测试流程。

```mermaid
graph TD
    A[开始] --> B(启动App);
    B --> C(闪屏页);
    C --> D{文档列表主页};

    D --> E[选择一个PDF文件];
    D --> F[搜索文件];
    D --> G[查看最近文件];
    D --> H[查看收藏文件];
    D --> I[进入设置页面];

    E --> J(PDF阅读器页面);
    J --> K[进入编辑模式];
    
    K --> L{编辑器页面};
    L --> M[裁剪页面];
    L --> N[签名管理];
    N --> O[创建新签名];
    L --> P[其他编辑功能, 如添加文本/图片等];
    
    M --> Q(保存或导出PDF);
    O --> N;
    P --> Q;

    I --> R{设置页面};
    R --> S[语言选择];
    R --> T[授权目录管理];
    R --> U[关于页面];

    Q --> V[结束];
```

## 流程节点说明

- **文档列表主页**: 应用的核心入口，展示所有PDF文件，并提供不同视图（最近、收藏）的切换。
- **PDF阅读器页面**: 用于查看PDF内容。
- **编辑器页面**: 核心编辑功能的集合点，可以从这里进入不同的编辑模块。
- **签名管理**: 用户可以在此添加或删除自己的签名。
- **设置页面**: 提供应用级的配置选项。

## 自动文件扫描流程

应用能够自动扫描用户设备以查找支持的文件类型。

- **支持的格式**: PDF, Word (`.doc`, `.docx`), Excel (`.xls`, `.xlsx`), PowerPoint (`.ppt`, `.pptx`)。
- **扫描时机**: 主要在应用启动时触发。
- **扫描间隔**: 扫描是基于事件（如应用启动）而不是固定的时间间隔，以优化设备性能和电池寿命。

```mermaid
graph TD
    subgraph App Launch Scan
        A[应用启动] --> B{检查文件访问权限};
        B -- 已授权 --> E[开始扫描];
        B -- 未授权 --> C[向用户请求权限];
        C --> D{用户是否授权?};
        D -- 是 --> E;
        D -- 否 --> F[扫描结束<br/>(显示无权限提示)];
        E --> G[在设备中查找支持的文件<br/>(PDF, Word, Excel, PPT)];
        G --> H[更新内部文件数据库];
        H --> I[在文档列表主页显示文件];
    end
```
