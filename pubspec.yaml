name: all_pdf_editor
description: "A beautiful document hub for PDFs, Word, Excel, and PowerPoint with search, sort, and quick PDF creation."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.8
  # State management
  provider: ^6.1.2
  # Permissions and platform utils
  permission_handler: ^12.0.1
  device_info_plus: ^11.1.1
  path: ^1.9.0
  path_provider: ^2.1.4
  # Android MediaStore access for document discovery
  media_store_plus: ^0.1.3
  # Open files with external apps
  open_filex: ^4.5.0
  # Share files
  share_plus: ^11.1.0
  # Pick images from gallery and camera
  image_picker: ^1.1.2
  file_picker: ^10.3.1
  # PDF creation
  pdf: ^3.11.1
  printing: ^5.13.4
  # Persistence for favorites/recents
  shared_preferences: ^2.3.3
  # Formatting
  intl: ^0.20.2
  # URL launching
  url_launcher: ^6.3.1
  # PDF viewing
  pdfx: ^2.7.0
  photo_view: ^0.15.0
  # PDF text extraction
  read_pdf_text: ^0.3.1
  # Syncfusion PDF viewer (for precise search/highlight test)
  syncfusion_flutter_pdfviewer: ^30.2.5
  # HTTP client for image processing service integration
  http: ^1.1.0
  # Cryptographic functions for HMAC and AES encryption
  crypto: ^3.0.3
  encrypt: ^5.0.1
  # MIME type detection for uploads
  mime: ^1.0.6
  http_parser: ^4.0.2
  # Enhanced WebView with screenshot capability for document viewing
  flutter_inappwebview: ^6.1.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true
  generate: true
  
  assets:
    - assets/images/

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/app_icon.png"
  remove_alpha_ios: true
